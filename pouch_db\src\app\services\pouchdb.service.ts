import { Injectable } from '@angular/core';
import * as PouchDB from 'pouchdb';


@Injectable({
  providedIn: 'root'
})
export class PouchdbService {
  private db: any;

  constructor() {
    this.db = new PouchDB('mydb'); // Create local PouchDB
  }

  // Add document
  addDoc(doc: any) {
    return this.db.post(doc);
  }

  // Get all documents
  getAllDocs() {
    return this.db.allDocs({ include_docs: true });
  }
}
