{"ast": null, "code": "import v35 from './v35.js';\nimport md5 from './md5.js';\nvar v3 = v35('v3', 0x30, md5);\nexport default v3;", "map": {"version": 3, "names": ["v35", "md5", "v3"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/uuid/dist/esm-browser/v3.js"], "sourcesContent": ["import v35 from './v35.js';\nimport md5 from './md5.js';\nvar v3 = v35('v3', 0x30, md5);\nexport default v3;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,GAAG,MAAM,UAAU;AAC1B,IAAIC,EAAE,GAAGF,GAAG,CAAC,IAAI,EAAE,IAAI,EAAEC,GAAG,CAAC;AAC7B,eAAeC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}