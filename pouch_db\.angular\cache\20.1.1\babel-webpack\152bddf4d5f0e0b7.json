{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/pouchdb.service\";\nimport * as i2 from \"@ionic/angular\";\nexport class HomePage {\n  constructor(pouchService) {\n    this.pouchService = pouchService;\n    this.formData = {\n      name: '',\n      email: ''\n    };\n    this.docs = [];\n    this.loadDocs();\n  }\n  onSubmit() {\n    this.pouchService.addDoc(this.formData).then(() => {\n      console.log('Document saved');\n      this.formData = {\n        name: '',\n        email: ''\n      }; // Clear form\n      this.loadDocs();\n    }).catch(err => console.error(err));\n  }\n  loadDocs() {\n    this.pouchService.getAllDocs().then(result => {\n      this.docs = result.rows;\n    }).catch(err => console.error(err));\n  }\n  static #_ = this.ɵfac = function HomePage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomePage)(i0.ɵɵdirectiveInject(i1.PouchdbService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomePage,\n    selectors: [[\"app-home\"]],\n    decls: 16,\n    vars: 2,\n    consts: [[3, \"translucent\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"id\", \"container\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", \"href\", \"https://ionicframework.com/docs/components\"]],\n    template: function HomePage_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\")(2, \"ion-title\");\n        i0.ɵɵtext(3, \" Blank \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(4, \"ion-content\", 1)(5, \"ion-header\", 2)(6, \"ion-toolbar\")(7, \"ion-title\", 3);\n        i0.ɵɵtext(8, \"Blank\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 4)(10, \"strong\");\n        i0.ɵɵtext(11, \"Ready to create an app?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"Start with Ionic \");\n        i0.ɵɵelementStart(14, \"a\", 5);\n        i0.ɵɵtext(15, \"UI Components\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"translucent\", true);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"fullscreen\", true);\n      }\n    },\n    dependencies: [i2.IonContent, i2.IonHeader, i2.IonTitle, i2.IonToolbar],\n    styles: [\"#container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 26px;\\n}\\n\\n#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  line-height: 22px;\\n  color: #8c8c8c;\\n  margin: 0;\\n}\\n\\n#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLnBhZ2Uuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uLy4uLy4uL1ZJQ1RVUyUyMFVTRVIvT25lRHJpdmUvRGVza3RvcC9GUk9OVEVORCUyMERFVkVMT1BFUi9wb3VjaF9kYi9zcmMvYXBwL2hvbWUvaG9tZS5wYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxrQkFBQTtFQUVBLGtCQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxRQUFBO0VBQ0EsMkJBQUE7QUNBRjs7QURHQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtBQ0FGOztBREdBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBRUEsY0FBQTtFQUVBLFNBQUE7QUNGRjs7QURLQTtFQUNFLHFCQUFBO0FDRkYiLCJzb3VyY2VzQ29udGVudCI6WyIjY29udGFpbmVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIHRvcDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG59XG5cbiNjb250YWluZXIgc3Ryb25nIHtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBsaW5lLWhlaWdodDogMjZweDtcbn1cblxuI2NvbnRhaW5lciBwIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBsaW5lLWhlaWdodDogMjJweDtcblxuICBjb2xvcjogIzhjOGM4YztcblxuICBtYXJnaW46IDA7XG59XG5cbiNjb250YWluZXIgYSB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn0iLCIjY29udGFpbmVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICB0b3A6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xufVxuXG4jY29udGFpbmVyIHN0cm9uZyB7XG4gIGZvbnQtc2l6ZTogMjBweDtcbiAgbGluZS1oZWlnaHQ6IDI2cHg7XG59XG5cbiNjb250YWluZXIgcCB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDIycHg7XG4gIGNvbG9yOiAjOGM4YzhjO1xuICBtYXJnaW46IDA7XG59XG5cbiNjb250YWluZXIgYSB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["HomePage", "constructor", "pouchService", "formData", "name", "email", "docs", "loadDocs", "onSubmit", "addDoc", "then", "console", "log", "catch", "err", "error", "getAllDocs", "result", "rows", "_", "i0", "ɵɵdirectiveInject", "i1", "PouchdbService", "_2", "selectors", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home.page.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { PouchdbService } from '../services/pouchdb.service';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: 'home.page.html',\n  styleUrls: ['home.page.scss'],\n})\nexport class HomePage {\n  formData = {\n    name: '',\n    email: ''\n  };\n\n  docs: any[] = [];\n\n  constructor(private pouchService: PouchdbService) {\n    this.loadDocs();\n  }\n\n  onSubmit() {\n    this.pouchService.addDoc(this.formData)\n      .then(() => {\n        console.log('Document saved');\n        this.formData = { name: '', email: '' }; // Clear form\n        this.loadDocs();\n      })\n      .catch((err: any) => console.error(err));\n  }\n\n  loadDocs() {\n    this.pouchService.getAllDocs()\n      .then((result: { rows: any[]; }) => {\n        this.docs = result.rows;\n      })\n      .catch((err: any) => console.error(err));\n  }\n}\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar>\n    <ion-title>\n      Blank\n    </ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\">\n  <ion-header collapse=\"condense\">\n    <ion-toolbar>\n      <ion-title size=\"large\">Blank</ion-title>\n    </ion-toolbar>\n  </ion-header>\n\n  <div id=\"container\">\n    <strong>Ready to create an app?</strong>\n    <p>Start with Ionic <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"https://ionicframework.com/docs/components\">UI Components</a></p>\n  </div>\n</ion-content>\n"], "mappings": ";;;AAQA,OAAM,MAAOA,QAAQ;EAQnBC,YAAoBC,YAA4B;IAA5B,KAAAA,YAAY,GAAZA,YAAY;IAPhC,KAAAC,QAAQ,GAAG;MACTC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE;KACR;IAED,KAAAC,IAAI,GAAU,EAAE;IAGd,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACN,YAAY,CAACO,MAAM,CAAC,IAAI,CAACN,QAAQ,CAAC,CACpCO,IAAI,CAAC,MAAK;MACTC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,IAAI,CAACT,QAAQ,GAAG;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAE,CAAC,CAAC;MACzC,IAAI,CAACE,QAAQ,EAAE;IACjB,CAAC,CAAC,CACDM,KAAK,CAAEC,GAAQ,IAAKH,OAAO,CAACI,KAAK,CAACD,GAAG,CAAC,CAAC;EAC5C;EAEAP,QAAQA,CAAA;IACN,IAAI,CAACL,YAAY,CAACc,UAAU,EAAE,CAC3BN,IAAI,CAAEO,MAAwB,IAAI;MACjC,IAAI,CAACX,IAAI,GAAGW,MAAM,CAACC,IAAI;IACzB,CAAC,CAAC,CACDL,KAAK,CAAEC,GAAQ,IAAKH,OAAO,CAACI,KAAK,CAACD,GAAG,CAAC,CAAC;EAC5C;EAAC,QAAAK,CAAA,G;qCA5BUnB,QAAQ,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAARxB,QAAQ;IAAAyB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNjBX,EAFJ,CAAAa,cAAA,oBAAiC,kBAClB,gBACA;QACTb,EAAA,CAAAc,MAAA,cACF;QAEJd,EAFI,CAAAe,YAAA,EAAY,EACA,EACH;QAKPf,EAHN,CAAAa,cAAA,qBAAiC,oBACC,kBACjB,mBACa;QAAAb,EAAA,CAAAc,MAAA,YAAK;QAEjCd,EAFiC,CAAAe,YAAA,EAAY,EAC7B,EACH;QAGXf,EADF,CAAAa,cAAA,aAAoB,cACV;QAAAb,EAAA,CAAAc,MAAA,+BAAuB;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACxCf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,yBAAiB;QAAAd,EAAA,CAAAa,cAAA,YAA+F;QAAAb,EAAA,CAAAc,MAAA,qBAAa;QAEpId,EAFoI,CAAAe,YAAA,EAAI,EAAI,EACpI,EACM;;;QAnBFf,EAAA,CAAAgB,UAAA,qBAAoB;QAQnBhB,EAAA,CAAAiB,SAAA,GAAmB;QAAnBjB,EAAA,CAAAgB,UAAA,oBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}