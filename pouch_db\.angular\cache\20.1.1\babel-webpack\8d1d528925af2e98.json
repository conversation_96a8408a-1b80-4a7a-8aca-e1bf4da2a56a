{"ast": null, "code": "import REGEX from './regex.js';\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;", "map": {"version": 3, "names": ["REGEX", "validate", "uuid", "test"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/uuid/dist/esm-browser/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAE9B,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAIF,KAAK,CAACG,IAAI,CAACD,IAAI,CAAC;AACrD;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}