{"ast": null, "code": "\"use strict\";\n\n/**\n * Prevents Angular change detection from\n * running with certain Web Component callbacks\n */\n// eslint-disable-next-line no-underscore-dangle\nwindow.__Zone_disable_customElements = true;", "map": {"version": 3, "names": ["window", "__Zone_disable_customElements"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\zone-flags.ts"], "sourcesContent": ["/**\n * Prevents Angular change detection from\n * running with certain Web Component callbacks\n */\n// eslint-disable-next-line no-underscore-dangle\n(window as any).__Zone_disable_customElements = true;\n"], "mappings": ";;AAAA;;;;AAIA;AACCA,MAAc,CAACC,6BAA6B,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}