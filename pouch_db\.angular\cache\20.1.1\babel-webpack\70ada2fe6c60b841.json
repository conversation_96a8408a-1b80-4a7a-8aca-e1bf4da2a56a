{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Injectable, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  _zone;\n  _plugins;\n  _eventNameToPlugin = new Map();\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static ɵfac = function EventManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: EventManager,\n    factory: EventManager.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  _doc;\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nclass SharedStylesHost {\n  doc;\n  appId;\n  nonce;\n  /**\n   * Provides usage information for active inline style content and associated HTML <style> elements.\n   * Embedded styles typically originate from the `styles` metadata of a rendered component.\n   */\n  inline = new Map();\n  /**\n   * Provides usage information for active external style URLs and the associated HTML <link> elements.\n   * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n   */\n  external = new Map();\n  /**\n   * Set of host DOM nodes that will have styles attached.\n   */\n  hosts = new Set();\n  constructor(doc, appId, nonce,\n  // Cannot remove it due to backward compatibility\n  // (it seems some TGP targets might be calling this constructor directly).\n  platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles, urls) {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n    urls?.forEach(value => this.addUsage(value, this.external, createLinkElement));\n  }\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles, urls) {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n    urls?.forEach(value => this.removeUsage(value, this.external));\n  }\n  addUsage(value, usages, creator) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n      });\n    }\n  }\n  removeUsage(value, usages) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n  ngOnDestroy() {\n    for (const [, {\n      elements\n    }] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode) {\n    this.hosts.add(hostNode);\n    // Add existing styles to new host\n    for (const [style, {\n      elements\n    }] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {\n      elements\n    }] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n  removeHost(hostNode) {\n    this.hosts.delete(hostNode);\n  }\n  addElement(host, element) {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n    // Add application identifier when on the server to support client-side reuse\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n  static ɵfac = function SharedStylesHost_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedStylesHost,\n    factory: SharedStylesHost.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nclass DomRendererFactory2 {\n  eventManager;\n  sharedStylesHost;\n  appId;\n  removeStylesOnCompDestroy;\n  doc;\n  platformId;\n  ngZone;\n  nonce;\n  tracingService;\n  rendererByCompId = new Map();\n  defaultRenderer;\n  platformIsServer;\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.tracingService = tracingService;\n    this.platformIsServer = typeof ngServerMode !== 'undefined' && ngServerMode;\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (typeof ngServerMode !== 'undefined' && ngServerMode && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    this.rendererByCompId.delete(componentId);\n  }\n  static ɵfac = function DomRendererFactory2_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(_TracingService, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomRendererFactory2,\n    factory: DomRendererFactory2.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }, {\n    type: i0.ɵTracingService,\n    decorators: [{\n      type: Inject,\n      args: [_TracingService]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  eventManager;\n  doc;\n  ngZone;\n  platformIsServer;\n  tracingService;\n  data = Object.create(null);\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  destroyNode = null;\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = _getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if (this.tracingService?.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = typeof ngServerMode !== 'undefined' && ngServerMode ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  hostEl;\n  shadowRoot;\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = component.getExternalStyles?.();\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  removeStylesOnCompDestroy;\n  styles;\n  styleUrls;\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = component.getExternalStyles?.(compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  contentAttr;\n  hostAttr;\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };", "map": {"version": 3, "names": ["DOCUMENT", "ɵgetDOM", "_getDOM", "i0", "InjectionToken", "ɵRuntimeError", "_RuntimeError", "Injectable", "Inject", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "ɵTracingService", "_TracingService", "RendererStyleFlags2", "EVENT_MANAGER_PLUGINS", "ngDevMode", "EventManager", "_zone", "_plugins", "_eventNameToPlugin", "Map", "constructor", "plugins", "for<PERSON>ach", "plugin", "manager", "slice", "reverse", "addEventListener", "element", "eventName", "handler", "options", "_findPluginFor", "getZone", "get", "find", "supports", "set", "ɵfac", "EventManager_Factory", "__ngFactoryType__", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "removeElements", "elements", "remove", "createStyleElement", "style", "doc", "styleElement", "createElement", "textContent", "addServerStyles", "appId", "inline", "external", "head", "querySelectorAll", "removeAttribute", "HTMLLinkElement", "href", "lastIndexOf", "usage", "createLinkElement", "url", "linkElement", "setAttribute", "SharedStylesHost", "nonce", "hosts", "Set", "platformId", "add", "addStyles", "styles", "urls", "value", "addUsage", "removeStyles", "removeUsage", "usages", "creator", "record", "map", "host", "addElement", "delete", "ngOnDestroy", "clear", "addHost", "hostNode", "push", "removeHost", "ngServerMode", "append<PERSON><PERSON><PERSON>", "SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "SOURCEMAP_URL_REGEXP", "PROTOCOL_REGEXP", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "addBaseHrefToCssSourceMap", "baseHref", "absoluteBaseHrefUrl", "URL", "cssContent", "includes", "_", "sourceMapUrl", "startsWith", "test", "pathname", "resolvedSourceMapUrl", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "tracingService", "rendererByCompId", "defaultRenderer", "platformIsServer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "ShadowDom<PERSON><PERSON><PERSON>", "componentReplaced", "componentId", "DomRendererFactory2_Factory", "Object", "data", "create", "throwOnSyntheticProps", "destroy", "destroyNode", "name", "namespace", "createElementNS", "createComment", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "_parent", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "el", "querySelector", "parentNode", "node", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "target", "event", "callback", "getGlobalEventTarget", "wrappedCallback", "decoratePreventDefault", "wrapEventListener", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "tagName", "hostEl", "shadowRoot", "component", "attachShadow", "mode", "getBaseHref", "styleEl", "document", "styleUrls", "getExternalStyles", "styleUrl", "linkEl", "nodeOrShadowRoot", "contentAttr", "hostAttr"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/platform-browser/fesm2022/dom_renderer.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Injectable, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    _zone;\n    _plugins;\n    _eventNameToPlugin = new Map();\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @param options Options that configure how the event listener is bound.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler, options) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler, options);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: EventManager });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }] });\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n    _doc;\n    // TODO: remove (has some usage in G3)\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    // Using non-null assertion because it's set by EventManager's constructor\n    manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n    for (const element of elements) {\n        element.remove();\n    }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n    const styleElement = doc.createElement('style');\n    styleElement.textContent = style;\n    return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n    const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n    if (elements) {\n        for (const styleElement of elements) {\n            styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (styleElement instanceof HTMLLinkElement) {\n                // Only use filename from href\n                // The href is build time generated with a unique value to prevent duplicates.\n                external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n                    usage: 0,\n                    elements: [styleElement],\n                });\n            }\n            else if (styleElement.textContent) {\n                inline.set(styleElement.textContent, { usage: 0, elements: [styleElement] });\n            }\n        }\n    }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n    const linkElement = doc.createElement('link');\n    linkElement.setAttribute('rel', 'stylesheet');\n    linkElement.setAttribute('href', url);\n    return linkElement;\n}\nclass SharedStylesHost {\n    doc;\n    appId;\n    nonce;\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    inline = new Map();\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    external = new Map();\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    hosts = new Set();\n    constructor(doc, appId, nonce, \n    // Cannot remove it due to backward compatibility\n    // (it seems some TGP targets might be calling this constructor directly).\n    platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        addServerStyles(doc, appId, this.inline, this.external);\n        this.hosts.add(doc.head);\n    }\n    /**\n     * Adds embedded styles to the DOM via HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    addStyles(styles, urls) {\n        for (const value of styles) {\n            this.addUsage(value, this.inline, createStyleElement);\n        }\n        urls?.forEach((value) => this.addUsage(value, this.external, createLinkElement));\n    }\n    /**\n     * Removes embedded styles from the DOM that were added as HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    removeStyles(styles, urls) {\n        for (const value of styles) {\n            this.removeUsage(value, this.inline);\n        }\n        urls?.forEach((value) => this.removeUsage(value, this.external));\n    }\n    addUsage(value, usages, creator) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If existing, just increment the usage count\n        if (record) {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n                // A usage count of zero indicates a preexisting server generated style.\n                // This attribute is solely used for debugging purposes of SSR style reuse.\n                record.elements.forEach((element) => element.setAttribute('ng-style-reused', ''));\n            }\n            record.usage++;\n        }\n        else {\n            // Otherwise, create an entry to track the elements and add element for each host\n            usages.set(value, {\n                usage: 1,\n                elements: [...this.hosts].map((host) => this.addElement(host, creator(value, this.doc))),\n            });\n        }\n    }\n    removeUsage(value, usages) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If there is a record, reduce the usage count and if no longer used,\n        // remove from DOM and delete usage record.\n        if (record) {\n            record.usage--;\n            if (record.usage <= 0) {\n                removeElements(record.elements);\n                usages.delete(value);\n            }\n        }\n    }\n    ngOnDestroy() {\n        for (const [, { elements }] of [...this.inline, ...this.external]) {\n            removeElements(elements);\n        }\n        this.hosts.clear();\n    }\n    /**\n     * Adds a host node to the set of style hosts and adds all existing style usage to\n     * the newly added host node.\n     *\n     * This is currently only used for Shadow DOM encapsulation mode.\n     */\n    addHost(hostNode) {\n        this.hosts.add(hostNode);\n        // Add existing styles to new host\n        for (const [style, { elements }] of this.inline) {\n            elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n        }\n        for (const [url, { elements }] of this.external) {\n            elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n        }\n    }\n    removeHost(hostNode) {\n        this.hosts.delete(hostNode);\n    }\n    addElement(host, element) {\n        // Add a nonce if present\n        if (this.nonce) {\n            element.setAttribute('nonce', this.nonce);\n        }\n        // Add application identifier when on the server to support client-side reuse\n        if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n            element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n        }\n        // Insert the element into the DOM with the host node as parent\n        return host.appendChild(element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: SharedStylesHost });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/Math/MathML',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n    if (!baseHref) {\n        return styles;\n    }\n    const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n    return styles.map((cssContent) => {\n        if (!cssContent.includes('sourceMappingURL=')) {\n            return cssContent;\n        }\n        return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n            if (sourceMapUrl[0] === '/' ||\n                sourceMapUrl.startsWith('data:') ||\n                PROTOCOL_REGEXP.test(sourceMapUrl)) {\n                return `/*# sourceMappingURL=${sourceMapUrl} */`;\n            }\n            const { pathname: resolvedSourceMapUrl } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n            return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n        });\n    });\n}\nclass DomRendererFactory2 {\n    eventManager;\n    sharedStylesHost;\n    appId;\n    removeStylesOnCompDestroy;\n    doc;\n    platformId;\n    ngZone;\n    nonce;\n    tracingService;\n    rendererByCompId = new Map();\n    defaultRenderer;\n    platformIsServer;\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.tracingService = tracingService;\n        this.platformIsServer = typeof ngServerMode !== 'undefined' && ngServerMode;\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (typeof ngServerMode !== 'undefined' &&\n            ngServerMode &&\n            type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            const tracingService = this.tracingService;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        this.rendererByCompId.delete(componentId);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }, { token: _TracingService, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomRendererFactory2 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }, { type: i0.ɵTracingService, decorators: [{\n                    type: Inject,\n                    args: [_TracingService]\n                }, {\n                    type: Optional\n                }] }] });\nclass DefaultDomRenderer2 {\n    eventManager;\n    doc;\n    ngZone;\n    platformIsServer;\n    tracingService;\n    data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    throwOnSyntheticProps = true;\n    constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.tracingService = tracingService;\n    }\n    destroy() { }\n    destroyNode = null;\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(_parent, oldChild) {\n        oldChild.remove();\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n        if (!el) {\n            throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback, options) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = _getDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        let wrappedCallback = this.decoratePreventDefault(callback);\n        if (this.tracingService?.wrapEventListener) {\n            wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n        }\n        return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = typeof ngServerMode !== 'undefined' && ngServerMode\n                ? this.ngZone.runGuarded(() => eventHandler(event))\n                : eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    hostEl;\n    shadowRoot;\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = _getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        styles = shimStylesContent(component.id, styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n        // Apply any external component styles to the shadow root for the component's element.\n        // The ShadowDOM renderer uses an alternative execution path for component styles that\n        // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n        // the manual addition of embedded styles directly above, any external stylesheets\n        // must be manually added here to ensure ShadowDOM components are correctly styled.\n        // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n        const styleUrls = component.getExternalStyles?.();\n        if (styleUrls) {\n            for (const styleUrl of styleUrls) {\n                const linkEl = createLinkElement(styleUrl, doc);\n                if (nonce) {\n                    linkEl.setAttribute('nonce', nonce);\n                }\n                this.shadowRoot.appendChild(linkEl);\n            }\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(_parent, oldChild) {\n        return super.removeChild(null, oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    removeStylesOnCompDestroy;\n    styles;\n    styleUrls;\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = _getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        this.styles = compId ? shimStylesContent(compId, styles) : styles;\n        this.styleUrls = component.getExternalStyles?.(compId);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    contentAttr;\n    hostAttr;\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,OAAO,IAAIC,OAAO,QAAQ,iBAAiB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,aAAa,IAAIC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,eAAe,IAAIC,eAAe,EAAEC,mBAAmB,QAAQ,eAAe;;AAExN;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAIb,cAAc,CAACc,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,KAAK;EACLC,QAAQ;EACRC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAEL,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClBK,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACP,QAAQ,GAAGI,OAAO,CAACI,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACnD,MAAMR,MAAM,GAAG,IAAI,CAACS,cAAc,CAACH,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACI,gBAAgB,CAACC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACIE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjB,KAAK;EACrB;EACA;EACAgB,cAAcA,CAACH,SAAS,EAAE;IACtB,IAAIN,MAAM,GAAG,IAAI,CAACL,kBAAkB,CAACgB,GAAG,CAACL,SAAS,CAAC;IACnD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMF,OAAO,GAAG,IAAI,CAACJ,QAAQ;IAC7BM,MAAM,GAAGF,OAAO,CAACc,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACa,QAAQ,CAACP,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACN,MAAM,EAAE;MACT,MAAM,IAAIrB,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,2CAA2Ce,SAAS,EAAE,CAAC;IAC/D;IACA,IAAI,CAACX,kBAAkB,CAACmB,GAAG,CAACR,SAAS,EAAEN,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;EACA,OAAOe,IAAI,YAAAC,qBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFzB,YAAY,EAAtBhB,EAAE,CAAA0C,QAAA,CAAsC5B,qBAAqB,GAA7Dd,EAAE,CAAA0C,QAAA,CAAwE1C,EAAE,CAAC2C,MAAM;EAAA;EAC5K,OAAOC,KAAK,kBAD6E5C,EAAE,CAAA6C,kBAAA;IAAAC,KAAA,EACY9B,YAAY;IAAA+B,OAAA,EAAZ/B,YAAY,CAAAuB;EAAA;AACvH;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAH6Ff,EAAE,CAAAgD,iBAAA,CAGJhC,YAAY,EAAc,CAAC;IAC1GiC,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAACtC,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEmC,IAAI,EAAEjD,EAAE,CAAC2C;EAAO,CAAC,CAAC;AAAA;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,kBAAkB,CAAC;EACrBC,IAAI;EACJ;EACAjC,WAAWA,CAACiC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;EACA7B,OAAO;AACX;;AAEA;AACA,MAAM8B,qBAAqB,GAAG,WAAW;AACzC;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC9B,KAAK,MAAM5B,OAAO,IAAI4B,QAAQ,EAAE;IAC5B5B,OAAO,CAAC6B,MAAM,CAAC,CAAC;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACpC,MAAMC,YAAY,GAAGD,GAAG,CAACE,aAAa,CAAC,OAAO,CAAC;EAC/CD,YAAY,CAACE,WAAW,GAAGJ,KAAK;EAChC,OAAOE,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACJ,GAAG,EAAEK,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACnD,MAAMX,QAAQ,GAAGI,GAAG,CAACQ,IAAI,EAAEC,gBAAgB,CAAC,SAASf,qBAAqB,KAAKW,KAAK,WAAWX,qBAAqB,KAAKW,KAAK,IAAI,CAAC;EACnI,IAAIT,QAAQ,EAAE;IACV,KAAK,MAAMK,YAAY,IAAIL,QAAQ,EAAE;MACjCK,YAAY,CAACS,eAAe,CAAChB,qBAAqB,CAAC;MACnD,IAAIO,YAAY,YAAYU,eAAe,EAAE;QACzC;QACA;QACAJ,QAAQ,CAAC9B,GAAG,CAACwB,YAAY,CAACW,IAAI,CAAC/C,KAAK,CAACoC,YAAY,CAACW,IAAI,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;UAC1EC,KAAK,EAAE,CAAC;UACRlB,QAAQ,EAAE,CAACK,YAAY;QAC3B,CAAC,CAAC;MACN,CAAC,MACI,IAAIA,YAAY,CAACE,WAAW,EAAE;QAC/BG,MAAM,CAAC7B,GAAG,CAACwB,YAAY,CAACE,WAAW,EAAE;UAAEW,KAAK,EAAE,CAAC;UAAElB,QAAQ,EAAE,CAACK,YAAY;QAAE,CAAC,CAAC;MAChF;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,iBAAiBA,CAACC,GAAG,EAAEhB,GAAG,EAAE;EACjC,MAAMiB,WAAW,GAAGjB,GAAG,CAACE,aAAa,CAAC,MAAM,CAAC;EAC7Ce,WAAW,CAACC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;EAC7CD,WAAW,CAACC,YAAY,CAAC,MAAM,EAAEF,GAAG,CAAC;EACrC,OAAOC,WAAW;AACtB;AACA,MAAME,gBAAgB,CAAC;EACnBnB,GAAG;EACHK,KAAK;EACLe,KAAK;EACL;AACJ;AACA;AACA;EACId,MAAM,GAAG,IAAI/C,GAAG,CAAC,CAAC;EAClB;AACJ;AACA;AACA;EACIgD,QAAQ,GAAG,IAAIhD,GAAG,CAAC,CAAC;EACpB;AACJ;AACA;EACI8D,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjB9D,WAAWA,CAACwC,GAAG,EAAEK,KAAK,EAAEe,KAAK;EAC7B;EACA;EACAG,UAAU,GAAG,CAAC,CAAC,EAAE;IACb,IAAI,CAACvB,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACe,KAAK,GAAGA,KAAK;IAClBhB,eAAe,CAACJ,GAAG,EAAEK,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;IACvD,IAAI,CAACc,KAAK,CAACG,GAAG,CAACxB,GAAG,CAACQ,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIiB,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACpB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACG,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACtB,MAAM,EAAER,kBAAkB,CAAC;IACzD;IACA6B,IAAI,EAAEjE,OAAO,CAAEkE,KAAK,IAAK,IAAI,CAACC,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACrB,QAAQ,EAAEQ,iBAAiB,CAAC,CAAC;EACpF;EACA;AACJ;AACA;AACA;EACIe,YAAYA,CAACJ,MAAM,EAAEC,IAAI,EAAE;IACvB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACK,WAAW,CAACH,KAAK,EAAE,IAAI,CAACtB,MAAM,CAAC;IACxC;IACAqB,IAAI,EAAEjE,OAAO,CAAEkE,KAAK,IAAK,IAAI,CAACG,WAAW,CAACH,KAAK,EAAE,IAAI,CAACrB,QAAQ,CAAC,CAAC;EACpE;EACAsB,QAAQA,CAACD,KAAK,EAAEI,MAAM,EAAEC,OAAO,EAAE;IAC7B;IACA,MAAMC,MAAM,GAAGF,MAAM,CAAC1D,GAAG,CAACsD,KAAK,CAAC;IAChC;IACA,IAAIM,MAAM,EAAE;MACR,IAAI,CAAC,OAAOhF,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKgF,MAAM,CAACpB,KAAK,KAAK,CAAC,EAAE;QACvE;QACA;QACAoB,MAAM,CAACtC,QAAQ,CAAClC,OAAO,CAAEM,OAAO,IAAKA,OAAO,CAACkD,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;MACrF;MACAgB,MAAM,CAACpB,KAAK,EAAE;IAClB,CAAC,MACI;MACD;MACAkB,MAAM,CAACvD,GAAG,CAACmD,KAAK,EAAE;QACdd,KAAK,EAAE,CAAC;QACRlB,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACyB,KAAK,CAAC,CAACc,GAAG,CAAEC,IAAI,IAAK,IAAI,CAACC,UAAU,CAACD,IAAI,EAAEH,OAAO,CAACL,KAAK,EAAE,IAAI,CAAC5B,GAAG,CAAC,CAAC;MAC3F,CAAC,CAAC;IACN;EACJ;EACA+B,WAAWA,CAACH,KAAK,EAAEI,MAAM,EAAE;IACvB;IACA,MAAME,MAAM,GAAGF,MAAM,CAAC1D,GAAG,CAACsD,KAAK,CAAC;IAChC;IACA;IACA,IAAIM,MAAM,EAAE;MACRA,MAAM,CAACpB,KAAK,EAAE;MACd,IAAIoB,MAAM,CAACpB,KAAK,IAAI,CAAC,EAAE;QACnBnB,cAAc,CAACuC,MAAM,CAACtC,QAAQ,CAAC;QAC/BoC,MAAM,CAACM,MAAM,CAACV,KAAK,CAAC;MACxB;IACJ;EACJ;EACAW,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAG;MAAE3C;IAAS,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAACU,MAAM,EAAE,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAE;MAC/DZ,cAAc,CAACC,QAAQ,CAAC;IAC5B;IACA,IAAI,CAACyB,KAAK,CAACmB,KAAK,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAACrB,KAAK,CAACG,GAAG,CAACkB,QAAQ,CAAC;IACxB;IACA,KAAK,MAAM,CAAC3C,KAAK,EAAE;MAAEH;IAAS,CAAC,CAAC,IAAI,IAAI,CAACU,MAAM,EAAE;MAC7CV,QAAQ,CAAC+C,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,QAAQ,EAAE5C,kBAAkB,CAACC,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,KAAK,MAAM,CAACgB,GAAG,EAAE;MAAEpB;IAAS,CAAC,CAAC,IAAI,IAAI,CAACW,QAAQ,EAAE;MAC7CX,QAAQ,CAAC+C,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,QAAQ,EAAE3B,iBAAiB,CAACC,GAAG,EAAE,IAAI,CAAChB,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA4C,UAAUA,CAACF,QAAQ,EAAE;IACjB,IAAI,CAACrB,KAAK,CAACiB,MAAM,CAACI,QAAQ,CAAC;EAC/B;EACAL,UAAUA,CAACD,IAAI,EAAEpE,OAAO,EAAE;IACtB;IACA,IAAI,IAAI,CAACoD,KAAK,EAAE;MACZpD,OAAO,CAACkD,YAAY,CAAC,OAAO,EAAE,IAAI,CAACE,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,OAAOyB,YAAY,KAAK,WAAW,IAAIA,YAAY,EAAE;MACrD7E,OAAO,CAACkD,YAAY,CAACxB,qBAAqB,EAAE,IAAI,CAACW,KAAK,CAAC;IAC3D;IACA;IACA,OAAO+B,IAAI,CAACU,WAAW,CAAC9E,OAAO,CAAC;EACpC;EACA,OAAOU,IAAI,YAAAqE,yBAAAnE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuC,gBAAgB,EA9M1BhF,EAAE,CAAA0C,QAAA,CA8M0C7C,QAAQ,GA9MpDG,EAAE,CAAA0C,QAAA,CA8M+DpC,MAAM,GA9MvEN,EAAE,CAAA0C,QAAA,CA8MkFnC,SAAS,MA9M7FP,EAAE,CAAA0C,QAAA,CA8MwHlC,WAAW;EAAA;EAC9N,OAAOoC,KAAK,kBA/M6E5C,EAAE,CAAA6C,kBAAA;IAAAC,KAAA,EA+MYkC,gBAAgB;IAAAjC,OAAA,EAAhBiC,gBAAgB,CAAAzC;EAAA;AAC3H;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAjN6Ff,EAAE,CAAAgD,iBAAA,CAiNJgC,gBAAgB,EAAc,CAAC;IAC9G/B,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAE4D,QAAQ;IAAE1D,UAAU,EAAE,CAAC;MAC9CF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAACvD,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoD,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC9C,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE2C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC7C,SAAS;IACpB,CAAC,EAAE;MACC0C,IAAI,EAAExC;IACV,CAAC;EAAE,CAAC,EAAE;IAAEwC,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC5C,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMsG,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,oBAAoB,GAAG,uCAAuC;AACpE,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAG,WAAWD,kBAAkB,EAAE;AACjD,MAAME,YAAY,GAAG,cAAcF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIrH,cAAc,CAACc,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;EACxGwG,UAAU,EAAE,MAAM;EAClBxE,OAAO,EAAEA,CAAA,KAAMsE;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEtC,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACS,GAAG,CAAE8B,CAAC,IAAKA,CAAC,CAACJ,OAAO,CAACX,eAAe,EAAEc,MAAM,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,yBAAyBA,CAACC,QAAQ,EAAEzC,MAAM,EAAE;EACjD,IAAI,CAACyC,QAAQ,EAAE;IACX,OAAOzC,MAAM;EACjB;EACA,MAAM0C,mBAAmB,GAAG,IAAIC,GAAG,CAACF,QAAQ,EAAE,kBAAkB,CAAC;EACjE,OAAOzC,MAAM,CAACS,GAAG,CAAEmC,UAAU,IAAK;IAC9B,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC3C,OAAOD,UAAU;IACrB;IACA,OAAOA,UAAU,CAACT,OAAO,CAACV,oBAAoB,EAAE,CAACqB,CAAC,EAAEC,YAAY,KAAK;MACjE,IAAIA,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IACvBA,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,IAChCtB,eAAe,CAACuB,IAAI,CAACF,YAAY,CAAC,EAAE;QACpC,OAAO,wBAAwBA,YAAY,KAAK;MACpD;MACA,MAAM;QAAEG,QAAQ,EAAEC;MAAqB,CAAC,GAAG,IAAIR,GAAG,CAACI,YAAY,EAAEL,mBAAmB,CAAC;MACrF,OAAO,wBAAwBS,oBAAoB,KAAK;IAC5D,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,MAAMC,mBAAmB,CAAC;EACtBC,YAAY;EACZC,gBAAgB;EAChB3E,KAAK;EACL4E,yBAAyB;EACzBjF,GAAG;EACHuB,UAAU;EACV2D,MAAM;EACN9D,KAAK;EACL+D,cAAc;EACdC,gBAAgB,GAAG,IAAI7H,GAAG,CAAC,CAAC;EAC5B8H,eAAe;EACfC,gBAAgB;EAChB9H,WAAWA,CAACuH,YAAY,EAAEC,gBAAgB,EAAE3E,KAAK,EAAE4E,yBAAyB,EAAEjF,GAAG,EAAEuB,UAAU,EAAE2D,MAAM,EAAE9D,KAAK,GAAG,IAAI,EAAE+D,cAAc,GAAG,IAAI,EAAE;IACxI,IAAI,CAACJ,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC3E,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4E,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACjF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACuB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC2D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC9D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+D,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACG,gBAAgB,GAAG,OAAOzC,YAAY,KAAK,WAAW,IAAIA,YAAY;IAC3E,IAAI,CAACwC,eAAe,GAAG,IAAIE,mBAAmB,CAACR,YAAY,EAAE/E,GAAG,EAAEkF,MAAM,EAAE,IAAI,CAACI,gBAAgB,EAAE,IAAI,CAACH,cAAc,CAAC;EACzH;EACAK,cAAcA,CAACxH,OAAO,EAAEoB,IAAI,EAAE;IAC1B,IAAI,CAACpB,OAAO,IAAI,CAACoB,IAAI,EAAE;MACnB,OAAO,IAAI,CAACiG,eAAe;IAC/B;IACA,IAAI,OAAOxC,YAAY,KAAK,WAAW,IACnCA,YAAY,IACZzD,IAAI,CAACqG,aAAa,KAAK5I,iBAAiB,CAAC6I,SAAS,EAAE;MACpD;MACAtG,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEqG,aAAa,EAAE5I,iBAAiB,CAAC8I;MAAS,CAAC;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAC7H,OAAO,EAAEoB,IAAI,CAAC;IACxD;IACA;IACA,IAAIwG,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAC/H,OAAO,CAAC;IACjC,CAAC,MACI,IAAI4H,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAC7H,OAAO,EAAEoB,IAAI,EAAE;IAC/B,MAAMgG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIQ,QAAQ,GAAGR,gBAAgB,CAAC9G,GAAG,CAACc,IAAI,CAAC8G,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAM5F,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMkF,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMK,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMH,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,QAAQ/F,IAAI,CAACqG,aAAa;QACtB,KAAK5I,iBAAiB,CAAC8I,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACf,YAAY,EAAEC,gBAAgB,EAAE5F,IAAI,EAAE,IAAI,CAACiB,KAAK,EAAE4E,yBAAyB,EAAEjF,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,CAAC;UAC5K;QACJ,KAAKtI,iBAAiB,CAAC6I,SAAS;UAC5B,OAAO,IAAIS,iBAAiB,CAACpB,YAAY,EAAEC,gBAAgB,EAAEhH,OAAO,EAAEoB,IAAI,EAAEY,GAAG,EAAEkF,MAAM,EAAE,IAAI,CAAC9D,KAAK,EAAEkE,gBAAgB,EAAEH,cAAc,CAAC;QAC1I;UACIS,QAAQ,GAAG,IAAII,4BAA4B,CAACjB,YAAY,EAAEC,gBAAgB,EAAE5F,IAAI,EAAE6F,yBAAyB,EAAEjF,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,CAAC;UAC3J;MACR;MACAC,gBAAgB,CAAC3G,GAAG,CAACW,IAAI,CAAC8G,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACArD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,gBAAgB,CAAC5C,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACI4D,iBAAiBA,CAACC,WAAW,EAAE;IAC3B,IAAI,CAACjB,gBAAgB,CAAC9C,MAAM,CAAC+D,WAAW,CAAC;EAC7C;EACA,OAAO3H,IAAI,YAAA4H,4BAAA1H,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkG,mBAAmB,EAjY7B3I,EAAE,CAAA0C,QAAA,CAiY6C1B,YAAY,GAjY3DhB,EAAE,CAAA0C,QAAA,CAiYsEsC,gBAAgB,GAjYxFhF,EAAE,CAAA0C,QAAA,CAiYmGpC,MAAM,GAjY3GN,EAAE,CAAA0C,QAAA,CAiYsH4E,kCAAkC,GAjY1JtH,EAAE,CAAA0C,QAAA,CAiYqK7C,QAAQ,GAjY/KG,EAAE,CAAA0C,QAAA,CAiY0LlC,WAAW,GAjYvMR,EAAE,CAAA0C,QAAA,CAiYkN1C,EAAE,CAAC2C,MAAM,GAjY7N3C,EAAE,CAAA0C,QAAA,CAiYwOnC,SAAS,GAjYnPP,EAAE,CAAA0C,QAAA,CAiY8P9B,eAAe;EAAA;EACxW,OAAOgC,KAAK,kBAlY6E5C,EAAE,CAAA6C,kBAAA;IAAAC,KAAA,EAkYY6F,mBAAmB;IAAA5F,OAAA,EAAnB4F,mBAAmB,CAAApG;EAAA;AAC9H;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KApY6Ff,EAAE,CAAAgD,iBAAA,CAoYJ2F,mBAAmB,EAAc,CAAC;IACjH1F,IAAI,EAAE7C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6C,IAAI,EAAEjC;EAAa,CAAC,EAAE;IAAEiC,IAAI,EAAE+B;EAAiB,CAAC,EAAE;IAAE/B,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnGF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC9C,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE2C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAACkE,kCAAkC;IAC7C,CAAC;EAAE,CAAC,EAAE;IAAErE,IAAI,EAAE4D,QAAQ;IAAE1D,UAAU,EAAE,CAAC;MACjCF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAACvD,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoD,IAAI,EAAEmH,MAAM;IAAEjH,UAAU,EAAE,CAAC;MAC/BF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC5C,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEyC,IAAI,EAAEjD,EAAE,CAAC2C;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAAC7C,SAAS;IACpB,CAAC;EAAE,CAAC,EAAE;IAAE0C,IAAI,EAAEjD,EAAE,CAACW,eAAe;IAAEwC,UAAU,EAAE,CAAC;MAC3CF,IAAI,EAAE5C,MAAM;MACZ+C,IAAI,EAAE,CAACxC,eAAe;IAC1B,CAAC,EAAE;MACCqC,IAAI,EAAExC;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAM2I,mBAAmB,CAAC;EACtBR,YAAY;EACZ/E,GAAG;EACHkF,MAAM;EACNI,gBAAgB;EAChBH,cAAc;EACdqB,IAAI,GAAGD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;EAC1B;AACJ;AACA;AACA;EACIC,qBAAqB,GAAG,IAAI;EAC5BlJ,WAAWA,CAACuH,YAAY,EAAE/E,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,EAAE;IACrE,IAAI,CAACJ,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC/E,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACH,cAAc,GAAGA,cAAc;EACxC;EACAwB,OAAOA,CAAA,EAAG,CAAE;EACZC,WAAW,GAAG,IAAI;EAClB1G,aAAaA,CAAC2G,IAAI,EAAEC,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC9G,GAAG,CAAC+G,eAAe,CAAC9D,cAAc,CAAC6D,SAAS,CAAC,IAAIA,SAAS,EAAED,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC7G,GAAG,CAACE,aAAa,CAAC2G,IAAI,CAAC;EACvC;EACAG,aAAaA,CAACpF,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC5B,GAAG,CAACgH,aAAa,CAACpF,KAAK,CAAC;EACxC;EACAqF,UAAUA,CAACrF,KAAK,EAAE;IACd,OAAO,IAAI,CAAC5B,GAAG,CAACkH,cAAc,CAACtF,KAAK,CAAC;EACzC;EACAkB,WAAWA,CAACqE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACvE,WAAW,CAACsE,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3BA,QAAQ,CAAC/H,MAAM,CAAC,CAAC;EACrB;EACAgI,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAIC,EAAE,GAAG,OAAOF,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC9H,GAAG,CAACiI,aAAa,CAACH,cAAc,CAAC,GAAGA,cAAc;IACrG,IAAI,CAACE,EAAE,EAAE;MACL,MAAM,IAAI1L,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACpH,iBAAiB4K,cAAc,8BAA8B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBC,EAAE,CAAC7H,WAAW,GAAG,EAAE;IACvB;IACA,OAAO6H,EAAE;EACb;EACAE,UAAUA,CAACC,IAAI,EAAE;IACb,OAAOA,IAAI,CAACD,UAAU;EAC1B;EACAE,WAAWA,CAACD,IAAI,EAAE;IACd,OAAOA,IAAI,CAACC,WAAW;EAC3B;EACAlH,YAAYA,CAAC8G,EAAE,EAAEnB,IAAI,EAAEjF,KAAK,EAAEkF,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXD,IAAI,GAAGC,SAAS,GAAG,GAAG,GAAGD,IAAI;MAC7B,MAAMwB,YAAY,GAAGpF,cAAc,CAAC6D,SAAS,CAAC;MAC9C,IAAIuB,YAAY,EAAE;QACdL,EAAE,CAACM,cAAc,CAACD,YAAY,EAAExB,IAAI,EAAEjF,KAAK,CAAC;MAChD,CAAC,MACI;QACDoG,EAAE,CAAC9G,YAAY,CAAC2F,IAAI,EAAEjF,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDoG,EAAE,CAAC9G,YAAY,CAAC2F,IAAI,EAAEjF,KAAK,CAAC;IAChC;EACJ;EACAlB,eAAeA,CAACsH,EAAE,EAAEnB,IAAI,EAAEC,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMuB,YAAY,GAAGpF,cAAc,CAAC6D,SAAS,CAAC;MAC9C,IAAIuB,YAAY,EAAE;QACdL,EAAE,CAACO,iBAAiB,CAACF,YAAY,EAAExB,IAAI,CAAC;MAC5C,CAAC,MACI;QACDmB,EAAE,CAACtH,eAAe,CAAC,GAAGoG,SAAS,IAAID,IAAI,EAAE,CAAC;MAC9C;IACJ,CAAC,MACI;MACDmB,EAAE,CAACtH,eAAe,CAACmG,IAAI,CAAC;IAC5B;EACJ;EACA2B,QAAQA,CAACR,EAAE,EAAEnB,IAAI,EAAE;IACfmB,EAAE,CAACS,SAAS,CAACjH,GAAG,CAACqF,IAAI,CAAC;EAC1B;EACA6B,WAAWA,CAACV,EAAE,EAAEnB,IAAI,EAAE;IAClBmB,EAAE,CAACS,SAAS,CAAC5I,MAAM,CAACgH,IAAI,CAAC;EAC7B;EACA8B,QAAQA,CAACX,EAAE,EAAEjI,KAAK,EAAE6B,KAAK,EAAEgH,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAI5L,mBAAmB,CAAC6L,QAAQ,GAAG7L,mBAAmB,CAAC8L,SAAS,CAAC,EAAE;MACxEd,EAAE,CAACjI,KAAK,CAACgJ,WAAW,CAAChJ,KAAK,EAAE6B,KAAK,EAAEgH,KAAK,GAAG5L,mBAAmB,CAAC8L,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDd,EAAE,CAACjI,KAAK,CAACA,KAAK,CAAC,GAAG6B,KAAK;IAC3B;EACJ;EACAoH,WAAWA,CAAChB,EAAE,EAAEjI,KAAK,EAAE6I,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAG5L,mBAAmB,CAAC6L,QAAQ,EAAE;MACtC;MACAb,EAAE,CAACjI,KAAK,CAACkJ,cAAc,CAAClJ,KAAK,CAAC;IAClC,CAAC,MACI;MACDiI,EAAE,CAACjI,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAgJ,WAAWA,CAACf,EAAE,EAAEnB,IAAI,EAAEjF,KAAK,EAAE;IACzB,IAAIoG,EAAE,IAAI,IAAI,EAAE;MACZ;IACJ;IACA,CAAC,OAAO9K,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACwJ,qBAAqB,IAC1BwC,oBAAoB,CAACrC,IAAI,EAAE,UAAU,CAAC;IAC1CmB,EAAE,CAACnB,IAAI,CAAC,GAAGjF,KAAK;EACpB;EACAuH,QAAQA,CAAChB,IAAI,EAAEvG,KAAK,EAAE;IAClBuG,IAAI,CAACiB,SAAS,GAAGxH,KAAK;EAC1B;EACAyH,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAErL,OAAO,EAAE;IACrC,CAAC,OAAOjB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACwJ,qBAAqB,IAC1BwC,oBAAoB,CAACK,KAAK,EAAE,UAAU,CAAC;IAC3C,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGpN,OAAO,CAAC,CAAC,CAACuN,oBAAoB,CAAC,IAAI,CAACzJ,GAAG,EAAEsJ,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAIhN,aAAa,CAAC,IAAI,CAAC,iDAAiD,CAAC,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,KACxH,4BAA4BoM,MAAM,cAAcC,KAAK,EAAE,CAAC;MAChE;IACJ;IACA,IAAIG,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAACH,QAAQ,CAAC;IAC3D,IAAI,IAAI,CAACrE,cAAc,EAAEyE,iBAAiB,EAAE;MACxCF,eAAe,GAAG,IAAI,CAACvE,cAAc,CAACyE,iBAAiB,CAACN,MAAM,EAAEC,KAAK,EAAEG,eAAe,CAAC;IAC3F;IACA,OAAO,IAAI,CAAC3E,YAAY,CAAChH,gBAAgB,CAACuL,MAAM,EAAEC,KAAK,EAAEG,eAAe,EAAEvL,OAAO,CAAC;EACtF;EACAwL,sBAAsBA,CAACE,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQN,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOM,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,OAAOjH,YAAY,KAAK,WAAW,IAAIA,YAAY,GAC1E,IAAI,CAACqC,MAAM,CAAC6E,UAAU,CAAC,MAAMF,YAAY,CAACN,KAAK,CAAC,CAAC,GACjDM,YAAY,CAACN,KAAK,CAAC;MACzB,IAAIO,oBAAoB,KAAK,KAAK,EAAE;QAChCP,KAAK,CAACS,cAAc,CAAC,CAAC;MAC1B;MACA,OAAO3K,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAM4K,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAAShB,oBAAoBA,CAACrC,IAAI,EAAEsD,QAAQ,EAAE;EAC1C,IAAItD,IAAI,CAACqD,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAI3N,aAAa,CAAC,IAAI,CAAC,sDAAsD,wBAAwB6N,QAAQ,IAAItD,IAAI;AACnI;AACA,+DAA+DA,IAAI,iIAAiI,CAAC;EACjM;AACJ;AACA,SAASS,cAAcA,CAACa,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACiC,OAAO,KAAK,UAAU,IAAIjC,IAAI,CAACZ,OAAO,KAAKlI,SAAS;AACpE;AACA,MAAM8G,iBAAiB,SAASZ,mBAAmB,CAAC;EAChDP,gBAAgB;EAChBqF,MAAM;EACNC,UAAU;EACV9M,WAAWA,CAACuH,YAAY,EAAEC,gBAAgB,EAAEqF,MAAM,EAAEE,SAAS,EAAEvK,GAAG,EAAEkF,MAAM,EAAE9D,KAAK,EAAEkE,gBAAgB,EAAEH,cAAc,EAAE;IACjH,KAAK,CAACJ,YAAY,EAAE/E,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,CAAC;IAClE,IAAI,CAACH,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACqF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGD,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAACzF,gBAAgB,CAACvC,OAAO,CAAC,IAAI,CAAC6H,UAAU,CAAC;IAC9C,IAAI5I,MAAM,GAAG6I,SAAS,CAAC7I,MAAM;IAC7B,IAAIxE,SAAS,EAAE;MACX;MACA,MAAMiH,QAAQ,GAAGjI,OAAO,CAAC,CAAC,CAACwO,WAAW,CAAC1K,GAAG,CAAC,IAAI,EAAE;MACjD0B,MAAM,GAAGwC,yBAAyB,CAACC,QAAQ,EAAEzC,MAAM,CAAC;IACxD;IACAA,MAAM,GAAGqC,iBAAiB,CAACwG,SAAS,CAACrE,EAAE,EAAExE,MAAM,CAAC;IAChD,KAAK,MAAM3B,KAAK,IAAI2B,MAAM,EAAE;MACxB,MAAMiJ,OAAO,GAAGC,QAAQ,CAAC1K,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAIkB,KAAK,EAAE;QACPuJ,OAAO,CAACzJ,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;MACxC;MACAuJ,OAAO,CAACxK,WAAW,GAAGJ,KAAK;MAC3B,IAAI,CAACuK,UAAU,CAACxH,WAAW,CAAC6H,OAAO,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAME,SAAS,GAAGN,SAAS,CAACO,iBAAiB,GAAG,CAAC;IACjD,IAAID,SAAS,EAAE;MACX,KAAK,MAAME,QAAQ,IAAIF,SAAS,EAAE;QAC9B,MAAMG,MAAM,GAAGjK,iBAAiB,CAACgK,QAAQ,EAAE/K,GAAG,CAAC;QAC/C,IAAIoB,KAAK,EAAE;UACP4J,MAAM,CAAC9J,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;QACvC;QACA,IAAI,CAACkJ,UAAU,CAACxH,WAAW,CAACkI,MAAM,CAAC;MACvC;IACJ;EACJ;EACAC,gBAAgBA,CAAC9C,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAACkC,MAAM,GAAG,IAAI,CAACC,UAAU,GAAGnC,IAAI;EACxD;EACArF,WAAWA,CAACqE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACtE,WAAW,CAAC,IAAI,CAACmI,gBAAgB,CAAC9D,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACyD,gBAAgB,CAAC9D,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3B,OAAO,KAAK,CAACF,WAAW,CAAC,IAAI,EAAEE,QAAQ,CAAC;EAC5C;EACAM,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAAC8C,gBAAgB,CAAC,KAAK,CAAC/C,UAAU,CAAC,IAAI,CAAC+C,gBAAgB,CAAC9C,IAAI,CAAC,CAAC,CAAC;EAC/E;EACAxB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,gBAAgB,CAACpC,UAAU,CAAC,IAAI,CAAC0H,UAAU,CAAC;EACrD;AACJ;AACA,MAAMtE,4BAA4B,SAAST,mBAAmB,CAAC;EAC3DP,gBAAgB;EAChBC,yBAAyB;EACzBvD,MAAM;EACNmJ,SAAS;EACTrN,WAAWA,CAACuH,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAEtF,yBAAyB,EAAEjF,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,EAAEnB,MAAM,EAAE;IACrI,KAAK,CAACe,YAAY,EAAE/E,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,CAAC;IAClE,IAAI,CAACH,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAIvD,MAAM,GAAG6I,SAAS,CAAC7I,MAAM;IAC7B,IAAIxE,SAAS,EAAE;MACX;MACA,MAAMiH,QAAQ,GAAGjI,OAAO,CAAC,CAAC,CAACwO,WAAW,CAAC1K,GAAG,CAAC,IAAI,EAAE;MACjD0B,MAAM,GAAGwC,yBAAyB,CAACC,QAAQ,EAAEzC,MAAM,CAAC;IACxD;IACA,IAAI,CAACA,MAAM,GAAGsC,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAEtC,MAAM,CAAC,GAAGA,MAAM;IACjE,IAAI,CAACmJ,SAAS,GAAGN,SAAS,CAACO,iBAAiB,GAAG9G,MAAM,CAAC;EAC1D;EACAiC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,gBAAgB,CAACvD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACmJ,SAAS,CAAC;EAChE;EACAlE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC1B,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAClD,YAAY,CAAC,IAAI,CAACJ,MAAM,EAAE,IAAI,CAACmJ,SAAS,CAAC;EACnE;AACJ;AACA,MAAM/E,iCAAiC,SAASE,4BAA4B,CAAC;EACzEkF,WAAW;EACXC,QAAQ;EACR3N,WAAWA,CAACuH,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAElK,KAAK,EAAE4E,yBAAyB,EAAEjF,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,EAAE;IACpI,MAAMnB,MAAM,GAAG3D,KAAK,GAAG,GAAG,GAAGkK,SAAS,CAACrE,EAAE;IACzC,KAAK,CAACnB,YAAY,EAAEC,gBAAgB,EAAEuF,SAAS,EAAEtF,yBAAyB,EAAEjF,GAAG,EAAEkF,MAAM,EAAEI,gBAAgB,EAAEH,cAAc,EAAEnB,MAAM,CAAC;IAClI,IAAI,CAACkH,WAAW,GAAGvH,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACmH,QAAQ,GAAGrH,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACA+B,WAAWA,CAAC/H,OAAO,EAAE;IACjB,IAAI,CAACiI,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC/E,YAAY,CAAClD,OAAO,EAAE,IAAI,CAACmN,QAAQ,EAAE,EAAE,CAAC;EACjD;EACAjL,aAAaA,CAACiH,MAAM,EAAEN,IAAI,EAAE;IACxB,MAAMmB,EAAE,GAAG,KAAK,CAAC9H,aAAa,CAACiH,MAAM,EAAEN,IAAI,CAAC;IAC5C,KAAK,CAAC3F,YAAY,CAAC8G,EAAE,EAAE,IAAI,CAACkD,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOlD,EAAE;EACb;AACJ;AAEA,SAASlD,mBAAmB,EAAE7H,qBAAqB,EAAEE,YAAY,EAAEqC,kBAAkB,EAAEiE,kCAAkC,EAAEtC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}