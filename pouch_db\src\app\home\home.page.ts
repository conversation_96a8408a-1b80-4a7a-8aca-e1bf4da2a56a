import { Component } from '@angular/core';
import { PouchdbService } from '../services/pouchdb.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage {
  formData = {
    name: '',
    email: ''
  };

  docs: any[] = [];

  constructor(private pouchService: PouchdbService) {
    this.loadDocs();
  }

  onSubmit() {
    this.pouchService.addDoc(this.formData)
      .then(() => {
        console.log('Document saved');
        this.formData = { name: '', email: '' }; // Clear form
        this.loadDocs();
      })
      .catch((err: any) => console.error(err));
  }

  loadDocs() {
    this.pouchService.getAllDocs()
      .then((result: { rows: any[]; }) => {
        this.docs = result.rows;
      })
      .catch((err: any) => console.error(err));
  }
}
