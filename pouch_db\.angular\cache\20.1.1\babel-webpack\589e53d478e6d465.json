{"ast": null, "code": "import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { connect } from './connect';\nexport function publish(selector) {\n  return selector ? source => connect(selector)(source) : source => multicast(new Subject())(source);\n}", "map": {"version": 3, "names": ["Subject", "multicast", "connect", "publish", "selector", "source"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/rxjs/dist/esm/internal/operators/publish.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { connect } from './connect';\nexport function publish(selector) {\n    return selector ? (source) => connect(selector)(source) : (source) => multicast(new Subject())(source);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,GAAIC,MAAM,IAAKH,OAAO,CAACE,QAAQ,CAAC,CAACC,MAAM,CAAC,GAAIA,MAAM,IAAKJ,SAAS,CAAC,IAAID,OAAO,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}