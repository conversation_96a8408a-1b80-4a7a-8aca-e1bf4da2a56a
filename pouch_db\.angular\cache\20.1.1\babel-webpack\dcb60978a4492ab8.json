{"ast": null, "code": "import * as PouchDB from 'pouchdb';\nimport * as i0 from \"@angular/core\";\nexport class PouchdbService {\n  constructor() {\n    this.db = new PouchDB('mydb'); // Create local PouchDB\n  }\n  // Add document\n  addDoc(doc) {\n    return this.db.post(doc);\n  }\n  // Get all documents\n  getAllDocs() {\n    return this.db.allDocs({\n      include_docs: true\n    });\n  }\n  static #_ = this.ɵfac = function PouchdbService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PouchdbService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PouchdbService,\n    factory: PouchdbService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["PouchDB", "PouchdbService", "constructor", "db", "addDoc", "doc", "post", "getAllDocs", "allDocs", "include_docs", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\services\\pouchdb.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as PouchDB from 'pouchdb';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PouchdbService {\r\n  private db: any;\r\n\r\n  constructor() {\r\n    this.db = new PouchDB('mydb'); // Create local PouchDB\r\n  }\r\n\r\n  // Add document\r\n  addDoc(doc: any) {\r\n    return this.db.post(doc);\r\n  }\r\n\r\n  // Get all documents\r\n  getAllDocs() {\r\n    return this.db.allDocs({ include_docs: true });\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAO,KAAKA,OAAO,MAAM,SAAS;;AAMlC,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IACE,IAAI,CAACC,EAAE,GAAG,IAAIH,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;EACjC;EAEA;EACAI,MAAMA,CAACC,GAAQ;IACb,OAAO,IAAI,CAACF,EAAE,CAACG,IAAI,CAACD,GAAG,CAAC;EAC1B;EAEA;EACAE,UAAUA,CAAA;IACR,OAAO,IAAI,CAACJ,EAAE,CAACK,OAAO,CAAC;MAAEC,YAAY,EAAE;IAAI,CAAE,CAAC;EAChD;EAAC,QAAAC,CAAA,G;qCAfUT,cAAc;EAAA;EAAA,QAAAU,EAAA,G;WAAdV,cAAc;IAAAW,OAAA,EAAdX,cAAc,CAAAY,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}