{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as getIonMode, h, j as Host, k as getElement, n as forceUpdate, m as printIonWarning, o as printIonError } from './index-B_U9CtaY.js';\nimport { r as raf, f as addEventListener, m as removeEventListener, b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nconst segmentIosCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}\";\nconst segmentMdCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}\";\nconst Segment = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionSelect = createEvent(this, \"ionSelect\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.segmentViewEl = null;\n    this.activated = false;\n    /**\n     * If `true`, the user cannot interact with the segment.\n     */\n    this.disabled = false;\n    /**\n     * If `true`, the segment buttons will overflow and the user can swipe to see them.\n     * In addition, this will disable the gesture to drag the indicator between the buttons\n     * in order to swipe to see hidden buttons.\n     */\n    this.scrollable = false;\n    /**\n     * If `true`, users will be able to swipe between segment buttons to activate them.\n     */\n    this.swipeGesture = true;\n    /**\n     * If `true`, navigating to an `ion-segment-button` with the keyboard will focus and select the element.\n     * If `false`, keyboard navigation will only focus the `ion-segment-button` element.\n     */\n    this.selectOnFocus = false;\n    this.onClick = ev => {\n      const current = ev.target;\n      const previous = this.checked;\n      // If the current element is a segment then that means\n      // the user tried to swipe to a segment button and\n      // click a segment button at the same time so we should\n      // not update the checked segment button\n      if (current.tagName === 'ION-SEGMENT') {\n        return;\n      }\n      this.value = current.value;\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n      if (this.segmentViewEl) {\n        this.updateSegmentView();\n        if (this.scrollable && previous) {\n          this.checkButton(previous, current);\n        }\n      } else if (this.scrollable || !this.swipeGesture) {\n        if (previous) {\n          this.checkButton(previous, current);\n        } else {\n          this.setCheckedClasses();\n        }\n      }\n    };\n    this.onSlottedItemsChange = () => {\n      /**\n       * When the slotted segment buttons change we need to\n       * ensure that the new segment buttons are checked if\n       * the value matches the segment button value.\n       */\n      this.valueChanged(this.value);\n    };\n    this.getSegmentButton = selector => {\n      var _a, _b;\n      const buttons = this.getButtons().filter(button => !button.disabled);\n      const currIndex = buttons.findIndex(button => button === document.activeElement);\n      switch (selector) {\n        case 'first':\n          return buttons[0];\n        case 'last':\n          return buttons[buttons.length - 1];\n        case 'next':\n          return (_a = buttons[currIndex + 1]) !== null && _a !== void 0 ? _a : buttons[0];\n        case 'previous':\n          return (_b = buttons[currIndex - 1]) !== null && _b !== void 0 ? _b : buttons[buttons.length - 1];\n        default:\n          return null;\n      }\n    };\n  }\n  colorChanged(value, oldValue) {\n    /**\n     * If color is set after not having\n     * previously been set (or vice versa),\n     * we need to emit style so the segment-buttons\n     * can apply their color classes properly.\n     */\n    if (oldValue === undefined && value !== undefined || oldValue !== undefined && value === undefined) {\n      this.emitStyle();\n    }\n  }\n  swipeGestureChanged() {\n    this.gestureChanged();\n  }\n  valueChanged(value, oldValue) {\n    // Force a value to exist if we're using a segment view\n    if (this.segmentViewEl && value === undefined) {\n      this.value = this.getButtons()[0].value;\n      return;\n    }\n    if (oldValue !== undefined && value !== undefined) {\n      const buttons = this.getButtons();\n      const previous = buttons.find(button => button.value === oldValue);\n      const current = buttons.find(button => button.value === value);\n      if (previous && current) {\n        if (!this.segmentViewEl) {\n          this.checkButton(previous, current);\n        } else if (this.triggerScrollOnValueChange !== false) {\n          this.updateSegmentView();\n        }\n      }\n    } else if (value !== undefined && oldValue === undefined && this.segmentViewEl) {\n      this.updateSegmentView();\n    }\n    /**\n     * `ionSelect` is emitted every time the value changes (internal or external changes).\n     * Used by `ion-segment-button` to determine if the button should be checked.\n     */\n    this.ionSelect.emit({\n      value\n    });\n    // The scroll listener should handle scrolling the active button into view as needed\n    if (!this.segmentViewEl) {\n      this.scrollActiveButtonIntoView();\n    }\n    this.triggerScrollOnValueChange = undefined;\n  }\n  disabledChanged() {\n    this.gestureChanged();\n    if (!this.segmentViewEl) {\n      const buttons = this.getButtons();\n      for (const button of buttons) {\n        button.disabled = this.disabled;\n      }\n    } else {\n      this.segmentViewEl.disabled = this.disabled;\n    }\n  }\n  gestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.scrollable && !this.disabled && this.swipeGesture);\n    }\n  }\n  connectedCallback() {\n    this.emitStyle();\n    this.segmentViewEl = this.getSegmentView();\n  }\n  disconnectedCallback() {\n    this.segmentViewEl = null;\n  }\n  componentWillLoad() {\n    this.emitStyle();\n  }\n  componentDidLoad() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.segmentViewEl = _this.getSegmentView();\n      _this.setCheckedClasses();\n      /**\n       * We need to wait for the buttons to all be rendered\n       * before we can scroll.\n       */\n      raf(() => {\n        /**\n         * When the segment loads for the first\n         * time we just want to snap the active button into\n         * place instead of scroll. Smooth scrolling should only\n         * happen when the user interacts with the segment.\n         */\n        _this.scrollActiveButtonIntoView(false);\n      });\n      _this.gesture = (yield import('./index-CfgBF1SE.js')).createGesture({\n        el: _this.el,\n        gestureName: 'segment',\n        gesturePriority: 100,\n        threshold: 0,\n        passive: false,\n        onStart: ev => _this.onStart(ev),\n        onMove: ev => _this.onMove(ev),\n        onEnd: ev => _this.onEnd(ev)\n      });\n      _this.gestureChanged();\n      if (_this.disabled) {\n        _this.disabledChanged();\n      }\n      // Update segment view based on the initial value,\n      // but do not animate the scroll\n      _this.updateSegmentView(false);\n    })();\n  }\n  onStart(detail) {\n    this.valueBeforeGesture = this.value;\n    this.activate(detail);\n  }\n  onMove(detail) {\n    this.setNextIndex(detail);\n  }\n  onEnd(detail) {\n    this.setActivated(false);\n    this.setNextIndex(detail, true);\n    detail.event.stopImmediatePropagation();\n    const value = this.value;\n    if (value !== undefined) {\n      if (this.valueBeforeGesture !== value) {\n        this.emitValueChange();\n        this.updateSegmentView();\n      }\n    }\n    this.valueBeforeGesture = undefined;\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange() {\n    const {\n      value\n    } = this;\n    this.ionChange.emit({\n      value\n    });\n  }\n  getButtons() {\n    return Array.from(this.el.querySelectorAll('ion-segment-button'));\n  }\n  get checked() {\n    return this.getButtons().find(button => button.value === this.value);\n  }\n  /*\n   * Activate both the segment and the buttons\n   * due to a bug with ::slotted in Safari\n   */\n  setActivated(activated) {\n    const buttons = this.getButtons();\n    buttons.forEach(button => {\n      button.classList.toggle('segment-button-activated', activated);\n    });\n    this.activated = activated;\n  }\n  activate(detail) {\n    const clicked = detail.event.target;\n    const buttons = this.getButtons();\n    const checked = buttons.find(button => button.value === this.value);\n    // Make sure we are only checking for activation on a segment button\n    // since disabled buttons will get the click on the segment\n    if (clicked.tagName !== 'ION-SEGMENT-BUTTON') {\n      return;\n    }\n    // If there are no checked buttons, set the current button to checked\n    if (!checked) {\n      this.value = clicked.value;\n      this.setCheckedClasses();\n    }\n    // If the gesture began on the clicked button with the indicator\n    // then we should activate the indicator\n    if (this.value === clicked.value) {\n      this.setActivated(true);\n    }\n  }\n  getIndicator(button) {\n    const root = button.shadowRoot || button;\n    return root.querySelector('.segment-button-indicator');\n  }\n  checkButton(previous, current) {\n    const previousIndicator = this.getIndicator(previous);\n    const currentIndicator = this.getIndicator(current);\n    if (previousIndicator === null || currentIndicator === null) {\n      return;\n    }\n    const previousClientRect = previousIndicator.getBoundingClientRect();\n    const currentClientRect = currentIndicator.getBoundingClientRect();\n    const widthDelta = previousClientRect.width / currentClientRect.width;\n    const xPosition = previousClientRect.left - currentClientRect.left;\n    // Scale the indicator width to match the previous indicator width\n    // and translate it on top of the previous indicator\n    const transform = `translate3d(${xPosition}px, 0, 0) scaleX(${widthDelta})`;\n    writeTask(() => {\n      // Remove the transition before positioning on top of the previous indicator\n      currentIndicator.classList.remove('segment-button-indicator-animated');\n      currentIndicator.style.setProperty('transform', transform);\n      // Force a repaint to ensure the transform happens\n      currentIndicator.getBoundingClientRect();\n      // Add the transition to move the indicator into place\n      currentIndicator.classList.add('segment-button-indicator-animated');\n      // Remove the transform to slide the indicator back to the button clicked\n      currentIndicator.style.setProperty('transform', '');\n      this.scrollActiveButtonIntoView(true);\n    });\n    this.value = current.value;\n    this.setCheckedClasses();\n  }\n  setCheckedClasses() {\n    const buttons = this.getButtons();\n    const index = buttons.findIndex(button => button.value === this.value);\n    const next = index + 1;\n    for (const button of buttons) {\n      button.classList.remove('segment-button-after-checked');\n    }\n    if (next < buttons.length) {\n      buttons[next].classList.add('segment-button-after-checked');\n    }\n  }\n  getSegmentView() {\n    const buttons = this.getButtons();\n    // Get the first button with a contentId\n    const firstContentId = buttons.find(button => button.contentId);\n    // Get the segment content with an id matching the button's contentId\n    const segmentContent = document.querySelector(`ion-segment-content[id=\"${firstContentId === null || firstContentId === void 0 ? void 0 : firstContentId.contentId}\"]`);\n    // Return the segment view for that matching segment content\n    return segmentContent === null || segmentContent === void 0 ? void 0 : segmentContent.closest('ion-segment-view');\n  }\n  handleSegmentViewScroll(ev) {\n    const {\n      scrollRatio,\n      isManualScroll\n    } = ev.detail;\n    if (!isManualScroll) {\n      return;\n    }\n    const dispatchedFrom = ev.target;\n    const segmentViewEl = this.segmentViewEl;\n    const segmentEl = this.el;\n    // Only update the indicator if the event was dispatched from the correct segment view\n    if (ev.composedPath().includes(segmentViewEl) || (dispatchedFrom === null || dispatchedFrom === void 0 ? void 0 : dispatchedFrom.contains(segmentEl))) {\n      const buttons = this.getButtons();\n      // If no buttons are found or there is no value set then do nothing\n      if (!buttons.length) return;\n      const index = buttons.findIndex(button => button.value === this.value);\n      const current = buttons[index];\n      const nextIndex = Math.round(scrollRatio * (buttons.length - 1));\n      if (this.lastNextIndex === undefined || this.lastNextIndex !== nextIndex) {\n        this.lastNextIndex = nextIndex;\n        this.triggerScrollOnValueChange = false;\n        this.checkButton(current, buttons[nextIndex]);\n        this.emitValueChange();\n      }\n    }\n  }\n  /**\n   * Finds the related segment view and sets its current content\n   * based on the selected segment button. This method\n   * should be called on initial load of the segment,\n   * after the gesture is completed (if dragging between segments)\n   * and when a segment button is clicked directly.\n   */\n  updateSegmentView(smoothScroll = true) {\n    const buttons = this.getButtons();\n    const button = buttons.find(btn => btn.value === this.value);\n    // If the button does not have a contentId then there is\n    // no associated segment view to update\n    if (!(button === null || button === void 0 ? void 0 : button.contentId)) {\n      return;\n    }\n    const segmentView = this.segmentViewEl;\n    if (segmentView) {\n      segmentView.setContent(button.contentId, smoothScroll);\n    }\n  }\n  scrollActiveButtonIntoView(smoothScroll = true) {\n    const {\n      scrollable,\n      value,\n      el\n    } = this;\n    if (scrollable) {\n      const buttons = this.getButtons();\n      const activeButton = buttons.find(button => button.value === value);\n      if (activeButton !== undefined) {\n        const scrollContainerBox = el.getBoundingClientRect();\n        const activeButtonBox = activeButton.getBoundingClientRect();\n        /**\n         * Subtract the active button x position from the scroll\n         * container x position. This will give us the x position\n         * of the active button within the scroll container.\n         */\n        const activeButtonLeft = activeButtonBox.x - scrollContainerBox.x;\n        /**\n         * If we just used activeButtonLeft, then the active button\n         * would be aligned with the left edge of the scroll container.\n         * Instead, we want the segment button to be centered. As a result,\n         * we subtract half of the scroll container width. This will position\n         * the left edge of the active button at the midpoint of the scroll container.\n         * We then add half of the active button width. This will position the active\n         * button such that the midpoint of the active button is at the midpoint of the\n         * scroll container.\n         */\n        const centeredX = activeButtonLeft - scrollContainerBox.width / 2 + activeButtonBox.width / 2;\n        /**\n         * newScrollPosition is the absolute scroll position that the\n         * container needs to move to in order to center the active button.\n         * It is calculated by adding the current scroll position\n         * (scrollLeft) to the offset needed to center the button\n         * (centeredX).\n         */\n        const newScrollPosition = el.scrollLeft + centeredX;\n        /**\n         * We intentionally use scrollTo here instead of scrollIntoView\n         * to avoid a WebKit bug where accelerated animations break\n         * when using scrollIntoView. Using scrollIntoView will cause the\n         * segment container to jump during the transition and then snap into place.\n         * This is because scrollIntoView can potentially cause parent element\n         * containers to also scroll. scrollTo does not have this same behavior, so\n         * we use this API instead.\n         *\n         * scrollTo is used instead of scrollBy because there is a\n         * Webkit bug that causes scrollBy to not work smoothly when\n         * the active button is near the edge of the scroll container.\n         * This leads to the buttons to jump around during the transition.\n         *\n         * Note that if there is not enough scrolling space to center the element\n         * within the scroll container, the browser will attempt\n         * to center by as much as it can.\n         */\n        el.scrollTo({\n          top: 0,\n          left: newScrollPosition,\n          behavior: smoothScroll ? 'smooth' : 'instant'\n        });\n      }\n    }\n  }\n  setNextIndex(detail, isEnd = false) {\n    const rtl = isRTL(this.el);\n    const activated = this.activated;\n    const buttons = this.getButtons();\n    const index = buttons.findIndex(button => button.value === this.value);\n    const previous = buttons[index];\n    let current;\n    let nextIndex;\n    if (index === -1) {\n      return;\n    }\n    // Get the element that the touch event started on in case\n    // it was the checked button, then we will move the indicator\n    const rect = previous.getBoundingClientRect();\n    const left = rect.left;\n    const width = rect.width;\n    // Get the element that the gesture is on top of based on the currentX of the\n    // gesture event and the Y coordinate of the starting element, since the gesture\n    // can move up and down off of the segment\n    const currentX = detail.currentX;\n    const previousY = rect.top + rect.height / 2;\n    /**\n     * Segment can be used inside the shadow dom\n     * so doing document.elementFromPoint would never\n     * return a segment button in that instance.\n     * We use getRootNode to which will return the parent\n     * shadow root if used inside a shadow component and\n     * returns document otherwise.\n     */\n    const root = this.el.getRootNode();\n    const nextEl = root.elementFromPoint(currentX, previousY);\n    const decreaseIndex = rtl ? currentX > left + width : currentX < left;\n    const increaseIndex = rtl ? currentX < left : currentX > left + width;\n    // If the indicator is currently activated then we have started the gesture\n    // on top of the checked button so we need to slide the indicator\n    // by checking the button next to it as we move\n    if (activated && !isEnd) {\n      // Decrease index, move left in LTR & right in RTL\n      if (decreaseIndex) {\n        const newIndex = index - 1;\n        if (newIndex >= 0) {\n          nextIndex = newIndex;\n        }\n        // Increase index, moves right in LTR & left in RTL\n      } else if (increaseIndex) {\n        if (activated && !isEnd) {\n          const newIndex = index + 1;\n          if (newIndex < buttons.length) {\n            nextIndex = newIndex;\n          }\n        }\n      }\n      if (nextIndex !== undefined && !buttons[nextIndex].disabled) {\n        current = buttons[nextIndex];\n      }\n    }\n    // If the indicator is not activated then we will just set the indicator\n    // to the element where the gesture ended\n    if (!activated && isEnd) {\n      current = nextEl;\n    }\n    if (current != null) {\n      /**\n       * If current element is ion-segment then that means\n       * user tried to select a disabled ion-segment-button,\n       * and we should not update the ripple.\n       */\n      if (current.tagName === 'ION-SEGMENT') {\n        return false;\n      }\n      if (previous !== current) {\n        this.checkButton(previous, current);\n      }\n    }\n    return true;\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      segment: true\n    });\n  }\n  onKeyDown(ev) {\n    const rtl = isRTL(this.el);\n    let keyDownSelectsButton = this.selectOnFocus;\n    let current;\n    switch (ev.key) {\n      case 'ArrowRight':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('previous') : this.getSegmentButton('next');\n        break;\n      case 'ArrowLeft':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('next') : this.getSegmentButton('previous');\n        break;\n      case 'Home':\n        ev.preventDefault();\n        current = this.getSegmentButton('first');\n        break;\n      case 'End':\n        ev.preventDefault();\n        current = this.getSegmentButton('last');\n        break;\n      case ' ':\n      case 'Enter':\n        ev.preventDefault();\n        current = document.activeElement;\n        keyDownSelectsButton = true;\n    }\n    if (!current) {\n      return;\n    }\n    if (keyDownSelectsButton) {\n      const previous = this.checked;\n      this.checkButton(previous || current, current);\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n    }\n    current.setFocus();\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'e67ed512739cf2cfe657b0c44ebc3dfb1e9fbb79',\n      role: \"tablist\",\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'segment-activated': this.activated,\n        'segment-disabled': this.disabled,\n        'segment-scrollable': this.scrollable\n      })\n    }, h(\"slot\", {\n      key: '804d8acfcb9abfeeee38244b015fbc5c36330b9e',\n      onSlotchange: this.onSlottedItemsChange\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"color\": [\"colorChanged\"],\n      \"swipeGesture\": [\"swipeGestureChanged\"],\n      \"value\": [\"valueChanged\"],\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nSegment.style = {\n  ios: segmentIosCss,\n  md: segmentMdCss\n};\nconst segmentButtonIosCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:\\\"\\\";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff))))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}\";\nconst segmentButtonMdCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #0054e9));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}\";\nlet ids = 0;\nconst SegmentButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.segmentEl = null;\n    this.inheritedAttributes = {};\n    this.checked = false;\n    /**\n     * If `true`, the user cannot interact with the segment button.\n     */\n    this.disabled = false;\n    /**\n     * Set the layout of the text and icon in the segment.\n     */\n    this.layout = 'icon-top';\n    /**\n     * The type of the button.\n     */\n    this.type = 'button';\n    /**\n     * The value of the segment button.\n     */\n    this.value = 'ion-sb-' + ids++;\n    this.updateStyle = () => {\n      forceUpdate(this);\n    };\n    this.updateState = () => {\n      const {\n        segmentEl\n      } = this;\n      if (segmentEl) {\n        this.checked = segmentEl.value === this.value;\n        if (segmentEl.disabled) {\n          this.disabled = true;\n        }\n      }\n    };\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    const segmentEl = this.segmentEl = this.el.closest('ion-segment');\n    if (segmentEl) {\n      this.updateState();\n      addEventListener(segmentEl, 'ionSelect', this.updateState);\n      addEventListener(segmentEl, 'ionStyle', this.updateStyle);\n    }\n    // Prevent buttons from being disabled when associated with segment content\n    if (this.contentId && this.disabled) {\n      printIonWarning(`[ion-segment-button] - Segment buttons cannot be disabled when associated with an <ion-segment-content>.`);\n      this.disabled = false;\n    }\n  }\n  disconnectedCallback() {\n    const segmentEl = this.segmentEl;\n    if (segmentEl) {\n      removeEventListener(segmentEl, 'ionSelect', this.updateState);\n      removeEventListener(segmentEl, 'ionStyle', this.updateStyle);\n      this.segmentEl = null;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n    // Return if there is no contentId defined\n    if (!this.contentId) return;\n    // Attempt to find the Segment Content by its contentId\n    const segmentContent = document.getElementById(this.contentId);\n    // If no associated Segment Content exists, log an error and return\n    if (!segmentContent) {\n      printIonError(`[ion-segment-button] - Unable to find Segment Content with id=\"${this.contentId}\".`);\n      return;\n    }\n    // Ensure the found element is a valid ION-SEGMENT-CONTENT\n    if (segmentContent.tagName !== 'ION-SEGMENT-CONTENT') {\n      printIonError(`[ion-segment-button] - Element with id=\"${this.contentId}\" is not an <ion-segment-content> element.`);\n      return;\n    }\n  }\n  get hasLabel() {\n    return !!this.el.querySelector('ion-label');\n  }\n  get hasIcon() {\n    return !!this.el.querySelector('ion-icon');\n  }\n  /**\n   * @internal\n   * Focuses the native <button> element\n   * inside of ion-segment-button.\n   */\n  setFocus() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        nativeEl\n      } = _this2;\n      if (nativeEl !== undefined) {\n        nativeEl.focus();\n      }\n    })();\n  }\n  render() {\n    const {\n      checked,\n      type,\n      disabled,\n      hasIcon,\n      hasLabel,\n      layout,\n      segmentEl\n    } = this;\n    const mode = getIonMode(this);\n    const hasSegmentColor = () => (segmentEl === null || segmentEl === void 0 ? void 0 : segmentEl.color) !== undefined;\n    return h(Host, {\n      key: '26cb7ee90455bcaa6416125802d7e5729fa05b5b',\n      class: {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'in-segment': hostContext('ion-segment', this.el),\n        'in-segment-color': hasSegmentColor(),\n        'segment-button-has-label': hasLabel,\n        'segment-button-has-icon': hasIcon,\n        'segment-button-has-label-only': hasLabel && !hasIcon,\n        'segment-button-has-icon-only': hasIcon && !hasLabel,\n        'segment-button-disabled': disabled,\n        'segment-button-checked': checked,\n        [`segment-button-layout-${layout}`]: true,\n        'ion-activatable': true,\n        'ion-activatable-instant': true,\n        'ion-focusable': true\n      }\n    }, h(\"button\", Object.assign({\n      key: '75add37f11c107d1e2cfdb154e08004e9579e863',\n      \"aria-selected\": checked ? 'true' : 'false',\n      role: \"tab\",\n      ref: el => this.nativeEl = el,\n      type: type,\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled\n    }, this.inheritedAttributes), h(\"span\", {\n      key: '8e720d2a3e304903685bf09d226a64e944d78a22',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: 'c8e7b3ebf8f03042a1001155643b585283c73c65'\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '3586ac317b8d82c92b0ccfbfae42f8778612321b'\n    })), h(\"div\", {\n      key: '9cf93957da9e8dc333c8b05327bb903385b1c5f4',\n      part: \"indicator\",\n      class: \"segment-button-indicator segment-button-indicator-animated\"\n    }, h(\"div\", {\n      key: 'd3b6f0b3860ec6896b46703f64ed1cc8c75612e3',\n      part: \"indicator-background\",\n      class: \"segment-button-indicator-background\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nSegmentButton.style = {\n  ios: segmentButtonIosCss,\n  md: segmentButtonMdCss\n};\nexport { Segment as ion_segment, SegmentButton as ion_segment_button };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "n", "forceUpdate", "m", "printIonWarning", "o", "printIonError", "raf", "f", "addEventListener", "removeEventListener", "b", "inheritAttributes", "i", "isRTL", "c", "createColorClasses", "hostContext", "segmentIosCss", "segmentMdCss", "Segment", "constructor", "hostRef", "ionChange", "ionSelect", "ionStyle", "segmentViewEl", "activated", "disabled", "scrollable", "swipeGesture", "selectOnFocus", "onClick", "ev", "current", "target", "previous", "checked", "tagName", "value", "emitValueChange", "updateSegmentView", "checkButton", "setCheckedClasses", "onSlottedItemsChange", "valueChanged", "getSegmentButton", "selector", "_a", "_b", "buttons", "getButtons", "filter", "button", "currIndex", "findIndex", "document", "activeElement", "length", "colorChanged", "oldValue", "undefined", "emitStyle", "swipeGestureChanged", "gestureChanged", "find", "triggerScrollOnValueChange", "emit", "scrollActiveButtonIntoView", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "getSegmentView", "disconnectedCallback", "componentWillLoad", "componentDidLoad", "_this", "_asyncToGenerator", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "passive", "onStart", "onMove", "onEnd", "detail", "valueBeforeGesture", "activate", "setNextIndex", "setActivated", "event", "stopImmediatePropagation", "Array", "from", "querySelectorAll", "for<PERSON>ach", "classList", "toggle", "clicked", "getIndicator", "root", "shadowRoot", "querySelector", "previousIndicator", "currentIndicator", "previousClientRect", "getBoundingClientRect", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "transform", "remove", "style", "setProperty", "add", "index", "next", "firstContentId", "contentId", "segmentContent", "closest", "handleSegmentViewScroll", "scrollRatio", "isManualScroll", "dispatchedFrom", "segmentEl", "<PERSON><PERSON><PERSON>", "includes", "contains", "nextIndex", "Math", "round", "lastNextIndex", "smoothScroll", "btn", "segmentView", "<PERSON><PERSON><PERSON><PERSON>", "activeButton", "scrollContainerBox", "activeButtonBox", "activeButtonLeft", "x", "centeredX", "newScrollPosition", "scrollLeft", "scrollTo", "top", "behavior", "isEnd", "rtl", "rect", "currentX", "previousY", "height", "getRootNode", "nextEl", "elementFromPoint", "decreaseIndex", "increaseIndex", "newIndex", "segment", "onKeyDown", "keyDownSelectsButton", "key", "preventDefault", "setFocus", "render", "mode", "role", "class", "color", "onSlotchange", "watchers", "ios", "md", "segmentButtonIosCss", "segmentButtonMdCss", "ids", "SegmentButton", "inheritedAttributes", "layout", "type", "updateStyle", "updateState", "Object", "assign", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "hasIcon", "_this2", "nativeEl", "focus", "hasSegmentColor", "ref", "part", "ion_segment", "ion_segment_button"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-segment_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as getIonMode, h, j as Host, k as getElement, n as forceUpdate, m as printIonWarning, o as printIonError } from './index-B_U9CtaY.js';\nimport { r as raf, f as addEventListener, m as removeEventListener, b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\n\nconst segmentIosCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}\";\n\nconst segmentMdCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}\";\n\nconst Segment = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionSelect = createEvent(this, \"ionSelect\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.segmentViewEl = null;\n        this.activated = false;\n        /**\n         * If `true`, the user cannot interact with the segment.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the segment buttons will overflow and the user can swipe to see them.\n         * In addition, this will disable the gesture to drag the indicator between the buttons\n         * in order to swipe to see hidden buttons.\n         */\n        this.scrollable = false;\n        /**\n         * If `true`, users will be able to swipe between segment buttons to activate them.\n         */\n        this.swipeGesture = true;\n        /**\n         * If `true`, navigating to an `ion-segment-button` with the keyboard will focus and select the element.\n         * If `false`, keyboard navigation will only focus the `ion-segment-button` element.\n         */\n        this.selectOnFocus = false;\n        this.onClick = (ev) => {\n            const current = ev.target;\n            const previous = this.checked;\n            // If the current element is a segment then that means\n            // the user tried to swipe to a segment button and\n            // click a segment button at the same time so we should\n            // not update the checked segment button\n            if (current.tagName === 'ION-SEGMENT') {\n                return;\n            }\n            this.value = current.value;\n            if (current !== previous) {\n                this.emitValueChange();\n            }\n            if (this.segmentViewEl) {\n                this.updateSegmentView();\n                if (this.scrollable && previous) {\n                    this.checkButton(previous, current);\n                }\n            }\n            else if (this.scrollable || !this.swipeGesture) {\n                if (previous) {\n                    this.checkButton(previous, current);\n                }\n                else {\n                    this.setCheckedClasses();\n                }\n            }\n        };\n        this.onSlottedItemsChange = () => {\n            /**\n             * When the slotted segment buttons change we need to\n             * ensure that the new segment buttons are checked if\n             * the value matches the segment button value.\n             */\n            this.valueChanged(this.value);\n        };\n        this.getSegmentButton = (selector) => {\n            var _a, _b;\n            const buttons = this.getButtons().filter((button) => !button.disabled);\n            const currIndex = buttons.findIndex((button) => button === document.activeElement);\n            switch (selector) {\n                case 'first':\n                    return buttons[0];\n                case 'last':\n                    return buttons[buttons.length - 1];\n                case 'next':\n                    return (_a = buttons[currIndex + 1]) !== null && _a !== void 0 ? _a : buttons[0];\n                case 'previous':\n                    return (_b = buttons[currIndex - 1]) !== null && _b !== void 0 ? _b : buttons[buttons.length - 1];\n                default:\n                    return null;\n            }\n        };\n    }\n    colorChanged(value, oldValue) {\n        /**\n         * If color is set after not having\n         * previously been set (or vice versa),\n         * we need to emit style so the segment-buttons\n         * can apply their color classes properly.\n         */\n        if ((oldValue === undefined && value !== undefined) || (oldValue !== undefined && value === undefined)) {\n            this.emitStyle();\n        }\n    }\n    swipeGestureChanged() {\n        this.gestureChanged();\n    }\n    valueChanged(value, oldValue) {\n        // Force a value to exist if we're using a segment view\n        if (this.segmentViewEl && value === undefined) {\n            this.value = this.getButtons()[0].value;\n            return;\n        }\n        if (oldValue !== undefined && value !== undefined) {\n            const buttons = this.getButtons();\n            const previous = buttons.find((button) => button.value === oldValue);\n            const current = buttons.find((button) => button.value === value);\n            if (previous && current) {\n                if (!this.segmentViewEl) {\n                    this.checkButton(previous, current);\n                }\n                else if (this.triggerScrollOnValueChange !== false) {\n                    this.updateSegmentView();\n                }\n            }\n        }\n        else if (value !== undefined && oldValue === undefined && this.segmentViewEl) {\n            this.updateSegmentView();\n        }\n        /**\n         * `ionSelect` is emitted every time the value changes (internal or external changes).\n         * Used by `ion-segment-button` to determine if the button should be checked.\n         */\n        this.ionSelect.emit({ value });\n        // The scroll listener should handle scrolling the active button into view as needed\n        if (!this.segmentViewEl) {\n            this.scrollActiveButtonIntoView();\n        }\n        this.triggerScrollOnValueChange = undefined;\n    }\n    disabledChanged() {\n        this.gestureChanged();\n        if (!this.segmentViewEl) {\n            const buttons = this.getButtons();\n            for (const button of buttons) {\n                button.disabled = this.disabled;\n            }\n        }\n        else {\n            this.segmentViewEl.disabled = this.disabled;\n        }\n    }\n    gestureChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.scrollable && !this.disabled && this.swipeGesture);\n        }\n    }\n    connectedCallback() {\n        this.emitStyle();\n        this.segmentViewEl = this.getSegmentView();\n    }\n    disconnectedCallback() {\n        this.segmentViewEl = null;\n    }\n    componentWillLoad() {\n        this.emitStyle();\n    }\n    async componentDidLoad() {\n        this.segmentViewEl = this.getSegmentView();\n        this.setCheckedClasses();\n        /**\n         * We need to wait for the buttons to all be rendered\n         * before we can scroll.\n         */\n        raf(() => {\n            /**\n             * When the segment loads for the first\n             * time we just want to snap the active button into\n             * place instead of scroll. Smooth scrolling should only\n             * happen when the user interacts with the segment.\n             */\n            this.scrollActiveButtonIntoView(false);\n        });\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el: this.el,\n            gestureName: 'segment',\n            gesturePriority: 100,\n            threshold: 0,\n            passive: false,\n            onStart: (ev) => this.onStart(ev),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.gestureChanged();\n        if (this.disabled) {\n            this.disabledChanged();\n        }\n        // Update segment view based on the initial value,\n        // but do not animate the scroll\n        this.updateSegmentView(false);\n    }\n    onStart(detail) {\n        this.valueBeforeGesture = this.value;\n        this.activate(detail);\n    }\n    onMove(detail) {\n        this.setNextIndex(detail);\n    }\n    onEnd(detail) {\n        this.setActivated(false);\n        this.setNextIndex(detail, true);\n        detail.event.stopImmediatePropagation();\n        const value = this.value;\n        if (value !== undefined) {\n            if (this.valueBeforeGesture !== value) {\n                this.emitValueChange();\n                this.updateSegmentView();\n            }\n        }\n        this.valueBeforeGesture = undefined;\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange() {\n        const { value } = this;\n        this.ionChange.emit({ value });\n    }\n    getButtons() {\n        return Array.from(this.el.querySelectorAll('ion-segment-button'));\n    }\n    get checked() {\n        return this.getButtons().find((button) => button.value === this.value);\n    }\n    /*\n     * Activate both the segment and the buttons\n     * due to a bug with ::slotted in Safari\n     */\n    setActivated(activated) {\n        const buttons = this.getButtons();\n        buttons.forEach((button) => {\n            button.classList.toggle('segment-button-activated', activated);\n        });\n        this.activated = activated;\n    }\n    activate(detail) {\n        const clicked = detail.event.target;\n        const buttons = this.getButtons();\n        const checked = buttons.find((button) => button.value === this.value);\n        // Make sure we are only checking for activation on a segment button\n        // since disabled buttons will get the click on the segment\n        if (clicked.tagName !== 'ION-SEGMENT-BUTTON') {\n            return;\n        }\n        // If there are no checked buttons, set the current button to checked\n        if (!checked) {\n            this.value = clicked.value;\n            this.setCheckedClasses();\n        }\n        // If the gesture began on the clicked button with the indicator\n        // then we should activate the indicator\n        if (this.value === clicked.value) {\n            this.setActivated(true);\n        }\n    }\n    getIndicator(button) {\n        const root = button.shadowRoot || button;\n        return root.querySelector('.segment-button-indicator');\n    }\n    checkButton(previous, current) {\n        const previousIndicator = this.getIndicator(previous);\n        const currentIndicator = this.getIndicator(current);\n        if (previousIndicator === null || currentIndicator === null) {\n            return;\n        }\n        const previousClientRect = previousIndicator.getBoundingClientRect();\n        const currentClientRect = currentIndicator.getBoundingClientRect();\n        const widthDelta = previousClientRect.width / currentClientRect.width;\n        const xPosition = previousClientRect.left - currentClientRect.left;\n        // Scale the indicator width to match the previous indicator width\n        // and translate it on top of the previous indicator\n        const transform = `translate3d(${xPosition}px, 0, 0) scaleX(${widthDelta})`;\n        writeTask(() => {\n            // Remove the transition before positioning on top of the previous indicator\n            currentIndicator.classList.remove('segment-button-indicator-animated');\n            currentIndicator.style.setProperty('transform', transform);\n            // Force a repaint to ensure the transform happens\n            currentIndicator.getBoundingClientRect();\n            // Add the transition to move the indicator into place\n            currentIndicator.classList.add('segment-button-indicator-animated');\n            // Remove the transform to slide the indicator back to the button clicked\n            currentIndicator.style.setProperty('transform', '');\n            this.scrollActiveButtonIntoView(true);\n        });\n        this.value = current.value;\n        this.setCheckedClasses();\n    }\n    setCheckedClasses() {\n        const buttons = this.getButtons();\n        const index = buttons.findIndex((button) => button.value === this.value);\n        const next = index + 1;\n        for (const button of buttons) {\n            button.classList.remove('segment-button-after-checked');\n        }\n        if (next < buttons.length) {\n            buttons[next].classList.add('segment-button-after-checked');\n        }\n    }\n    getSegmentView() {\n        const buttons = this.getButtons();\n        // Get the first button with a contentId\n        const firstContentId = buttons.find((button) => button.contentId);\n        // Get the segment content with an id matching the button's contentId\n        const segmentContent = document.querySelector(`ion-segment-content[id=\"${firstContentId === null || firstContentId === void 0 ? void 0 : firstContentId.contentId}\"]`);\n        // Return the segment view for that matching segment content\n        return segmentContent === null || segmentContent === void 0 ? void 0 : segmentContent.closest('ion-segment-view');\n    }\n    handleSegmentViewScroll(ev) {\n        const { scrollRatio, isManualScroll } = ev.detail;\n        if (!isManualScroll) {\n            return;\n        }\n        const dispatchedFrom = ev.target;\n        const segmentViewEl = this.segmentViewEl;\n        const segmentEl = this.el;\n        // Only update the indicator if the event was dispatched from the correct segment view\n        if (ev.composedPath().includes(segmentViewEl) || (dispatchedFrom === null || dispatchedFrom === void 0 ? void 0 : dispatchedFrom.contains(segmentEl))) {\n            const buttons = this.getButtons();\n            // If no buttons are found or there is no value set then do nothing\n            if (!buttons.length)\n                return;\n            const index = buttons.findIndex((button) => button.value === this.value);\n            const current = buttons[index];\n            const nextIndex = Math.round(scrollRatio * (buttons.length - 1));\n            if (this.lastNextIndex === undefined || this.lastNextIndex !== nextIndex) {\n                this.lastNextIndex = nextIndex;\n                this.triggerScrollOnValueChange = false;\n                this.checkButton(current, buttons[nextIndex]);\n                this.emitValueChange();\n            }\n        }\n    }\n    /**\n     * Finds the related segment view and sets its current content\n     * based on the selected segment button. This method\n     * should be called on initial load of the segment,\n     * after the gesture is completed (if dragging between segments)\n     * and when a segment button is clicked directly.\n     */\n    updateSegmentView(smoothScroll = true) {\n        const buttons = this.getButtons();\n        const button = buttons.find((btn) => btn.value === this.value);\n        // If the button does not have a contentId then there is\n        // no associated segment view to update\n        if (!(button === null || button === void 0 ? void 0 : button.contentId)) {\n            return;\n        }\n        const segmentView = this.segmentViewEl;\n        if (segmentView) {\n            segmentView.setContent(button.contentId, smoothScroll);\n        }\n    }\n    scrollActiveButtonIntoView(smoothScroll = true) {\n        const { scrollable, value, el } = this;\n        if (scrollable) {\n            const buttons = this.getButtons();\n            const activeButton = buttons.find((button) => button.value === value);\n            if (activeButton !== undefined) {\n                const scrollContainerBox = el.getBoundingClientRect();\n                const activeButtonBox = activeButton.getBoundingClientRect();\n                /**\n                 * Subtract the active button x position from the scroll\n                 * container x position. This will give us the x position\n                 * of the active button within the scroll container.\n                 */\n                const activeButtonLeft = activeButtonBox.x - scrollContainerBox.x;\n                /**\n                 * If we just used activeButtonLeft, then the active button\n                 * would be aligned with the left edge of the scroll container.\n                 * Instead, we want the segment button to be centered. As a result,\n                 * we subtract half of the scroll container width. This will position\n                 * the left edge of the active button at the midpoint of the scroll container.\n                 * We then add half of the active button width. This will position the active\n                 * button such that the midpoint of the active button is at the midpoint of the\n                 * scroll container.\n                 */\n                const centeredX = activeButtonLeft - scrollContainerBox.width / 2 + activeButtonBox.width / 2;\n                /**\n                 * newScrollPosition is the absolute scroll position that the\n                 * container needs to move to in order to center the active button.\n                 * It is calculated by adding the current scroll position\n                 * (scrollLeft) to the offset needed to center the button\n                 * (centeredX).\n                 */\n                const newScrollPosition = el.scrollLeft + centeredX;\n                /**\n                 * We intentionally use scrollTo here instead of scrollIntoView\n                 * to avoid a WebKit bug where accelerated animations break\n                 * when using scrollIntoView. Using scrollIntoView will cause the\n                 * segment container to jump during the transition and then snap into place.\n                 * This is because scrollIntoView can potentially cause parent element\n                 * containers to also scroll. scrollTo does not have this same behavior, so\n                 * we use this API instead.\n                 *\n                 * scrollTo is used instead of scrollBy because there is a\n                 * Webkit bug that causes scrollBy to not work smoothly when\n                 * the active button is near the edge of the scroll container.\n                 * This leads to the buttons to jump around during the transition.\n                 *\n                 * Note that if there is not enough scrolling space to center the element\n                 * within the scroll container, the browser will attempt\n                 * to center by as much as it can.\n                 */\n                el.scrollTo({\n                    top: 0,\n                    left: newScrollPosition,\n                    behavior: smoothScroll ? 'smooth' : 'instant',\n                });\n            }\n        }\n    }\n    setNextIndex(detail, isEnd = false) {\n        const rtl = isRTL(this.el);\n        const activated = this.activated;\n        const buttons = this.getButtons();\n        const index = buttons.findIndex((button) => button.value === this.value);\n        const previous = buttons[index];\n        let current;\n        let nextIndex;\n        if (index === -1) {\n            return;\n        }\n        // Get the element that the touch event started on in case\n        // it was the checked button, then we will move the indicator\n        const rect = previous.getBoundingClientRect();\n        const left = rect.left;\n        const width = rect.width;\n        // Get the element that the gesture is on top of based on the currentX of the\n        // gesture event and the Y coordinate of the starting element, since the gesture\n        // can move up and down off of the segment\n        const currentX = detail.currentX;\n        const previousY = rect.top + rect.height / 2;\n        /**\n         * Segment can be used inside the shadow dom\n         * so doing document.elementFromPoint would never\n         * return a segment button in that instance.\n         * We use getRootNode to which will return the parent\n         * shadow root if used inside a shadow component and\n         * returns document otherwise.\n         */\n        const root = this.el.getRootNode();\n        const nextEl = root.elementFromPoint(currentX, previousY);\n        const decreaseIndex = rtl ? currentX > left + width : currentX < left;\n        const increaseIndex = rtl ? currentX < left : currentX > left + width;\n        // If the indicator is currently activated then we have started the gesture\n        // on top of the checked button so we need to slide the indicator\n        // by checking the button next to it as we move\n        if (activated && !isEnd) {\n            // Decrease index, move left in LTR & right in RTL\n            if (decreaseIndex) {\n                const newIndex = index - 1;\n                if (newIndex >= 0) {\n                    nextIndex = newIndex;\n                }\n                // Increase index, moves right in LTR & left in RTL\n            }\n            else if (increaseIndex) {\n                if (activated && !isEnd) {\n                    const newIndex = index + 1;\n                    if (newIndex < buttons.length) {\n                        nextIndex = newIndex;\n                    }\n                }\n            }\n            if (nextIndex !== undefined && !buttons[nextIndex].disabled) {\n                current = buttons[nextIndex];\n            }\n        }\n        // If the indicator is not activated then we will just set the indicator\n        // to the element where the gesture ended\n        if (!activated && isEnd) {\n            current = nextEl;\n        }\n        if (current != null) {\n            /**\n             * If current element is ion-segment then that means\n             * user tried to select a disabled ion-segment-button,\n             * and we should not update the ripple.\n             */\n            if (current.tagName === 'ION-SEGMENT') {\n                return false;\n            }\n            if (previous !== current) {\n                this.checkButton(previous, current);\n            }\n        }\n        return true;\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            segment: true,\n        });\n    }\n    onKeyDown(ev) {\n        const rtl = isRTL(this.el);\n        let keyDownSelectsButton = this.selectOnFocus;\n        let current;\n        switch (ev.key) {\n            case 'ArrowRight':\n                ev.preventDefault();\n                current = rtl ? this.getSegmentButton('previous') : this.getSegmentButton('next');\n                break;\n            case 'ArrowLeft':\n                ev.preventDefault();\n                current = rtl ? this.getSegmentButton('next') : this.getSegmentButton('previous');\n                break;\n            case 'Home':\n                ev.preventDefault();\n                current = this.getSegmentButton('first');\n                break;\n            case 'End':\n                ev.preventDefault();\n                current = this.getSegmentButton('last');\n                break;\n            case ' ':\n            case 'Enter':\n                ev.preventDefault();\n                current = document.activeElement;\n                keyDownSelectsButton = true;\n        }\n        if (!current) {\n            return;\n        }\n        if (keyDownSelectsButton) {\n            const previous = this.checked;\n            this.checkButton(previous || current, current);\n            if (current !== previous) {\n                this.emitValueChange();\n            }\n        }\n        current.setFocus();\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'e67ed512739cf2cfe657b0c44ebc3dfb1e9fbb79', role: \"tablist\", onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'segment-activated': this.activated,\n                'segment-disabled': this.disabled,\n                'segment-scrollable': this.scrollable,\n            }) }, h(\"slot\", { key: '804d8acfcb9abfeeee38244b015fbc5c36330b9e', onSlotchange: this.onSlottedItemsChange })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"color\": [\"colorChanged\"],\n        \"swipeGesture\": [\"swipeGestureChanged\"],\n        \"value\": [\"valueChanged\"],\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nSegment.style = {\n    ios: segmentIosCss,\n    md: segmentMdCss\n};\n\nconst segmentButtonIosCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:\\\"\\\";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff))))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}\";\n\nconst segmentButtonMdCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #0054e9));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}\";\n\nlet ids = 0;\nconst SegmentButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.segmentEl = null;\n        this.inheritedAttributes = {};\n        this.checked = false;\n        /**\n         * If `true`, the user cannot interact with the segment button.\n         */\n        this.disabled = false;\n        /**\n         * Set the layout of the text and icon in the segment.\n         */\n        this.layout = 'icon-top';\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        /**\n         * The value of the segment button.\n         */\n        this.value = 'ion-sb-' + ids++;\n        this.updateStyle = () => {\n            forceUpdate(this);\n        };\n        this.updateState = () => {\n            const { segmentEl } = this;\n            if (segmentEl) {\n                this.checked = segmentEl.value === this.value;\n                if (segmentEl.disabled) {\n                    this.disabled = true;\n                }\n            }\n        };\n    }\n    valueChanged() {\n        this.updateState();\n    }\n    connectedCallback() {\n        const segmentEl = (this.segmentEl = this.el.closest('ion-segment'));\n        if (segmentEl) {\n            this.updateState();\n            addEventListener(segmentEl, 'ionSelect', this.updateState);\n            addEventListener(segmentEl, 'ionStyle', this.updateStyle);\n        }\n        // Prevent buttons from being disabled when associated with segment content\n        if (this.contentId && this.disabled) {\n            printIonWarning(`[ion-segment-button] - Segment buttons cannot be disabled when associated with an <ion-segment-content>.`);\n            this.disabled = false;\n        }\n    }\n    disconnectedCallback() {\n        const segmentEl = this.segmentEl;\n        if (segmentEl) {\n            removeEventListener(segmentEl, 'ionSelect', this.updateState);\n            removeEventListener(segmentEl, 'ionStyle', this.updateStyle);\n            this.segmentEl = null;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n        // Return if there is no contentId defined\n        if (!this.contentId)\n            return;\n        // Attempt to find the Segment Content by its contentId\n        const segmentContent = document.getElementById(this.contentId);\n        // If no associated Segment Content exists, log an error and return\n        if (!segmentContent) {\n            printIonError(`[ion-segment-button] - Unable to find Segment Content with id=\"${this.contentId}\".`);\n            return;\n        }\n        // Ensure the found element is a valid ION-SEGMENT-CONTENT\n        if (segmentContent.tagName !== 'ION-SEGMENT-CONTENT') {\n            printIonError(`[ion-segment-button] - Element with id=\"${this.contentId}\" is not an <ion-segment-content> element.`);\n            return;\n        }\n    }\n    get hasLabel() {\n        return !!this.el.querySelector('ion-label');\n    }\n    get hasIcon() {\n        return !!this.el.querySelector('ion-icon');\n    }\n    /**\n     * @internal\n     * Focuses the native <button> element\n     * inside of ion-segment-button.\n     */\n    async setFocus() {\n        const { nativeEl } = this;\n        if (nativeEl !== undefined) {\n            nativeEl.focus();\n        }\n    }\n    render() {\n        const { checked, type, disabled, hasIcon, hasLabel, layout, segmentEl } = this;\n        const mode = getIonMode(this);\n        const hasSegmentColor = () => (segmentEl === null || segmentEl === void 0 ? void 0 : segmentEl.color) !== undefined;\n        return (h(Host, { key: '26cb7ee90455bcaa6416125802d7e5729fa05b5b', class: {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'in-segment': hostContext('ion-segment', this.el),\n                'in-segment-color': hasSegmentColor(),\n                'segment-button-has-label': hasLabel,\n                'segment-button-has-icon': hasIcon,\n                'segment-button-has-label-only': hasLabel && !hasIcon,\n                'segment-button-has-icon-only': hasIcon && !hasLabel,\n                'segment-button-disabled': disabled,\n                'segment-button-checked': checked,\n                [`segment-button-layout-${layout}`]: true,\n                'ion-activatable': true,\n                'ion-activatable-instant': true,\n                'ion-focusable': true,\n            } }, h(\"button\", Object.assign({ key: '75add37f11c107d1e2cfdb154e08004e9579e863', \"aria-selected\": checked ? 'true' : 'false', role: \"tab\", ref: (el) => (this.nativeEl = el), type: type, class: \"button-native\", part: \"native\", disabled: disabled }, this.inheritedAttributes), h(\"span\", { key: '8e720d2a3e304903685bf09d226a64e944d78a22', class: \"button-inner\" }, h(\"slot\", { key: 'c8e7b3ebf8f03042a1001155643b585283c73c65' })), mode === 'md' && h(\"ion-ripple-effect\", { key: '3586ac317b8d82c92b0ccfbfae42f8778612321b' })), h(\"div\", { key: '9cf93957da9e8dc333c8b05327bb903385b1c5f4', part: \"indicator\", class: \"segment-button-indicator segment-button-indicator-animated\" }, h(\"div\", { key: 'd3b6f0b3860ec6896b46703f64ed1cc8c75612e3', part: \"indicator-background\", class: \"segment-button-indicator-background\" }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nSegmentButton.style = {\n    ios: segmentButtonIosCss,\n    md: segmentButtonMdCss\n};\n\nexport { Segment as ion_segment, SegmentButton as ion_segment_button };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AACzM,SAASlB,CAAC,IAAImB,GAAG,EAAEC,CAAC,IAAIC,gBAAgB,EAAEN,CAAC,IAAIO,mBAAmB,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,uBAAuB;AACzH,SAASC,CAAC,IAAIC,KAAK,QAAQ,mBAAmB;AAC9C,SAASC,CAAC,IAAIC,kBAAkB,EAAEpB,CAAC,IAAIqB,WAAW,QAAQ,qBAAqB;AAE/E,MAAMC,aAAa,GAAG,4sCAA4sC;AAEluC,MAAMC,YAAY,GAAG,o3BAAo3B;AAEz4B,MAAMC,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjBjC,gBAAgB,CAAC,IAAI,EAAEiC,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGhC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACiC,SAAS,GAAGjC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACkC,QAAQ,GAAGlC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,OAAO,GAAGD,EAAE,CAACE,MAAM;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAACC,OAAO;MAC7B;MACA;MACA;MACA;MACA,IAAIH,OAAO,CAACI,OAAO,KAAK,aAAa,EAAE;QACnC;MACJ;MACA,IAAI,CAACC,KAAK,GAAGL,OAAO,CAACK,KAAK;MAC1B,IAAIL,OAAO,KAAKE,QAAQ,EAAE;QACtB,IAAI,CAACI,eAAe,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACd,aAAa,EAAE;QACpB,IAAI,CAACe,iBAAiB,CAAC,CAAC;QACxB,IAAI,IAAI,CAACZ,UAAU,IAAIO,QAAQ,EAAE;UAC7B,IAAI,CAACM,WAAW,CAACN,QAAQ,EAAEF,OAAO,CAAC;QACvC;MACJ,CAAC,MACI,IAAI,IAAI,CAACL,UAAU,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QAC5C,IAAIM,QAAQ,EAAE;UACV,IAAI,CAACM,WAAW,CAACN,QAAQ,EAAEF,OAAO,CAAC;QACvC,CAAC,MACI;UACD,IAAI,CAACS,iBAAiB,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,oBAAoB,GAAG,MAAM;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,YAAY,CAAC,IAAI,CAACN,KAAK,CAAC;IACjC,CAAC;IACD,IAAI,CAACO,gBAAgB,GAAIC,QAAQ,IAAK;MAClC,IAAIC,EAAE,EAAEC,EAAE;MACV,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEC,MAAM,IAAK,CAACA,MAAM,CAACzB,QAAQ,CAAC;MACtE,MAAM0B,SAAS,GAAGJ,OAAO,CAACK,SAAS,CAAEF,MAAM,IAAKA,MAAM,KAAKG,QAAQ,CAACC,aAAa,CAAC;MAClF,QAAQV,QAAQ;QACZ,KAAK,OAAO;UACR,OAAOG,OAAO,CAAC,CAAC,CAAC;QACrB,KAAK,MAAM;UACP,OAAOA,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC;QACtC,KAAK,MAAM;UACP,OAAO,CAACV,EAAE,GAAGE,OAAO,CAACI,SAAS,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,OAAO,CAAC,CAAC,CAAC;QACpF,KAAK,UAAU;UACX,OAAO,CAACD,EAAE,GAAGC,OAAO,CAACI,SAAS,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC;QACrG;UACI,OAAO,IAAI;MACnB;IACJ,CAAC;EACL;EACAC,YAAYA,CAACpB,KAAK,EAAEqB,QAAQ,EAAE;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAKA,QAAQ,KAAKC,SAAS,IAAItB,KAAK,KAAKsB,SAAS,IAAMD,QAAQ,KAAKC,SAAS,IAAItB,KAAK,KAAKsB,SAAU,EAAE;MACpG,IAAI,CAACC,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAnB,YAAYA,CAACN,KAAK,EAAEqB,QAAQ,EAAE;IAC1B;IACA,IAAI,IAAI,CAAClC,aAAa,IAAIa,KAAK,KAAKsB,SAAS,EAAE;MAC3C,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACY,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAACZ,KAAK;MACvC;IACJ;IACA,IAAIqB,QAAQ,KAAKC,SAAS,IAAItB,KAAK,KAAKsB,SAAS,EAAE;MAC/C,MAAMX,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MACjC,MAAMf,QAAQ,GAAGc,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAKqB,QAAQ,CAAC;MACpE,MAAM1B,OAAO,GAAGgB,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAKA,KAAK,CAAC;MAChE,IAAIH,QAAQ,IAAIF,OAAO,EAAE;QACrB,IAAI,CAAC,IAAI,CAACR,aAAa,EAAE;UACrB,IAAI,CAACgB,WAAW,CAACN,QAAQ,EAAEF,OAAO,CAAC;QACvC,CAAC,MACI,IAAI,IAAI,CAACgC,0BAA0B,KAAK,KAAK,EAAE;UAChD,IAAI,CAACzB,iBAAiB,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC,MACI,IAAIF,KAAK,KAAKsB,SAAS,IAAID,QAAQ,KAAKC,SAAS,IAAI,IAAI,CAACnC,aAAa,EAAE;MAC1E,IAAI,CAACe,iBAAiB,CAAC,CAAC;IAC5B;IACA;AACR;AACA;AACA;IACQ,IAAI,CAACjB,SAAS,CAAC2C,IAAI,CAAC;MAAE5B;IAAM,CAAC,CAAC;IAC9B;IACA,IAAI,CAAC,IAAI,CAACb,aAAa,EAAE;MACrB,IAAI,CAAC0C,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,CAACF,0BAA0B,GAAGL,SAAS;EAC/C;EACAQ,eAAeA,CAAA,EAAG;IACd,IAAI,CAACL,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAACtC,aAAa,EAAE;MACrB,MAAMwB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MACjC,KAAK,MAAME,MAAM,IAAIH,OAAO,EAAE;QAC1BG,MAAM,CAACzB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACnC;IACJ,CAAC,MACI;MACD,IAAI,CAACF,aAAa,CAACE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC/C;EACJ;EACAoC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACM,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAAC1C,UAAU,IAAI,CAAC,IAAI,CAACD,QAAQ,IAAI,IAAI,CAACE,YAAY,CAAC;IAChF;EACJ;EACA0C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACV,SAAS,CAAC,CAAC;IAChB,IAAI,CAACpC,aAAa,GAAG,IAAI,CAAC+C,cAAc,CAAC,CAAC;EAC9C;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAChD,aAAa,GAAG,IAAI;EAC7B;EACAiD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACb,SAAS,CAAC,CAAC;EACpB;EACMc,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrBD,KAAI,CAACnD,aAAa,GAAGmD,KAAI,CAACJ,cAAc,CAAC,CAAC;MAC1CI,KAAI,CAAClC,iBAAiB,CAAC,CAAC;MACxB;AACR;AACA;AACA;MACQpC,GAAG,CAAC,MAAM;QACN;AACZ;AACA;AACA;AACA;AACA;QACYsE,KAAI,CAACT,0BAA0B,CAAC,KAAK,CAAC;MAC1C,CAAC,CAAC;MACFS,KAAI,CAACP,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAES,aAAa,CAAC;QAC/DC,EAAE,EAAEH,KAAI,CAACG,EAAE;QACXC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAGpD,EAAE,IAAK4C,KAAI,CAACQ,OAAO,CAACpD,EAAE,CAAC;QACjCqD,MAAM,EAAGrD,EAAE,IAAK4C,KAAI,CAACS,MAAM,CAACrD,EAAE,CAAC;QAC/BsD,KAAK,EAAGtD,EAAE,IAAK4C,KAAI,CAACU,KAAK,CAACtD,EAAE;MAChC,CAAC,CAAC;MACF4C,KAAI,CAACb,cAAc,CAAC,CAAC;MACrB,IAAIa,KAAI,CAACjD,QAAQ,EAAE;QACfiD,KAAI,CAACR,eAAe,CAAC,CAAC;MAC1B;MACA;MACA;MACAQ,KAAI,CAACpC,iBAAiB,CAAC,KAAK,CAAC;IAAC;EAClC;EACA4C,OAAOA,CAACG,MAAM,EAAE;IACZ,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAClD,KAAK;IACpC,IAAI,CAACmD,QAAQ,CAACF,MAAM,CAAC;EACzB;EACAF,MAAMA,CAACE,MAAM,EAAE;IACX,IAAI,CAACG,YAAY,CAACH,MAAM,CAAC;EAC7B;EACAD,KAAKA,CAACC,MAAM,EAAE;IACV,IAAI,CAACI,YAAY,CAAC,KAAK,CAAC;IACxB,IAAI,CAACD,YAAY,CAACH,MAAM,EAAE,IAAI,CAAC;IAC/BA,MAAM,CAACK,KAAK,CAACC,wBAAwB,CAAC,CAAC;IACvC,MAAMvD,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,KAAKsB,SAAS,EAAE;MACrB,IAAI,IAAI,CAAC4B,kBAAkB,KAAKlD,KAAK,EAAE;QACnC,IAAI,CAACC,eAAe,CAAC,CAAC;QACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ;IACA,IAAI,CAACgD,kBAAkB,GAAG5B,SAAS;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrB,eAAeA,CAAA,EAAG;IACd,MAAM;MAAED;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAChB,SAAS,CAAC4C,IAAI,CAAC;MAAE5B;IAAM,CAAC,CAAC;EAClC;EACAY,UAAUA,CAAA,EAAG;IACT,OAAO4C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChB,EAAE,CAACiB,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;EACrE;EACA,IAAI5D,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACc,UAAU,CAAC,CAAC,CAACc,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;EAC1E;EACA;AACJ;AACA;AACA;EACIqD,YAAYA,CAACjE,SAAS,EAAE;IACpB,MAAMuB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjCD,OAAO,CAACgD,OAAO,CAAE7C,MAAM,IAAK;MACxBA,MAAM,CAAC8C,SAAS,CAACC,MAAM,CAAC,0BAA0B,EAAEzE,SAAS,CAAC;IAClE,CAAC,CAAC;IACF,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA+D,QAAQA,CAACF,MAAM,EAAE;IACb,MAAMa,OAAO,GAAGb,MAAM,CAACK,KAAK,CAAC1D,MAAM;IACnC,MAAMe,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAMd,OAAO,GAAGa,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACrE;IACA;IACA,IAAI8D,OAAO,CAAC/D,OAAO,KAAK,oBAAoB,EAAE;MAC1C;IACJ;IACA;IACA,IAAI,CAACD,OAAO,EAAE;MACV,IAAI,CAACE,KAAK,GAAG8D,OAAO,CAAC9D,KAAK;MAC1B,IAAI,CAACI,iBAAiB,CAAC,CAAC;IAC5B;IACA;IACA;IACA,IAAI,IAAI,CAACJ,KAAK,KAAK8D,OAAO,CAAC9D,KAAK,EAAE;MAC9B,IAAI,CAACqD,YAAY,CAAC,IAAI,CAAC;IAC3B;EACJ;EACAU,YAAYA,CAACjD,MAAM,EAAE;IACjB,MAAMkD,IAAI,GAAGlD,MAAM,CAACmD,UAAU,IAAInD,MAAM;IACxC,OAAOkD,IAAI,CAACE,aAAa,CAAC,2BAA2B,CAAC;EAC1D;EACA/D,WAAWA,CAACN,QAAQ,EAAEF,OAAO,EAAE;IAC3B,MAAMwE,iBAAiB,GAAG,IAAI,CAACJ,YAAY,CAAClE,QAAQ,CAAC;IACrD,MAAMuE,gBAAgB,GAAG,IAAI,CAACL,YAAY,CAACpE,OAAO,CAAC;IACnD,IAAIwE,iBAAiB,KAAK,IAAI,IAAIC,gBAAgB,KAAK,IAAI,EAAE;MACzD;IACJ;IACA,MAAMC,kBAAkB,GAAGF,iBAAiB,CAACG,qBAAqB,CAAC,CAAC;IACpE,MAAMC,iBAAiB,GAAGH,gBAAgB,CAACE,qBAAqB,CAAC,CAAC;IAClE,MAAME,UAAU,GAAGH,kBAAkB,CAACI,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IACrE,MAAMC,SAAS,GAAGL,kBAAkB,CAACM,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAClE;IACA;IACA,MAAMC,SAAS,GAAG,eAAeF,SAAS,oBAAoBF,UAAU,GAAG;IAC3EtH,SAAS,CAAC,MAAM;MACZ;MACAkH,gBAAgB,CAACR,SAAS,CAACiB,MAAM,CAAC,mCAAmC,CAAC;MACtET,gBAAgB,CAACU,KAAK,CAACC,WAAW,CAAC,WAAW,EAAEH,SAAS,CAAC;MAC1D;MACAR,gBAAgB,CAACE,qBAAqB,CAAC,CAAC;MACxC;MACAF,gBAAgB,CAACR,SAAS,CAACoB,GAAG,CAAC,mCAAmC,CAAC;MACnE;MACAZ,gBAAgB,CAACU,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;MACnD,IAAI,CAAClD,0BAA0B,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAAC7B,KAAK,GAAGL,OAAO,CAACK,KAAK;IAC1B,IAAI,CAACI,iBAAiB,CAAC,CAAC;EAC5B;EACAA,iBAAiBA,CAAA,EAAG;IAChB,MAAMO,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAMqE,KAAK,GAAGtE,OAAO,CAACK,SAAS,CAAEF,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACxE,MAAMkF,IAAI,GAAGD,KAAK,GAAG,CAAC;IACtB,KAAK,MAAMnE,MAAM,IAAIH,OAAO,EAAE;MAC1BG,MAAM,CAAC8C,SAAS,CAACiB,MAAM,CAAC,8BAA8B,CAAC;IAC3D;IACA,IAAIK,IAAI,GAAGvE,OAAO,CAACQ,MAAM,EAAE;MACvBR,OAAO,CAACuE,IAAI,CAAC,CAACtB,SAAS,CAACoB,GAAG,CAAC,8BAA8B,CAAC;IAC/D;EACJ;EACA9C,cAAcA,CAAA,EAAG;IACb,MAAMvB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC;IACA,MAAMuE,cAAc,GAAGxE,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACsE,SAAS,CAAC;IACjE;IACA,MAAMC,cAAc,GAAGpE,QAAQ,CAACiD,aAAa,CAAC,2BAA2BiB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,SAAS,IAAI,CAAC;IACtK;IACA,OAAOC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC;EACrH;EACAC,uBAAuBA,CAAC7F,EAAE,EAAE;IACxB,MAAM;MAAE8F,WAAW;MAAEC;IAAe,CAAC,GAAG/F,EAAE,CAACuD,MAAM;IACjD,IAAI,CAACwC,cAAc,EAAE;MACjB;IACJ;IACA,MAAMC,cAAc,GAAGhG,EAAE,CAACE,MAAM;IAChC,MAAMT,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,MAAMwG,SAAS,GAAG,IAAI,CAAClD,EAAE;IACzB;IACA,IAAI/C,EAAE,CAACkG,YAAY,CAAC,CAAC,CAACC,QAAQ,CAAC1G,aAAa,CAAC,KAAKuG,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,QAAQ,CAACH,SAAS,CAAC,CAAC,EAAE;MACnJ,MAAMhF,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MACjC;MACA,IAAI,CAACD,OAAO,CAACQ,MAAM,EACf;MACJ,MAAM8D,KAAK,GAAGtE,OAAO,CAACK,SAAS,CAAEF,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;MACxE,MAAML,OAAO,GAAGgB,OAAO,CAACsE,KAAK,CAAC;MAC9B,MAAMc,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACT,WAAW,IAAI7E,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAC;MAChE,IAAI,IAAI,CAAC+E,aAAa,KAAK5E,SAAS,IAAI,IAAI,CAAC4E,aAAa,KAAKH,SAAS,EAAE;QACtE,IAAI,CAACG,aAAa,GAAGH,SAAS;QAC9B,IAAI,CAACpE,0BAA0B,GAAG,KAAK;QACvC,IAAI,CAACxB,WAAW,CAACR,OAAO,EAAEgB,OAAO,CAACoF,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC9F,eAAe,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACiG,YAAY,GAAG,IAAI,EAAE;IACnC,MAAMxF,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAME,MAAM,GAAGH,OAAO,CAACe,IAAI,CAAE0E,GAAG,IAAKA,GAAG,CAACpG,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IAC9D;IACA;IACA,IAAI,EAAEc,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACsE,SAAS,CAAC,EAAE;MACrE;IACJ;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAClH,aAAa;IACtC,IAAIkH,WAAW,EAAE;MACbA,WAAW,CAACC,UAAU,CAACxF,MAAM,CAACsE,SAAS,EAAEe,YAAY,CAAC;IAC1D;EACJ;EACAtE,0BAA0BA,CAACsE,YAAY,GAAG,IAAI,EAAE;IAC5C,MAAM;MAAE7G,UAAU;MAAEU,KAAK;MAAEyC;IAAG,CAAC,GAAG,IAAI;IACtC,IAAInD,UAAU,EAAE;MACZ,MAAMqB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MACjC,MAAM2F,YAAY,GAAG5F,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAKA,KAAK,CAAC;MACrE,IAAIuG,YAAY,KAAKjF,SAAS,EAAE;QAC5B,MAAMkF,kBAAkB,GAAG/D,EAAE,CAAC6B,qBAAqB,CAAC,CAAC;QACrD,MAAMmC,eAAe,GAAGF,YAAY,CAACjC,qBAAqB,CAAC,CAAC;QAC5D;AAChB;AACA;AACA;AACA;QACgB,MAAMoC,gBAAgB,GAAGD,eAAe,CAACE,CAAC,GAAGH,kBAAkB,CAACG,CAAC;QACjE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMC,SAAS,GAAGF,gBAAgB,GAAGF,kBAAkB,CAAC/B,KAAK,GAAG,CAAC,GAAGgC,eAAe,CAAChC,KAAK,GAAG,CAAC;QAC7F;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMoC,iBAAiB,GAAGpE,EAAE,CAACqE,UAAU,GAAGF,SAAS;QACnD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBnE,EAAE,CAACsE,QAAQ,CAAC;UACRC,GAAG,EAAE,CAAC;UACNrC,IAAI,EAAEkC,iBAAiB;UACvBI,QAAQ,EAAEd,YAAY,GAAG,QAAQ,GAAG;QACxC,CAAC,CAAC;MACN;IACJ;EACJ;EACA/C,YAAYA,CAACH,MAAM,EAAEiE,KAAK,GAAG,KAAK,EAAE;IAChC,MAAMC,GAAG,GAAG5I,KAAK,CAAC,IAAI,CAACkE,EAAE,CAAC;IAC1B,MAAMrD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMuB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAMqE,KAAK,GAAGtE,OAAO,CAACK,SAAS,CAAEF,MAAM,IAAKA,MAAM,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACxE,MAAMH,QAAQ,GAAGc,OAAO,CAACsE,KAAK,CAAC;IAC/B,IAAItF,OAAO;IACX,IAAIoG,SAAS;IACb,IAAId,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IACA;IACA;IACA,MAAMmC,IAAI,GAAGvH,QAAQ,CAACyE,qBAAqB,CAAC,CAAC;IAC7C,MAAMK,IAAI,GAAGyC,IAAI,CAACzC,IAAI;IACtB,MAAMF,KAAK,GAAG2C,IAAI,CAAC3C,KAAK;IACxB;IACA;IACA;IACA,MAAM4C,QAAQ,GAAGpE,MAAM,CAACoE,QAAQ;IAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,GAAG,GAAGI,IAAI,CAACG,MAAM,GAAG,CAAC;IAC5C;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMvD,IAAI,GAAG,IAAI,CAACvB,EAAE,CAAC+E,WAAW,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAGzD,IAAI,CAAC0D,gBAAgB,CAACL,QAAQ,EAAEC,SAAS,CAAC;IACzD,MAAMK,aAAa,GAAGR,GAAG,GAAGE,QAAQ,GAAG1C,IAAI,GAAGF,KAAK,GAAG4C,QAAQ,GAAG1C,IAAI;IACrE,MAAMiD,aAAa,GAAGT,GAAG,GAAGE,QAAQ,GAAG1C,IAAI,GAAG0C,QAAQ,GAAG1C,IAAI,GAAGF,KAAK;IACrE;IACA;IACA;IACA,IAAIrF,SAAS,IAAI,CAAC8H,KAAK,EAAE;MACrB;MACA,IAAIS,aAAa,EAAE;QACf,MAAME,QAAQ,GAAG5C,KAAK,GAAG,CAAC;QAC1B,IAAI4C,QAAQ,IAAI,CAAC,EAAE;UACf9B,SAAS,GAAG8B,QAAQ;QACxB;QACA;MACJ,CAAC,MACI,IAAID,aAAa,EAAE;QACpB,IAAIxI,SAAS,IAAI,CAAC8H,KAAK,EAAE;UACrB,MAAMW,QAAQ,GAAG5C,KAAK,GAAG,CAAC;UAC1B,IAAI4C,QAAQ,GAAGlH,OAAO,CAACQ,MAAM,EAAE;YAC3B4E,SAAS,GAAG8B,QAAQ;UACxB;QACJ;MACJ;MACA,IAAI9B,SAAS,KAAKzE,SAAS,IAAI,CAACX,OAAO,CAACoF,SAAS,CAAC,CAAC1G,QAAQ,EAAE;QACzDM,OAAO,GAAGgB,OAAO,CAACoF,SAAS,CAAC;MAChC;IACJ;IACA;IACA;IACA,IAAI,CAAC3G,SAAS,IAAI8H,KAAK,EAAE;MACrBvH,OAAO,GAAG8H,MAAM;IACpB;IACA,IAAI9H,OAAO,IAAI,IAAI,EAAE;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAIA,OAAO,CAACI,OAAO,KAAK,aAAa,EAAE;QACnC,OAAO,KAAK;MAChB;MACA,IAAIF,QAAQ,KAAKF,OAAO,EAAE;QACtB,IAAI,CAACQ,WAAW,CAACN,QAAQ,EAAEF,OAAO,CAAC;MACvC;IACJ;IACA,OAAO,IAAI;EACf;EACA4B,SAASA,CAAA,EAAG;IACR,IAAI,CAACrC,QAAQ,CAAC0C,IAAI,CAAC;MACfkG,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACAC,SAASA,CAACrI,EAAE,EAAE;IACV,MAAMyH,GAAG,GAAG5I,KAAK,CAAC,IAAI,CAACkE,EAAE,CAAC;IAC1B,IAAIuF,oBAAoB,GAAG,IAAI,CAACxI,aAAa;IAC7C,IAAIG,OAAO;IACX,QAAQD,EAAE,CAACuI,GAAG;MACV,KAAK,YAAY;QACbvI,EAAE,CAACwI,cAAc,CAAC,CAAC;QACnBvI,OAAO,GAAGwH,GAAG,GAAG,IAAI,CAAC5G,gBAAgB,CAAC,UAAU,CAAC,GAAG,IAAI,CAACA,gBAAgB,CAAC,MAAM,CAAC;QACjF;MACJ,KAAK,WAAW;QACZb,EAAE,CAACwI,cAAc,CAAC,CAAC;QACnBvI,OAAO,GAAGwH,GAAG,GAAG,IAAI,CAAC5G,gBAAgB,CAAC,MAAM,CAAC,GAAG,IAAI,CAACA,gBAAgB,CAAC,UAAU,CAAC;QACjF;MACJ,KAAK,MAAM;QACPb,EAAE,CAACwI,cAAc,CAAC,CAAC;QACnBvI,OAAO,GAAG,IAAI,CAACY,gBAAgB,CAAC,OAAO,CAAC;QACxC;MACJ,KAAK,KAAK;QACNb,EAAE,CAACwI,cAAc,CAAC,CAAC;QACnBvI,OAAO,GAAG,IAAI,CAACY,gBAAgB,CAAC,MAAM,CAAC;QACvC;MACJ,KAAK,GAAG;MACR,KAAK,OAAO;QACRb,EAAE,CAACwI,cAAc,CAAC,CAAC;QACnBvI,OAAO,GAAGsB,QAAQ,CAACC,aAAa;QAChC8G,oBAAoB,GAAG,IAAI;IACnC;IACA,IAAI,CAACrI,OAAO,EAAE;MACV;IACJ;IACA,IAAIqI,oBAAoB,EAAE;MACtB,MAAMnI,QAAQ,GAAG,IAAI,CAACC,OAAO;MAC7B,IAAI,CAACK,WAAW,CAACN,QAAQ,IAAIF,OAAO,EAAEA,OAAO,CAAC;MAC9C,IAAIA,OAAO,KAAKE,QAAQ,EAAE;QACtB,IAAI,CAACI,eAAe,CAAC,CAAC;MAC1B;IACJ;IACAN,OAAO,CAACwI,QAAQ,CAAC,CAAC;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGjL,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACE,IAAI,EAAE;MAAE0K,GAAG,EAAE,0CAA0C;MAAEK,IAAI,EAAE,SAAS;MAAE7I,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE8I,KAAK,EAAE9J,kBAAkB,CAAC,IAAI,CAAC+J,KAAK,EAAE;QACzI,CAACH,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE3J,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC+D,EAAE,CAAC;QACjD,kBAAkB,EAAE/D,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC+D,EAAE,CAAC;QAC9D,mBAAmB,EAAE,IAAI,CAACrD,SAAS;QACnC,kBAAkB,EAAE,IAAI,CAACC,QAAQ;QACjC,oBAAoB,EAAE,IAAI,CAACC;MAC/B,CAAC;IAAE,CAAC,EAAEjC,CAAC,CAAC,MAAM,EAAE;MAAE4K,GAAG,EAAE,0CAA0C;MAAEQ,YAAY,EAAE,IAAI,CAACpI;IAAqB,CAAC,CAAC,CAAC;EACtH;EACA,IAAIoC,EAAEA,CAAA,EAAG;IAAE,OAAOhF,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD7J,OAAO,CAACiG,KAAK,GAAG;EACZ6D,GAAG,EAAEhK,aAAa;EAClBiK,EAAE,EAAEhK;AACR,CAAC;AAED,MAAMiK,mBAAmB,GAAG,kxQAAkxQ;AAE9yQ,MAAMC,kBAAkB,GAAG,8pQAA8pQ;AAEzrQ,IAAIC,GAAG,GAAG,CAAC;AACX,MAAMC,aAAa,GAAG,MAAM;EACxBlK,WAAWA,CAACC,OAAO,EAAE;IACjBjC,gBAAgB,CAAC,IAAI,EAAEiC,OAAO,CAAC;IAC/B,IAAI,CAAC4G,SAAS,GAAG,IAAI;IACrB,IAAI,CAACsD,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACnJ,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACT,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAAC6J,MAAM,GAAG,UAAU;IACxB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;IACQ,IAAI,CAACnJ,KAAK,GAAG,SAAS,GAAG+I,GAAG,EAAE;IAC9B,IAAI,CAACK,WAAW,GAAG,MAAM;MACrBzL,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC;IACD,IAAI,CAAC0L,WAAW,GAAG,MAAM;MACrB,MAAM;QAAE1D;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAIA,SAAS,EAAE;QACX,IAAI,CAAC7F,OAAO,GAAG6F,SAAS,CAAC3F,KAAK,KAAK,IAAI,CAACA,KAAK;QAC7C,IAAI2F,SAAS,CAACtG,QAAQ,EAAE;UACpB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACxB;MACJ;IACJ,CAAC;EACL;EACAiB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC+I,WAAW,CAAC,CAAC;EACtB;EACApH,iBAAiBA,CAAA,EAAG;IAChB,MAAM0D,SAAS,GAAI,IAAI,CAACA,SAAS,GAAG,IAAI,CAAClD,EAAE,CAAC6C,OAAO,CAAC,aAAa,CAAE;IACnE,IAAIK,SAAS,EAAE;MACX,IAAI,CAAC0D,WAAW,CAAC,CAAC;MAClBnL,gBAAgB,CAACyH,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC0D,WAAW,CAAC;MAC1DnL,gBAAgB,CAACyH,SAAS,EAAE,UAAU,EAAE,IAAI,CAACyD,WAAW,CAAC;IAC7D;IACA;IACA,IAAI,IAAI,CAAChE,SAAS,IAAI,IAAI,CAAC/F,QAAQ,EAAE;MACjCxB,eAAe,CAAC,0GAA0G,CAAC;MAC3H,IAAI,CAACwB,QAAQ,GAAG,KAAK;IACzB;EACJ;EACA8C,oBAAoBA,CAAA,EAAG;IACnB,MAAMwD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACXxH,mBAAmB,CAACwH,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC0D,WAAW,CAAC;MAC7DlL,mBAAmB,CAACwH,SAAS,EAAE,UAAU,EAAE,IAAI,CAACyD,WAAW,CAAC;MAC5D,IAAI,CAACzD,SAAS,GAAG,IAAI;IACzB;EACJ;EACAvD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC6G,mBAAmB,GAAGK,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElL,iBAAiB,CAAC,IAAI,CAACoE,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IACxF;IACA,IAAI,CAAC,IAAI,CAAC2C,SAAS,EACf;IACJ;IACA,MAAMC,cAAc,GAAGpE,QAAQ,CAACuI,cAAc,CAAC,IAAI,CAACpE,SAAS,CAAC;IAC9D;IACA,IAAI,CAACC,cAAc,EAAE;MACjBtH,aAAa,CAAC,kEAAkE,IAAI,CAACqH,SAAS,IAAI,CAAC;MACnG;IACJ;IACA;IACA,IAAIC,cAAc,CAACtF,OAAO,KAAK,qBAAqB,EAAE;MAClDhC,aAAa,CAAC,2CAA2C,IAAI,CAACqH,SAAS,4CAA4C,CAAC;MACpH;IACJ;EACJ;EACA,IAAIqE,QAAQA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAAChH,EAAE,CAACyB,aAAa,CAAC,WAAW,CAAC;EAC/C;EACA,IAAIwF,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACjH,EAAE,CAACyB,aAAa,CAAC,UAAU,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACUiE,QAAQA,CAAA,EAAG;IAAA,IAAAwB,MAAA;IAAA,OAAApH,iBAAA;MACb,MAAM;QAAEqH;MAAS,CAAC,GAAGD,MAAI;MACzB,IAAIC,QAAQ,KAAKtI,SAAS,EAAE;QACxBsI,QAAQ,CAACC,KAAK,CAAC,CAAC;MACpB;IAAC;EACL;EACAzB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtI,OAAO;MAAEqJ,IAAI;MAAE9J,QAAQ;MAAEqK,OAAO;MAAED,QAAQ;MAAEP,MAAM;MAAEvD;IAAU,CAAC,GAAG,IAAI;IAC9E,MAAM0C,IAAI,GAAGjL,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0M,eAAe,GAAGA,CAAA,KAAM,CAACnE,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC6C,KAAK,MAAMlH,SAAS;IACnH,OAAQjE,CAAC,CAACE,IAAI,EAAE;MAAE0K,GAAG,EAAE,0CAA0C;MAAEM,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE3J,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC+D,EAAE,CAAC;QACjD,kBAAkB,EAAE/D,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC+D,EAAE,CAAC;QAC9D,YAAY,EAAE/D,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC+D,EAAE,CAAC;QACjD,kBAAkB,EAAEqH,eAAe,CAAC,CAAC;QACrC,0BAA0B,EAAEL,QAAQ;QACpC,yBAAyB,EAAEC,OAAO;QAClC,+BAA+B,EAAED,QAAQ,IAAI,CAACC,OAAO;QACrD,8BAA8B,EAAEA,OAAO,IAAI,CAACD,QAAQ;QACpD,yBAAyB,EAAEpK,QAAQ;QACnC,wBAAwB,EAAES,OAAO;QACjC,CAAC,yBAAyBoJ,MAAM,EAAE,GAAG,IAAI;QACzC,iBAAiB,EAAE,IAAI;QACvB,yBAAyB,EAAE,IAAI;QAC/B,eAAe,EAAE;MACrB;IAAE,CAAC,EAAE7L,CAAC,CAAC,QAAQ,EAAEiM,MAAM,CAACC,MAAM,CAAC;MAAEtB,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAEnI,OAAO,GAAG,MAAM,GAAG,OAAO;MAAEwI,IAAI,EAAE,KAAK;MAAEyB,GAAG,EAAGtH,EAAE,IAAM,IAAI,CAACmH,QAAQ,GAAGnH,EAAG;MAAE0G,IAAI,EAAEA,IAAI;MAAEZ,KAAK,EAAE,eAAe;MAAEyB,IAAI,EAAE,QAAQ;MAAE3K,QAAQ,EAAEA;IAAS,CAAC,EAAE,IAAI,CAAC4J,mBAAmB,CAAC,EAAE5L,CAAC,CAAC,MAAM,EAAE;MAAE4K,GAAG,EAAE,0CAA0C;MAAEM,KAAK,EAAE;IAAe,CAAC,EAAElL,CAAC,CAAC,MAAM,EAAE;MAAE4K,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAEI,IAAI,KAAK,IAAI,IAAIhL,CAAC,CAAC,mBAAmB,EAAE;MAAE4K,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE5K,CAAC,CAAC,KAAK,EAAE;MAAE4K,GAAG,EAAE,0CAA0C;MAAE+B,IAAI,EAAE,WAAW;MAAEzB,KAAK,EAAE;IAA6D,CAAC,EAAElL,CAAC,CAAC,KAAK,EAAE;MAAE4K,GAAG,EAAE,0CAA0C;MAAE+B,IAAI,EAAE,sBAAsB;MAAEzB,KAAK,EAAE;IAAsC,CAAC,CAAC,CAAC,CAAC;EACnzB;EACA,IAAI9F,EAAEA,CAAA,EAAG;IAAE,OAAOhF,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACDM,aAAa,CAAClE,KAAK,GAAG;EAClB6D,GAAG,EAAEE,mBAAmB;EACxBD,EAAE,EAAEE;AACR,CAAC;AAED,SAASjK,OAAO,IAAIoL,WAAW,EAAEjB,aAAa,IAAIkB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}