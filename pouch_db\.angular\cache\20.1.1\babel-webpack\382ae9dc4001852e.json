{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/pouchdb.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@ionic/angular\";\nfunction HomePage_ion_item_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doc_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", doc_r2.doc.name, \" - \", doc_r2.doc.email, \" \");\n  }\n}\nexport class HomePage {\n  constructor(pouchService) {\n    this.pouchService = pouchService;\n    this.formData = {\n      name: '',\n      email: ''\n    };\n    this.docs = [];\n    this.loadDocs();\n  }\n  onSubmit() {\n    this.pouchService.addDoc(this.formData).then(() => {\n      console.log('Document saved');\n      this.formData = {\n        name: '',\n        email: ''\n      }; // Clear form\n      this.loadDocs();\n    }).catch(err => console.error(err));\n  }\n  loadDocs() {\n    this.pouchService.getAllDocs().then(result => {\n      this.docs = result.rows;\n    }).catch(err => console.error(err));\n  }\n  static #_ = this.ɵfac = function HomePage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomePage)(i0.ɵɵdirectiveInject(i1.PouchdbService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomePage,\n    selectors: [[\"app-home\"]],\n    decls: 21,\n    vars: 4,\n    consts: [[\"myForm\", \"ngForm\"], [1, \"ion-padding\"], [3, \"ngSubmit\"], [\"position\", \"floating\"], [\"type\", \"text\", \"name\", \"name\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"expand\", \"full\", \"type\", \"submit\", 3, \"disabled\"], [4, \"ngFor\", \"ngForOf\"]],\n    template: function HomePage_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ion-header\")(1, \"ion-toolbar\")(2, \"ion-title\");\n        i0.ɵɵtext(3, \" PouchDB Form \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(4, \"ion-content\", 1)(5, \"form\", 2, 0);\n        i0.ɵɵlistener(\"ngSubmit\", function HomePage_Template_form_ngSubmit_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSubmit());\n        });\n        i0.ɵɵelementStart(7, \"ion-item\")(8, \"ion-label\", 3);\n        i0.ɵɵtext(9, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"ion-input\", 4);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function HomePage_Template_ion_input_ngModelChange_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.formData.name, $event) || (ctx.formData.name = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"ion-item\")(12, \"ion-label\", 3);\n        i0.ɵɵtext(13, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"ion-input\", 5);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function HomePage_Template_ion_input_ngModelChange_14_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.formData.email, $event) || (ctx.formData.email = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"ion-button\", 6);\n        i0.ɵɵtext(16, \"Save\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"ion-list\")(18, \"ion-list-header\");\n        i0.ɵɵtext(19, \" Saved Documents \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, HomePage_ion_item_20_Template, 2, 2, \"ion-item\", 7);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const myForm_r3 = i0.ɵɵreference(6);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData.name);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData.email);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", !myForm_r3.form.valid);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.docs);\n      }\n    },\n    dependencies: [i2.NgForOf, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, i4.IonButton, i4.IonContent, i4.IonHeader, i4.IonInput, i4.IonItem, i4.IonLabel, i4.IonList, i4.IonListHeader, i4.IonTitle, i4.IonToolbar, i4.TextValueAccessor],\n    styles: [\"#container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 26px;\\n}\\n\\n#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  line-height: 22px;\\n  color: #8c8c8c;\\n  margin: 0;\\n}\\n\\n#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLnBhZ2Uuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uLy4uLy4uL1ZJQ1RVUyUyMFVTRVIvT25lRHJpdmUvRGVza3RvcC9GUk9OVEVORCUyMERFVkVMT1BFUi9wb3VjaF9kYi9zcmMvYXBwL2hvbWUvaG9tZS5wYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxrQkFBQTtFQUVBLGtCQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxRQUFBO0VBQ0EsMkJBQUE7QUNBRjs7QURHQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtBQ0FGOztBREdBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBRUEsY0FBQTtFQUVBLFNBQUE7QUNGRjs7QURLQTtFQUNFLHFCQUFBO0FDRkYiLCJzb3VyY2VzQ29udGVudCI6WyIjY29udGFpbmVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIHRvcDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG59XG5cbiNjb250YWluZXIgc3Ryb25nIHtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBsaW5lLWhlaWdodDogMjZweDtcbn1cblxuI2NvbnRhaW5lciBwIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBsaW5lLWhlaWdodDogMjJweDtcblxuICBjb2xvcjogIzhjOGM4YztcblxuICBtYXJnaW46IDA7XG59XG5cbiNjb250YWluZXIgYSB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn0iLCIjY29udGFpbmVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICB0b3A6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xufVxuXG4jY29udGFpbmVyIHN0cm9uZyB7XG4gIGZvbnQtc2l6ZTogMjBweDtcbiAgbGluZS1oZWlnaHQ6IDI2cHg7XG59XG5cbiNjb250YWluZXIgcCB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDIycHg7XG4gIGNvbG9yOiAjOGM4YzhjO1xuICBtYXJnaW46IDA7XG59XG5cbiNjb250YWluZXIgYSB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "doc_r2", "doc", "name", "email", "HomePage", "constructor", "pouchService", "formData", "docs", "loadDocs", "onSubmit", "addDoc", "then", "console", "log", "catch", "err", "error", "getAllDocs", "result", "rows", "_", "ɵɵdirectiveInject", "i1", "PouchdbService", "_2", "selectors", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "ɵɵlistener", "HomePage_Template_form_ngSubmit_5_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtwoWayListener", "HomePage_Template_ion_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "HomePage_Template_ion_input_ngModelChange_14_listener", "ɵɵtemplate", "HomePage_ion_item_20_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "myForm_r3", "form", "valid"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home.page.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { PouchdbService } from '../services/pouchdb.service';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: 'home.page.html',\n  styleUrls: ['home.page.scss'],\n  standalone: true\n})\nexport class HomePage {\n  formData = {\n    name: '',\n    email: ''\n  };\n\n  docs: any[] = [];\n\n  constructor(private pouchService: PouchdbService) {\n    this.loadDocs();\n  }\n\n  onSubmit() {\n    this.pouchService.addDoc(this.formData)\n      .then(() => {\n        console.log('Document saved');\n        this.formData = { name: '', email: '' }; // Clear form\n        this.loadDocs();\n      })\n      .catch((err: any) => console.error(err));\n  }\n\n  loadDocs() {\n    this.pouchService.getAllDocs()\n      .then((result: { rows: any[]; }) => {\n        this.docs = result.rows;\n      })\n      .catch((err: any) => console.error(err));\n  }\n}\n", "<ion-header>\n  <ion-toolbar>\n    <ion-title>\n      PouchDB Form\n    </ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n  <form (ngSubmit)=\"onSubmit()\" #myForm=\"ngForm\">\n    <ion-item>\n      <ion-label position=\"floating\">Name</ion-label>\n      <ion-input type=\"text\" [(ngModel)]=\"formData.name\" name=\"name\" required></ion-input>\n    </ion-item>\n\n    <ion-item>\n      <ion-label position=\"floating\">Email</ion-label>\n      <ion-input type=\"email\" [(ngModel)]=\"formData.email\" name=\"email\" required></ion-input>\n    </ion-item>\n\n    <ion-button expand=\"full\" type=\"submit\" [disabled]=\"!myForm.form.valid\">Save</ion-button>\n  </form>\n\n  <ion-list>\n    <ion-list-header>\n      Saved Documents\n    </ion-list-header>\n    <ion-item *ngFor=\"let doc of docs\">\n      {{ doc.doc.name }} - {{ doc.doc.email }}\n    </ion-item>\n  </ion-list>\n</ion-content>\n"], "mappings": ";;;;;;;IC2BIA,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,GAAA,CAAAC,IAAA,SAAAF,MAAA,CAAAC,GAAA,CAAAE,KAAA,MACF;;;ADpBJ,OAAM,MAAOC,QAAQ;EAQnBC,YAAoBC,YAA4B;IAA5B,KAAAA,YAAY,GAAZA,YAAY;IAPhC,KAAAC,QAAQ,GAAG;MACTL,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE;KACR;IAED,KAAAK,IAAI,GAAU,EAAE;IAGd,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACJ,YAAY,CAACK,MAAM,CAAC,IAAI,CAACJ,QAAQ,CAAC,CACpCK,IAAI,CAAC,MAAK;MACTC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,IAAI,CAACP,QAAQ,GAAG;QAAEL,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAE,CAAC,CAAC;MACzC,IAAI,CAACM,QAAQ,EAAE;IACjB,CAAC,CAAC,CACDM,KAAK,CAAEC,GAAQ,IAAKH,OAAO,CAACI,KAAK,CAACD,GAAG,CAAC,CAAC;EAC5C;EAEAP,QAAQA,CAAA;IACN,IAAI,CAACH,YAAY,CAACY,UAAU,EAAE,CAC3BN,IAAI,CAAEO,MAAwB,IAAI;MACjC,IAAI,CAACX,IAAI,GAAGW,MAAM,CAACC,IAAI;IACzB,CAAC,CAAC,CACDL,KAAK,CAAEC,GAAQ,IAAKH,OAAO,CAACI,KAAK,CAACD,GAAG,CAAC,CAAC;EAC5C;EAAC,QAAAK,CAAA,G;qCA5BUjB,QAAQ,EAAAV,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAARrB,QAAQ;IAAAsB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCPjBtC,EAFJ,CAAAC,cAAA,iBAAY,kBACG,gBACA;QACTD,EAAA,CAAAE,MAAA,qBACF;QAEJF,EAFI,CAAAG,YAAA,EAAY,EACA,EACH;QAGXH,EADF,CAAAC,cAAA,qBAAiC,iBACgB;QAAzCD,EAAA,CAAAwC,UAAA,sBAAAC,2CAAA;UAAAzC,EAAA,CAAA0C,aAAA,CAAAC,GAAA;UAAA,OAAA3C,EAAA,CAAA4C,WAAA,CAAYL,GAAA,CAAAvB,QAAA,EAAU;QAAA,EAAC;QAEzBhB,EADF,CAAAC,cAAA,eAAU,mBACuB;QAAAD,EAAA,CAAAE,MAAA,WAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/CH,EAAA,CAAAC,cAAA,oBAAwE;QAAjDD,EAAA,CAAA6C,gBAAA,2BAAAC,sDAAAC,MAAA;UAAA/C,EAAA,CAAA0C,aAAA,CAAAC,GAAA;UAAA3C,EAAA,CAAAgD,kBAAA,CAAAT,GAAA,CAAA1B,QAAA,CAAAL,IAAA,EAAAuC,MAAA,MAAAR,GAAA,CAAA1B,QAAA,CAAAL,IAAA,GAAAuC,MAAA;UAAA,OAAA/C,EAAA,CAAA4C,WAAA,CAAAG,MAAA;QAAA,EAA2B;QACpD/C,EAD0E,CAAAG,YAAA,EAAY,EAC3E;QAGTH,EADF,CAAAC,cAAA,gBAAU,oBACuB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChDH,EAAA,CAAAC,cAAA,oBAA2E;QAAnDD,EAAA,CAAA6C,gBAAA,2BAAAI,sDAAAF,MAAA;UAAA/C,EAAA,CAAA0C,aAAA,CAAAC,GAAA;UAAA3C,EAAA,CAAAgD,kBAAA,CAAAT,GAAA,CAAA1B,QAAA,CAAAJ,KAAA,EAAAsC,MAAA,MAAAR,GAAA,CAAA1B,QAAA,CAAAJ,KAAA,GAAAsC,MAAA;UAAA,OAAA/C,EAAA,CAAA4C,WAAA,CAAAG,MAAA;QAAA,EAA4B;QACtD/C,EAD6E,CAAAG,YAAA,EAAY,EAC9E;QAEXH,EAAA,CAAAC,cAAA,qBAAwE;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAC9EF,EAD8E,CAAAG,YAAA,EAAa,EACpF;QAGLH,EADF,CAAAC,cAAA,gBAAU,uBACS;QACfD,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAkB;QAClBH,EAAA,CAAAkD,UAAA,KAAAC,6BAAA,sBAAmC;QAIvCnD,EADE,CAAAG,YAAA,EAAW,EACC;;;;QAnBeH,EAAA,CAAAI,SAAA,IAA2B;QAA3BJ,EAAA,CAAAoD,gBAAA,YAAAb,GAAA,CAAA1B,QAAA,CAAAL,IAAA,CAA2B;QAK1BR,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAoD,gBAAA,YAAAb,GAAA,CAAA1B,QAAA,CAAAJ,KAAA,CAA4B;QAGdT,EAAA,CAAAI,SAAA,EAA+B;QAA/BJ,EAAA,CAAAqD,UAAA,cAAAC,SAAA,CAAAC,IAAA,CAAAC,KAAA,CAA+B;QAO7CxD,EAAA,CAAAI,SAAA,GAAO;QAAPJ,EAAA,CAAAqD,UAAA,YAAAd,GAAA,CAAAzB,IAAA,CAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}