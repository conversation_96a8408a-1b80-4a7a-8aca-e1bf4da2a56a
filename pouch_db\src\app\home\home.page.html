<ion-header>
  <ion-toolbar>
    <ion-title>
      PouchDB Form
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <form (ngSubmit)="onSubmit()" #myForm="ngForm">
    <ion-item>
      <ion-label position="floating">Name</ion-label>
      <ion-input type="text" [(ngModel)]="formData.name" name="name" required></ion-input>
    </ion-item>

    <ion-item>
      <ion-label position="floating">Email</ion-label>
      <ion-input type="email" [(ngModel)]="formData.email" name="email" required></ion-input>
    </ion-item>

    <ion-button expand="full" type="submit" [disabled]="!myForm.form.valid">Save</ion-button>
  </form>

  <ion-list>
    <ion-list-header>
      Saved Documents
    </ion-list-header>
    <ion-item *ngFor="let doc of docs">
      {{ doc.doc.name }} - {{ doc.doc.email }}
    </ion-item>
  </ion-list>
</ion-content>
