{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, m as printIonWarning, e as getIonMode, n as forceUpdate, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { c as createButtonActiveGesture } from './button-active-Bxcnevju.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\nimport './index-CfgBF1SE.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\n\n/**\n * iOS Alert Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([{\n    offset: 0,\n    opacity: '0.01',\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: '1',\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Alert Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Alert Enter Animation\n */\nconst mdEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([{\n    offset: 0,\n    opacity: '0.01',\n    transform: 'scale(0.9)'\n  }, {\n    offset: 1,\n    opacity: '1',\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(150).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Alert Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).fromTo('opacity', 0.99, 0);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(150).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst alertIosCss = \".sc-ion-alert-ios-h{--min-width:250px;--width:auto;--min-height:auto;--height:auto;--max-height:90%;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-alert-ios-h{display:none}.alert-top.sc-ion-alert-ios-h{padding-top:50px;-ms-flex-align:start;align-items:flex-start}.alert-wrapper.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:content;opacity:0;z-index:10}.alert-title.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-sub-title.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-weight:normal}.alert-message.sc-ion-alert-ios,.alert-input-group.sc-ion-alert-ios{-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-overflow-scrolling:touch;overflow-y:auto;overscroll-behavior-y:contain}.alert-checkbox-label.sc-ion-alert-ios,.alert-radio-label.sc-ion-alert-ios{overflow-wrap:anywhere}@media (any-pointer: coarse){.alert-checkbox-group.sc-ion-alert-ios::-webkit-scrollbar,.alert-radio-group.sc-ion-alert-ios::-webkit-scrollbar,.alert-message.sc-ion-alert-ios::-webkit-scrollbar{display:none}}.alert-input.sc-ion-alert-ios{padding-left:0;padding-right:0;padding-top:10px;padding-bottom:10px;width:100%;border:0;background:inherit;font:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.alert-button-group.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:100%}.alert-button-group-vertical.sc-ion-alert-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.alert-button.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;border:0;font-size:0.875rem;line-height:1.25rem;z-index:0}.alert-button.ion-focused.sc-ion-alert-ios,.alert-tappable.ion-focused.sc-ion-alert-ios{background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.alert-button-inner.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit}.alert-input-disabled.sc-ion-alert-ios,.alert-checkbox-button-disabled.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios,.alert-radio-button-disabled.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios{cursor:default;opacity:0.5;pointer-events:none}.alert-tappable.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;width:100%;border:0;background:transparent;font-size:inherit;line-height:initial;text-align:start;-webkit-appearance:none;-moz-appearance:none;appearance:none;contain:content}.alert-button.sc-ion-alert-ios,.alert-checkbox.sc-ion-alert-ios,.alert-input.sc-ion-alert-ios,.alert-radio.sc-ion-alert-ios{outline:none}.alert-radio-icon.sc-ion-alert-ios,.alert-checkbox-icon.sc-ion-alert-ios,.alert-checkbox-inner.sc-ion-alert-ios{-webkit-box-sizing:border-box;box-sizing:border-box}textarea.alert-input.sc-ion-alert-ios{min-height:37px;resize:none}.sc-ion-alert-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:clamp(270px, 16.875rem, 324px);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);font-size:max(14px, 0.875rem)}.alert-wrapper.sc-ion-alert-ios{border-radius:13px;-webkit-box-shadow:none;box-shadow:none;overflow:hidden}.alert-button.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios{pointer-events:none}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.alert-translucent.sc-ion-alert-ios-h .alert-wrapper.sc-ion-alert-ios{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.9);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.alert-head.sc-ion-alert-ios{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:7px;text-align:center}.alert-title.sc-ion-alert-ios{margin-top:8px;color:var(--ion-text-color, #000);font-size:max(17px, 1.0625rem);font-weight:600}.alert-sub-title.sc-ion-alert-ios{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:max(14px, 0.875rem)}.alert-message.sc-ion-alert-ios,.alert-input-group.sc-ion-alert-ios{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:21px;color:var(--ion-text-color, #000);font-size:max(13px, 0.8125rem);text-align:center}.alert-message.sc-ion-alert-ios{max-height:240px}.alert-message.sc-ion-alert-ios:empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:12px}.alert-input.sc-ion-alert-ios{border-radius:7px;margin-top:10px;-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:7px;padding-bottom:7px;border:0.55px solid var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf));background-color:var(--ion-background-color, #fff);-webkit-appearance:none;-moz-appearance:none;appearance:none;font-size:1rem}.alert-input.sc-ion-alert-ios::-webkit-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-moz-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios:-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-ms-clear{display:none}.alert-input.sc-ion-alert-ios::-webkit-date-and-time-value{height:18px}.alert-radio-group.sc-ion-alert-ios,.alert-checkbox-group.sc-ion-alert-ios{-ms-scroll-chaining:none;overscroll-behavior:contain;max-height:240px;border-top:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);overflow-y:auto;-webkit-overflow-scrolling:touch}.alert-tappable.sc-ion-alert-ios{min-height:44px}.alert-radio-label.sc-ion-alert-ios{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;-ms-flex-order:0;order:0;color:var(--ion-text-color, #000)}[aria-checked=true].sc-ion-alert-ios .alert-radio-label.sc-ion-alert-ios{color:var(--ion-color-primary, #0054e9)}.alert-radio-icon.sc-ion-alert-ios{position:relative;-ms-flex-order:1;order:1;min-width:30px}[aria-checked=true].sc-ion-alert-ios .alert-radio-inner.sc-ion-alert-ios{top:-7px;position:absolute;width:6px;height:12px;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:2px;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-ios .alert-radio-inner.sc-ion-alert-ios{inset-inline-start:7px}.alert-checkbox-label.sc-ion-alert-ios{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;color:var(--ion-text-color, #000)}.alert-checkbox-icon.sc-ion-alert-ios{border-radius:50%;-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:6px;margin-inline-end:6px;margin-top:10px;margin-bottom:10px;position:relative;width:min(1.375rem, 55.836px);height:min(1.375rem, 55.836px);border-width:0.125rem;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));background-color:var(--ion-item-background, var(--ion-background-color, #fff));contain:strict}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-icon.sc-ion-alert-ios{border-color:var(--ion-color-primary, #0054e9);background-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-inner.sc-ion-alert-ios{top:calc(min(1.375rem, 55.836px) / 8);position:absolute;width:calc(min(1.375rem, 55.836px) / 6 + 1px);height:calc(min(1.375rem, 55.836px) * 0.5);-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-background-color, #fff)}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-inner.sc-ion-alert-ios{inset-inline-start:calc(min(1.375rem, 55.836px) / 3)}.alert-button-group.sc-ion-alert-ios{-webkit-margin-end:-0.55px;margin-inline-end:-0.55px;-ms-flex-wrap:wrap;flex-wrap:wrap}.alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios{border-right:none}[dir=rtl].sc-ion-alert-ios-h .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:none}[dir=rtl].sc-ion-alert-ios .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:none}@supports selector(:dir(rtl)){.alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child:dir(rtl){border-right:none}}.alert-button.sc-ion-alert-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:0;-ms-flex:1 1 auto;flex:1 1 auto;min-width:50%;height:max(44px, 2.75rem);border-top:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);background-color:transparent;color:var(--ion-color-primary, #0054e9);font-size:max(17px, 1.0625rem);overflow:hidden}[dir=rtl].sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:first-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:first-child{border-right:0}[dir=rtl].sc-ion-alert-ios .alert-button.sc-ion-alert-ios:first-child{border-right:0}@supports selector(:dir(rtl)){.alert-button.sc-ion-alert-ios:first-child:dir(rtl){border-right:0}}.alert-button.sc-ion-alert-ios:last-child{border-right:0;font-weight:bold}[dir=rtl].sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:last-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:last-child{border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}[dir=rtl].sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}@supports selector(:dir(rtl)){.alert-button.sc-ion-alert-ios:last-child:dir(rtl){border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}}.alert-button.ion-activated.sc-ion-alert-ios{background-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1)}.alert-button-role-destructive.sc-ion-alert-ios,.alert-button-role-destructive.ion-activated.sc-ion-alert-ios,.alert-button-role-destructive.ion-focused.sc-ion-alert-ios{color:var(--ion-color-danger, #c5000f)}\";\nconst alertMdCss = \".sc-ion-alert-md-h{--min-width:250px;--width:auto;--min-height:auto;--height:auto;--max-height:90%;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-alert-md-h{display:none}.alert-top.sc-ion-alert-md-h{padding-top:50px;-ms-flex-align:start;align-items:flex-start}.alert-wrapper.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:content;opacity:0;z-index:10}.alert-title.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-sub-title.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-weight:normal}.alert-message.sc-ion-alert-md,.alert-input-group.sc-ion-alert-md{-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-overflow-scrolling:touch;overflow-y:auto;overscroll-behavior-y:contain}.alert-checkbox-label.sc-ion-alert-md,.alert-radio-label.sc-ion-alert-md{overflow-wrap:anywhere}@media (any-pointer: coarse){.alert-checkbox-group.sc-ion-alert-md::-webkit-scrollbar,.alert-radio-group.sc-ion-alert-md::-webkit-scrollbar,.alert-message.sc-ion-alert-md::-webkit-scrollbar{display:none}}.alert-input.sc-ion-alert-md{padding-left:0;padding-right:0;padding-top:10px;padding-bottom:10px;width:100%;border:0;background:inherit;font:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.alert-button-group.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:100%}.alert-button-group-vertical.sc-ion-alert-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.alert-button.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;border:0;font-size:0.875rem;line-height:1.25rem;z-index:0}.alert-button.ion-focused.sc-ion-alert-md,.alert-tappable.ion-focused.sc-ion-alert-md{background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.alert-button-inner.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit}.alert-input-disabled.sc-ion-alert-md,.alert-checkbox-button-disabled.sc-ion-alert-md .alert-button-inner.sc-ion-alert-md,.alert-radio-button-disabled.sc-ion-alert-md .alert-button-inner.sc-ion-alert-md{cursor:default;opacity:0.5;pointer-events:none}.alert-tappable.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;width:100%;border:0;background:transparent;font-size:inherit;line-height:initial;text-align:start;-webkit-appearance:none;-moz-appearance:none;appearance:none;contain:content}.alert-button.sc-ion-alert-md,.alert-checkbox.sc-ion-alert-md,.alert-input.sc-ion-alert-md,.alert-radio.sc-ion-alert-md{outline:none}.alert-radio-icon.sc-ion-alert-md,.alert-checkbox-icon.sc-ion-alert-md,.alert-checkbox-inner.sc-ion-alert-md{-webkit-box-sizing:border-box;box-sizing:border-box}textarea.alert-input.sc-ion-alert-md{min-height:37px;resize:none}.sc-ion-alert-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--max-width:280px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);font-size:0.875rem}.alert-wrapper.sc-ion-alert-md{border-radius:4px;-webkit-box-shadow:0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12);box-shadow:0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12)}.alert-head.sc-ion-alert-md{-webkit-padding-start:23px;padding-inline-start:23px;-webkit-padding-end:23px;padding-inline-end:23px;padding-top:20px;padding-bottom:15px;text-align:start}.alert-title.sc-ion-alert-md{color:var(--ion-text-color, #000);font-size:1.25rem;font-weight:500}.alert-sub-title.sc-ion-alert-md{color:var(--ion-text-color, #000);font-size:1rem}.alert-message.sc-ion-alert-md,.alert-input-group.sc-ion-alert-md{-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:20px;padding-bottom:20px;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.alert-message.sc-ion-alert-md{font-size:1rem}@media screen and (max-width: 767px){.alert-message.sc-ion-alert-md{max-height:266px}}.alert-message.sc-ion-alert-md:empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-head.sc-ion-alert-md+.alert-message.sc-ion-alert-md{padding-top:0}.alert-input.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;border-bottom:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));color:var(--ion-text-color, #000)}.alert-input.sc-ion-alert-md::-webkit-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-moz-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md:-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-ms-clear{display:none}.alert-input.sc-ion-alert-md:focus{margin-bottom:4px;border-bottom:2px solid var(--ion-color-primary, #0054e9)}.alert-radio-group.sc-ion-alert-md,.alert-checkbox-group.sc-ion-alert-md{position:relative;border-top:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));border-bottom:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));overflow:auto}@media screen and (max-width: 767px){.alert-radio-group.sc-ion-alert-md,.alert-checkbox-group.sc-ion-alert-md{max-height:266px}}.alert-tappable.sc-ion-alert-md{position:relative;min-height:48px}.alert-radio-label.sc-ion-alert-md{-webkit-padding-start:52px;padding-inline-start:52px;-webkit-padding-end:26px;padding-inline-end:26px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:1rem}.alert-radio-icon.sc-ion-alert-md{top:0;border-radius:50%;display:block;position:relative;width:20px;height:20px;border-width:2px;border-style:solid;border-color:var(--ion-color-step-550, var(--ion-background-color-step-550, #737373))}.alert-radio-icon.sc-ion-alert-md{inset-inline-start:26px}.alert-radio-inner.sc-ion-alert-md{top:3px;border-radius:50%;position:absolute;width:10px;height:10px;-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--ion-color-primary, #0054e9)}.alert-radio-inner.sc-ion-alert-md{inset-inline-start:3px}[aria-checked=true].sc-ion-alert-md .alert-radio-label.sc-ion-alert-md{color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626))}[aria-checked=true].sc-ion-alert-md .alert-radio-icon.sc-ion-alert-md{border-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-md .alert-radio-inner.sc-ion-alert-md{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}.alert-checkbox-label.sc-ion-alert-md{-webkit-padding-start:53px;padding-inline-start:53px;-webkit-padding-end:26px;padding-inline-end:26px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;width:calc(100% - 53px);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:1rem}.alert-checkbox-icon.sc-ion-alert-md{top:0;border-radius:2px;position:relative;width:16px;height:16px;border-width:2px;border-style:solid;border-color:var(--ion-color-step-550, var(--ion-background-color-step-550, #737373));contain:strict}.alert-checkbox-icon.sc-ion-alert-md{inset-inline-start:26px}[aria-checked=true].sc-ion-alert-md .alert-checkbox-icon.sc-ion-alert-md{border-color:var(--ion-color-primary, #0054e9);background-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-md .alert-checkbox-inner.sc-ion-alert-md{top:0;position:absolute;width:6px;height:10px;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:2px;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-color-primary-contrast, #fff)}[aria-checked=true].sc-ion-alert-md .alert-checkbox-inner.sc-ion-alert-md{inset-inline-start:3px}.alert-button-group.sc-ion-alert-md{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-wrap:wrap-reverse;flex-wrap:wrap-reverse;-ms-flex-pack:end;justify-content:flex-end}.alert-button.sc-ion-alert-md{border-radius:2px;-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:0;margin-bottom:0;-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;position:relative;background-color:transparent;color:var(--ion-color-primary, #0054e9);font-weight:500;text-align:end;text-transform:uppercase;overflow:hidden}.alert-button-inner.sc-ion-alert-md{-ms-flex-pack:end;justify-content:flex-end}@media screen and (min-width: 768px){.sc-ion-alert-md-h{--max-width:min(100vw - 96px, 560px);--max-height:min(100vh - 96px, 560px)}}\";\nconst Alert = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionAlertDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionAlertWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionAlertWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionAlertDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.processedInputs = [];\n    this.processedButtons = [];\n    this.presented = false;\n    /** @internal */\n    this.hasController = false;\n    /**\n     * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n     */\n    this.keyboardClose = true;\n    /**\n     * Array of buttons to be added to the alert.\n     */\n    this.buttons = [];\n    /**\n     * Array of input to show in the alert.\n     */\n    this.inputs = [];\n    /**\n     * If `true`, the alert will be dismissed when the backdrop is clicked.\n     */\n    this.backdropDismiss = true;\n    /**\n     * If `true`, the alert will be translucent.\n     * Only applies when the mode is `\"ios\"` and the device supports\n     * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n     */\n    this.translucent = false;\n    /**\n     * If `true`, the alert will animate.\n     */\n    this.animated = true;\n    /**\n     * If `true`, the alert will open. If `false`, the alert will close.\n     * Use this if you need finer grained control over presentation, otherwise\n     * just use the alertController or the `trigger` property.\n     * Note: `isOpen` will not automatically be set back to `false` when\n     * the alert dismisses. You will need to do that in your code.\n     */\n    this.isOpen = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.processedButtons.find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  onKeydown(ev) {\n    var _a;\n    const inputTypes = new Set(this.processedInputs.map(i => i.type));\n    /**\n     * Based on keyboard navigation requirements, the\n     * checkbox should not respond to the enter keydown event.\n     */\n    if (inputTypes.has('checkbox') && ev.key === 'Enter') {\n      ev.preventDefault();\n      return;\n    }\n    /**\n     * Ensure when alert container is being focused, and the user presses the tab + shift keys, the focus will be set to the last alert button.\n     */\n    if (ev.target.classList.contains('alert-wrapper')) {\n      if (ev.key === 'Tab' && ev.shiftKey) {\n        ev.preventDefault();\n        const lastChildBtn = (_a = this.wrapperEl) === null || _a === void 0 ? void 0 : _a.querySelector('.alert-button:last-child');\n        lastChildBtn.focus();\n        return;\n      }\n    }\n    // The only inputs we want to navigate between using arrow keys are the radios\n    // ignore the keydown event if it is not on a radio button\n    if (!inputTypes.has('radio') || ev.target && !this.el.contains(ev.target) || ev.target.classList.contains('alert-button')) {\n      return;\n    }\n    // Get all radios inside of the radio group and then\n    // filter out disabled radios since we need to skip those\n    const query = this.el.querySelectorAll('.alert-radio');\n    const radios = Array.from(query).filter(radio => !radio.disabled);\n    // The focused radio is the one that shares the same id as\n    // the event target\n    const index = radios.findIndex(radio => radio.id === ev.target.id);\n    // We need to know what the next radio element should\n    // be in order to change the focus\n    let nextEl;\n    // If hitting arrow down or arrow right, move to the next radio\n    // If we're on the last radio, move to the first radio\n    if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n      nextEl = index === radios.length - 1 ? radios[0] : radios[index + 1];\n    }\n    // If hitting arrow up or arrow left, move to the previous radio\n    // If we're on the first radio, move to the last radio\n    if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n      nextEl = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n    }\n    if (nextEl && radios.includes(nextEl)) {\n      const nextProcessed = this.processedInputs.find(input => input.id === (nextEl === null || nextEl === void 0 ? void 0 : nextEl.id));\n      if (nextProcessed) {\n        this.rbClick(nextProcessed);\n        nextEl.focus();\n      }\n    }\n  }\n  buttonsChanged() {\n    const buttons = this.buttons;\n    this.processedButtons = buttons.map(btn => {\n      return typeof btn === 'string' ? {\n        text: btn,\n        role: btn.toLowerCase() === 'cancel' ? 'cancel' : undefined\n      } : btn;\n    });\n  }\n  inputsChanged() {\n    const inputs = this.inputs;\n    // Get the first input that is not disabled and the checked one\n    // If an enabled checked input exists, set it to be the focusable input\n    // otherwise we default to focus the first input\n    // This will only be used when the input is type radio\n    const first = inputs.find(input => !input.disabled);\n    const checked = inputs.find(input => input.checked && !input.disabled);\n    const focusable = checked || first;\n    // An alert can be created with several different inputs. Radios,\n    // checkboxes and inputs are all accepted, but they cannot be mixed.\n    const inputTypes = new Set(inputs.map(i => i.type));\n    if (inputTypes.has('checkbox') && inputTypes.has('radio')) {\n      printIonWarning(`[ion-alert] - Alert cannot mix input types: ${Array.from(inputTypes.values()).join('/')}. Please see alert docs for more info.`);\n    }\n    this.inputType = inputTypes.values().next().value;\n    this.processedInputs = inputs.map((i, index) => {\n      var _a;\n      return {\n        type: i.type || 'text',\n        name: i.name || `${index}`,\n        placeholder: i.placeholder || '',\n        value: i.value,\n        label: i.label,\n        checked: !!i.checked,\n        disabled: !!i.disabled,\n        id: i.id || `alert-input-${this.overlayIndex}-${index}`,\n        handler: i.handler,\n        min: i.min,\n        max: i.max,\n        cssClass: (_a = i.cssClass) !== null && _a !== void 0 ? _a : '',\n        attributes: i.attributes || {},\n        tabindex: i.type === 'radio' && i !== focusable ? -1 : 0\n      };\n    });\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  componentWillLoad() {\n    var _a;\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n    this.inputsChanged();\n    this.buttonsChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  componentDidLoad() {\n    /**\n     * Only create gesture if:\n     * 1. A gesture does not already exist\n     * 2. App is running in iOS mode\n     * 3. A wrapper ref exists\n     */\n    if (!this.gesture && getIonMode(this) === 'ios' && this.wrapperEl) {\n      this.gesture = createButtonActiveGesture(this.wrapperEl, refEl => refEl.classList.contains('alert-button'));\n      this.gesture.enable(true);\n    }\n    /**\n     * If alert was rendered with isOpen=\"true\"\n     * then we should open alert immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Present the alert overlay after it has been created.\n   */\n  present() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this.lockController.lock();\n      yield _this.delegateController.attachViewToDom();\n      yield present(_this, 'alertEnter', iosEnterAnimation, mdEnterAnimation).then(() => {\n        var _a, _b;\n        /**\n         * Check if alert has only one button and no inputs.\n         * If so, then focus on the button. Otherwise, focus the alert wrapper.\n         * This will map to the default native alert behavior.\n         */\n        if (_this.buttons.length === 1 && _this.inputs.length === 0) {\n          const queryBtn = (_a = _this.wrapperEl) === null || _a === void 0 ? void 0 : _a.querySelector('.alert-button');\n          queryBtn.focus();\n        } else {\n          (_b = _this.wrapperEl) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n      });\n      unlock();\n    })();\n  }\n  /**\n   * Dismiss the alert overlay after it has been presented.\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the alert.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the alert. Some examples include:\n   * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n   */\n  dismiss(data, role) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this2.lockController.lock();\n      const dismissed = yield dismiss(_this2, data, role, 'alertLeave', iosLeaveAnimation, mdLeaveAnimation);\n      if (dismissed) {\n        _this2.delegateController.removeViewFromDom();\n      }\n      unlock();\n      return dismissed;\n    })();\n  }\n  /**\n   * Returns a promise that resolves when the alert did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionAlertDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the alert will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionAlertWillDismiss');\n  }\n  rbClick(selectedInput) {\n    for (const input of this.processedInputs) {\n      input.checked = input === selectedInput;\n      input.tabindex = input === selectedInput ? 0 : -1;\n    }\n    this.activeId = selectedInput.id;\n    safeCall(selectedInput.handler, selectedInput);\n    forceUpdate(this);\n  }\n  cbClick(selectedInput) {\n    selectedInput.checked = !selectedInput.checked;\n    safeCall(selectedInput.handler, selectedInput);\n    forceUpdate(this);\n  }\n  buttonClick(button) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const role = button.role;\n      const values = _this3.getValues();\n      if (isCancel(role)) {\n        return _this3.dismiss({\n          values\n        }, role);\n      }\n      const returnData = yield _this3.callButtonHandler(button, values);\n      if (returnData !== false) {\n        return _this3.dismiss(Object.assign({\n          values\n        }, returnData), button.role);\n      }\n      return false;\n    })();\n  }\n  callButtonHandler(button, data) {\n    return _asyncToGenerator(function* () {\n      if (button === null || button === void 0 ? void 0 : button.handler) {\n        // a handler has been provided, execute it\n        // pass the handler the values from the inputs\n        const returnData = yield safeCall(button.handler, data);\n        if (returnData === false) {\n          // if the return value of the handler is false then do not dismiss\n          return false;\n        }\n        if (typeof returnData === 'object') {\n          return returnData;\n        }\n      }\n      return {};\n    })();\n  }\n  getValues() {\n    if (this.processedInputs.length === 0) {\n      // this is an alert without any options/inputs at all\n      return undefined;\n    }\n    if (this.inputType === 'radio') {\n      // this is an alert with radio buttons (single value select)\n      // return the one value which is checked, otherwise undefined\n      const checkedInput = this.processedInputs.find(i => !!i.checked);\n      return checkedInput ? checkedInput.value : undefined;\n    }\n    if (this.inputType === 'checkbox') {\n      // this is an alert with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return this.processedInputs.filter(i => i.checked).map(i => i.value);\n    }\n    // this is an alert with text inputs\n    // return an object of all the values with the input name as the key\n    const values = {};\n    this.processedInputs.forEach(i => {\n      values[i.name] = i.value || '';\n    });\n    return values;\n  }\n  renderAlertInputs() {\n    switch (this.inputType) {\n      case 'checkbox':\n        return this.renderCheckbox();\n      case 'radio':\n        return this.renderRadio();\n      default:\n        return this.renderInput();\n    }\n  }\n  renderCheckbox() {\n    const inputs = this.processedInputs;\n    const mode = getIonMode(this);\n    if (inputs.length === 0) {\n      return null;\n    }\n    return h(\"div\", {\n      class: \"alert-checkbox-group\"\n    }, inputs.map(i => h(\"button\", {\n      type: \"button\",\n      onClick: () => this.cbClick(i),\n      \"aria-checked\": `${i.checked}`,\n      id: i.id,\n      disabled: i.disabled,\n      tabIndex: i.tabindex,\n      role: \"checkbox\",\n      class: Object.assign(Object.assign({}, getClassMap(i.cssClass)), {\n        'alert-tappable': true,\n        'alert-checkbox': true,\n        'alert-checkbox-button': true,\n        'ion-focusable': true,\n        'alert-checkbox-button-disabled': i.disabled || false\n      })\n    }, h(\"div\", {\n      class: \"alert-button-inner\"\n    }, h(\"div\", {\n      class: \"alert-checkbox-icon\"\n    }, h(\"div\", {\n      class: \"alert-checkbox-inner\"\n    })), h(\"div\", {\n      class: \"alert-checkbox-label\"\n    }, i.label)), mode === 'md' && h(\"ion-ripple-effect\", null))));\n  }\n  renderRadio() {\n    const inputs = this.processedInputs;\n    if (inputs.length === 0) {\n      return null;\n    }\n    return h(\"div\", {\n      class: \"alert-radio-group\",\n      role: \"radiogroup\",\n      \"aria-activedescendant\": this.activeId\n    }, inputs.map(i => h(\"button\", {\n      type: \"button\",\n      onClick: () => this.rbClick(i),\n      \"aria-checked\": `${i.checked}`,\n      disabled: i.disabled,\n      id: i.id,\n      tabIndex: i.tabindex,\n      class: Object.assign(Object.assign({}, getClassMap(i.cssClass)), {\n        'alert-radio-button': true,\n        'alert-tappable': true,\n        'alert-radio': true,\n        'ion-focusable': true,\n        'alert-radio-button-disabled': i.disabled || false\n      }),\n      role: \"radio\"\n    }, h(\"div\", {\n      class: \"alert-button-inner\"\n    }, h(\"div\", {\n      class: \"alert-radio-icon\"\n    }, h(\"div\", {\n      class: \"alert-radio-inner\"\n    })), h(\"div\", {\n      class: \"alert-radio-label\"\n    }, i.label)))));\n  }\n  renderInput() {\n    const inputs = this.processedInputs;\n    if (inputs.length === 0) {\n      return null;\n    }\n    return h(\"div\", {\n      class: \"alert-input-group\"\n    }, inputs.map(i => {\n      var _a, _b, _c, _d;\n      if (i.type === 'textarea') {\n        return h(\"div\", {\n          class: \"alert-input-wrapper\"\n        }, h(\"textarea\", Object.assign({\n          placeholder: i.placeholder,\n          value: i.value,\n          id: i.id,\n          tabIndex: i.tabindex\n        }, i.attributes, {\n          disabled: (_b = (_a = i.attributes) === null || _a === void 0 ? void 0 : _a.disabled) !== null && _b !== void 0 ? _b : i.disabled,\n          class: inputClass(i),\n          onInput: e => {\n            var _a;\n            i.value = e.target.value;\n            if ((_a = i.attributes) === null || _a === void 0 ? void 0 : _a.onInput) {\n              i.attributes.onInput(e);\n            }\n          }\n        })));\n      } else {\n        return h(\"div\", {\n          class: \"alert-input-wrapper\"\n        }, h(\"input\", Object.assign({\n          placeholder: i.placeholder,\n          type: i.type,\n          min: i.min,\n          max: i.max,\n          value: i.value,\n          id: i.id,\n          tabIndex: i.tabindex\n        }, i.attributes, {\n          disabled: (_d = (_c = i.attributes) === null || _c === void 0 ? void 0 : _c.disabled) !== null && _d !== void 0 ? _d : i.disabled,\n          class: inputClass(i),\n          onInput: e => {\n            var _a;\n            i.value = e.target.value;\n            if ((_a = i.attributes) === null || _a === void 0 ? void 0 : _a.onInput) {\n              i.attributes.onInput(e);\n            }\n          }\n        })));\n      }\n    }));\n  }\n  renderAlertButtons() {\n    const buttons = this.processedButtons;\n    const mode = getIonMode(this);\n    const alertButtonGroupClass = {\n      'alert-button-group': true,\n      'alert-button-group-vertical': buttons.length > 2\n    };\n    return h(\"div\", {\n      class: alertButtonGroupClass\n    }, buttons.map(button => h(\"button\", Object.assign({}, button.htmlAttributes, {\n      type: \"button\",\n      id: button.id,\n      class: buttonClass(button),\n      tabIndex: 0,\n      onClick: () => this.buttonClick(button)\n    }), h(\"span\", {\n      class: \"alert-button-inner\"\n    }, button.text), mode === 'md' && h(\"ion-ripple-effect\", null))));\n  }\n  renderAlertMessage(msgId) {\n    const {\n      customHTMLEnabled,\n      message\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        id: msgId,\n        class: \"alert-message\",\n        innerHTML: sanitizeDOMString(message)\n      });\n    }\n    return h(\"div\", {\n      id: msgId,\n      class: \"alert-message\"\n    }, message);\n  }\n  render() {\n    const {\n      overlayIndex,\n      header,\n      subHeader,\n      message,\n      htmlAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const hdrId = `alert-${overlayIndex}-hdr`;\n    const msgId = `alert-${overlayIndex}-msg`;\n    const subHdrId = `alert-${overlayIndex}-sub-hdr`;\n    const role = this.inputs.length > 0 || this.buttons.length > 0 ? 'alertdialog' : 'alert';\n    /**\n     * Use both the header and subHeader ids if they are defined.\n     * If only the header is defined, use the header id.\n     * If only the subHeader is defined, use the subHeader id.\n     * If neither are defined, do not set aria-labelledby.\n     */\n    const ariaLabelledBy = header && subHeader ? `${hdrId} ${subHdrId}` : header ? hdrId : subHeader ? subHdrId : null;\n    return h(Host, {\n      key: '6025440b9cd369d4fac89e7e4296c84a10a0b8e0',\n      tabindex: \"-1\",\n      style: {\n        zIndex: `${20000 + overlayIndex}`\n      },\n      class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), {\n        [mode]: true,\n        'overlay-hidden': true,\n        'alert-translucent': this.translucent\n      }),\n      onIonAlertWillDismiss: this.dispatchCancelHandler,\n      onIonBackdropTap: this.onBackdropTap\n    }, h(\"ion-backdrop\", {\n      key: '3cd5ca8b99cb95b11dd22ab41a820d841142896f',\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: '4cc62ae6e21424057d22aeef1e8fc77011e77cd5',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", Object.assign({\n      key: '364057a69f25aa88904df17bdcf7e5bf714e7830',\n      class: \"alert-wrapper ion-overlay-wrapper\",\n      role: role,\n      \"aria-modal\": \"true\",\n      \"aria-labelledby\": ariaLabelledBy,\n      \"aria-describedby\": message !== undefined ? msgId : null,\n      tabindex: \"0\",\n      ref: el => this.wrapperEl = el\n    }, htmlAttributes), h(\"div\", {\n      key: '78694e3c0db2d408df3899fb1a90859bcc8d14cc',\n      class: \"alert-head\"\n    }, header && h(\"h2\", {\n      key: 'ec88ff3e4e1ea871b5975133fdcf4cac38b05e0f',\n      id: hdrId,\n      class: \"alert-title\"\n    }, header), subHeader && !header && h(\"h2\", {\n      key: '9b09bc8bb68af255ef8b7d22587acc946148e544',\n      id: subHdrId,\n      class: \"alert-sub-title\"\n    }, subHeader), subHeader && header && h(\"h3\", {\n      key: '99abe815f75d2df7f1b77c0df9f3436724fea76f',\n      id: subHdrId,\n      class: \"alert-sub-title\"\n    }, subHeader)), this.renderAlertMessage(msgId), this.renderAlertInputs(), this.renderAlertButtons()), h(\"div\", {\n      key: 'a43d0c22c0e46b1ef911f92ffeb253d7911b85f7',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"],\n      \"buttons\": [\"buttonsChanged\"],\n      \"inputs\": [\"inputsChanged\"]\n    };\n  }\n};\nconst inputClass = input => {\n  var _a, _b, _c;\n  return Object.assign(Object.assign({\n    'alert-input': true,\n    'alert-input-disabled': ((_b = (_a = input.attributes) === null || _a === void 0 ? void 0 : _a.disabled) !== null && _b !== void 0 ? _b : input.disabled) || false\n  }, getClassMap(input.cssClass)), getClassMap(input.attributes ? (_c = input.attributes.class) === null || _c === void 0 ? void 0 : _c.toString() : ''));\n};\nconst buttonClass = button => {\n  return Object.assign({\n    'alert-button': true,\n    'ion-focusable': true,\n    'ion-activatable': true,\n    [`alert-button-role-${button.role}`]: button.role !== undefined\n  }, getClassMap(button.cssClass));\n};\nAlert.style = {\n  ios: alertIosCss,\n  md: alertMdCss\n};\nexport { Alert as ion_alert };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "l", "config", "m", "printIonWarning", "e", "getIonMode", "n", "forceUpdate", "h", "j", "Host", "k", "getElement", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "c", "createButtonActiveGesture", "raf", "createLockController", "createDelegateController", "createTriggerController", "B", "BACKDROP", "i", "isCancel", "prepareOverlay", "setOverlayId", "f", "present", "g", "dismiss", "eventMethod", "s", "safeCall", "getClassMap", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "keyframes", "offset", "opacity", "transform", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "alertIosCss", "alertMdCss", "<PERSON><PERSON>", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "customHTMLEnabled", "get", "processedInputs", "processedButtons", "presented", "hasController", "keyboardClose", "buttons", "inputs", "<PERSON><PERSON><PERSON><PERSON>", "translucent", "animated", "isOpen", "onBackdropTap", "undefined", "dispatchCancelHandler", "ev", "role", "detail", "cancelButton", "find", "b", "callButtonHandler", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "trigger", "el", "addClickListener", "onKeydown", "_a", "inputTypes", "Set", "map", "type", "has", "key", "preventDefault", "target", "classList", "contains", "shift<PERSON>ey", "lastChildBtn", "wrapperEl", "focus", "query", "querySelectorAll", "radios", "Array", "from", "filter", "radio", "disabled", "index", "findIndex", "id", "nextEl", "includes", "length", "nextProcessed", "input", "rbClick", "buttonsChanged", "btn", "text", "toLowerCase", "inputsChanged", "first", "checked", "focusable", "values", "join", "inputType", "next", "value", "name", "placeholder", "label", "overlayIndex", "handler", "min", "max", "cssClass", "attributes", "tabindex", "connectedCallback", "componentWillLoad", "htmlAttributes", "disconnectedCallback", "removeClickListener", "gesture", "destroy", "componentDidLoad", "refEl", "enable", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "then", "_b", "queryBtn", "data", "_this2", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "selectedInput", "activeId", "cbClick", "buttonClick", "button", "_this3", "getV<PERSON>ues", "returnData", "Object", "assign", "checkedInput", "for<PERSON>ach", "renderAlertInputs", "renderCheckbox", "renderRadio", "renderInput", "mode", "class", "onClick", "tabIndex", "_c", "_d", "inputClass", "onInput", "renderAlertButtons", "alertButtonGroupClass", "buttonClass", "renderAlertMessage", "msgId", "message", "innerHTML", "render", "header", "subHeader", "hdrId", "subHdrId", "ariaLabelledBy", "style", "zIndex", "onIonAlertWillDismiss", "onIonBackdropTap", "tappable", "ref", "watchers", "toString", "ios", "md", "ion_alert"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-alert.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, m as printIonWarning, e as getIonMode, n as forceUpdate, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { c as createButtonActiveGesture } from './button-active-Bxcnevju.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\nimport './index-CfgBF1SE.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\n\n/**\n * iOS Alert Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([\n        { offset: 0, opacity: '0.01', transform: 'scale(1.1)' },\n        { offset: 1, opacity: '1', transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Alert Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([\n        { offset: 0, opacity: 0.99, transform: 'scale(1)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.9)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Alert Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).keyframes([\n        { offset: 0, opacity: '0.01', transform: 'scale(0.9)' },\n        { offset: 1, opacity: '1', transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(150)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Alert Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.alert-wrapper')).fromTo('opacity', 0.99, 0);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(150)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst alertIosCss = \".sc-ion-alert-ios-h{--min-width:250px;--width:auto;--min-height:auto;--height:auto;--max-height:90%;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-alert-ios-h{display:none}.alert-top.sc-ion-alert-ios-h{padding-top:50px;-ms-flex-align:start;align-items:flex-start}.alert-wrapper.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:content;opacity:0;z-index:10}.alert-title.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-sub-title.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-weight:normal}.alert-message.sc-ion-alert-ios,.alert-input-group.sc-ion-alert-ios{-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-overflow-scrolling:touch;overflow-y:auto;overscroll-behavior-y:contain}.alert-checkbox-label.sc-ion-alert-ios,.alert-radio-label.sc-ion-alert-ios{overflow-wrap:anywhere}@media (any-pointer: coarse){.alert-checkbox-group.sc-ion-alert-ios::-webkit-scrollbar,.alert-radio-group.sc-ion-alert-ios::-webkit-scrollbar,.alert-message.sc-ion-alert-ios::-webkit-scrollbar{display:none}}.alert-input.sc-ion-alert-ios{padding-left:0;padding-right:0;padding-top:10px;padding-bottom:10px;width:100%;border:0;background:inherit;font:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.alert-button-group.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:100%}.alert-button-group-vertical.sc-ion-alert-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.alert-button.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;border:0;font-size:0.875rem;line-height:1.25rem;z-index:0}.alert-button.ion-focused.sc-ion-alert-ios,.alert-tappable.ion-focused.sc-ion-alert-ios{background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.alert-button-inner.sc-ion-alert-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit}.alert-input-disabled.sc-ion-alert-ios,.alert-checkbox-button-disabled.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios,.alert-radio-button-disabled.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios{cursor:default;opacity:0.5;pointer-events:none}.alert-tappable.sc-ion-alert-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;width:100%;border:0;background:transparent;font-size:inherit;line-height:initial;text-align:start;-webkit-appearance:none;-moz-appearance:none;appearance:none;contain:content}.alert-button.sc-ion-alert-ios,.alert-checkbox.sc-ion-alert-ios,.alert-input.sc-ion-alert-ios,.alert-radio.sc-ion-alert-ios{outline:none}.alert-radio-icon.sc-ion-alert-ios,.alert-checkbox-icon.sc-ion-alert-ios,.alert-checkbox-inner.sc-ion-alert-ios{-webkit-box-sizing:border-box;box-sizing:border-box}textarea.alert-input.sc-ion-alert-ios{min-height:37px;resize:none}.sc-ion-alert-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:clamp(270px, 16.875rem, 324px);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);font-size:max(14px, 0.875rem)}.alert-wrapper.sc-ion-alert-ios{border-radius:13px;-webkit-box-shadow:none;box-shadow:none;overflow:hidden}.alert-button.sc-ion-alert-ios .alert-button-inner.sc-ion-alert-ios{pointer-events:none}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.alert-translucent.sc-ion-alert-ios-h .alert-wrapper.sc-ion-alert-ios{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.9);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.alert-head.sc-ion-alert-ios{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:7px;text-align:center}.alert-title.sc-ion-alert-ios{margin-top:8px;color:var(--ion-text-color, #000);font-size:max(17px, 1.0625rem);font-weight:600}.alert-sub-title.sc-ion-alert-ios{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:max(14px, 0.875rem)}.alert-message.sc-ion-alert-ios,.alert-input-group.sc-ion-alert-ios{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:21px;color:var(--ion-text-color, #000);font-size:max(13px, 0.8125rem);text-align:center}.alert-message.sc-ion-alert-ios{max-height:240px}.alert-message.sc-ion-alert-ios:empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:12px}.alert-input.sc-ion-alert-ios{border-radius:7px;margin-top:10px;-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:7px;padding-bottom:7px;border:0.55px solid var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf));background-color:var(--ion-background-color, #fff);-webkit-appearance:none;-moz-appearance:none;appearance:none;font-size:1rem}.alert-input.sc-ion-alert-ios::-webkit-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-moz-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios:-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-ios::-ms-clear{display:none}.alert-input.sc-ion-alert-ios::-webkit-date-and-time-value{height:18px}.alert-radio-group.sc-ion-alert-ios,.alert-checkbox-group.sc-ion-alert-ios{-ms-scroll-chaining:none;overscroll-behavior:contain;max-height:240px;border-top:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);overflow-y:auto;-webkit-overflow-scrolling:touch}.alert-tappable.sc-ion-alert-ios{min-height:44px}.alert-radio-label.sc-ion-alert-ios{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;-ms-flex-order:0;order:0;color:var(--ion-text-color, #000)}[aria-checked=true].sc-ion-alert-ios .alert-radio-label.sc-ion-alert-ios{color:var(--ion-color-primary, #0054e9)}.alert-radio-icon.sc-ion-alert-ios{position:relative;-ms-flex-order:1;order:1;min-width:30px}[aria-checked=true].sc-ion-alert-ios .alert-radio-inner.sc-ion-alert-ios{top:-7px;position:absolute;width:6px;height:12px;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:2px;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-ios .alert-radio-inner.sc-ion-alert-ios{inset-inline-start:7px}.alert-checkbox-label.sc-ion-alert-ios{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;color:var(--ion-text-color, #000)}.alert-checkbox-icon.sc-ion-alert-ios{border-radius:50%;-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:6px;margin-inline-end:6px;margin-top:10px;margin-bottom:10px;position:relative;width:min(1.375rem, 55.836px);height:min(1.375rem, 55.836px);border-width:0.125rem;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));background-color:var(--ion-item-background, var(--ion-background-color, #fff));contain:strict}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-icon.sc-ion-alert-ios{border-color:var(--ion-color-primary, #0054e9);background-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-inner.sc-ion-alert-ios{top:calc(min(1.375rem, 55.836px) / 8);position:absolute;width:calc(min(1.375rem, 55.836px) / 6 + 1px);height:calc(min(1.375rem, 55.836px) * 0.5);-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-background-color, #fff)}[aria-checked=true].sc-ion-alert-ios .alert-checkbox-inner.sc-ion-alert-ios{inset-inline-start:calc(min(1.375rem, 55.836px) / 3)}.alert-button-group.sc-ion-alert-ios{-webkit-margin-end:-0.55px;margin-inline-end:-0.55px;-ms-flex-wrap:wrap;flex-wrap:wrap}.alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios{border-right:none}[dir=rtl].sc-ion-alert-ios-h .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:none}[dir=rtl].sc-ion-alert-ios .alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:none}@supports selector(:dir(rtl)){.alert-button-group-vertical.sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child:dir(rtl){border-right:none}}.alert-button.sc-ion-alert-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:0;-ms-flex:1 1 auto;flex:1 1 auto;min-width:50%;height:max(44px, 2.75rem);border-top:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2);background-color:transparent;color:var(--ion-color-primary, #0054e9);font-size:max(17px, 1.0625rem);overflow:hidden}[dir=rtl].sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:first-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:first-child{border-right:0}[dir=rtl].sc-ion-alert-ios .alert-button.sc-ion-alert-ios:first-child{border-right:0}@supports selector(:dir(rtl)){.alert-button.sc-ion-alert-ios:first-child:dir(rtl){border-right:0}}.alert-button.sc-ion-alert-ios:last-child{border-right:0;font-weight:bold}[dir=rtl].sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:last-child,[dir=rtl] .sc-ion-alert-ios-h .alert-button.sc-ion-alert-ios:last-child{border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}[dir=rtl].sc-ion-alert-ios .alert-button.sc-ion-alert-ios:last-child{border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}@supports selector(:dir(rtl)){.alert-button.sc-ion-alert-ios:last-child:dir(rtl){border-right:0.55px solid rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.2)}}.alert-button.ion-activated.sc-ion-alert-ios{background-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1)}.alert-button-role-destructive.sc-ion-alert-ios,.alert-button-role-destructive.ion-activated.sc-ion-alert-ios,.alert-button-role-destructive.ion-focused.sc-ion-alert-ios{color:var(--ion-color-danger, #c5000f)}\";\n\nconst alertMdCss = \".sc-ion-alert-md-h{--min-width:250px;--width:auto;--min-height:auto;--height:auto;--max-height:90%;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-alert-md-h{display:none}.alert-top.sc-ion-alert-md-h{padding-top:50px;-ms-flex-align:start;align-items:flex-start}.alert-wrapper.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:content;opacity:0;z-index:10}.alert-title.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-sub-title.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-weight:normal}.alert-message.sc-ion-alert-md,.alert-input-group.sc-ion-alert-md{-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-overflow-scrolling:touch;overflow-y:auto;overscroll-behavior-y:contain}.alert-checkbox-label.sc-ion-alert-md,.alert-radio-label.sc-ion-alert-md{overflow-wrap:anywhere}@media (any-pointer: coarse){.alert-checkbox-group.sc-ion-alert-md::-webkit-scrollbar,.alert-radio-group.sc-ion-alert-md::-webkit-scrollbar,.alert-message.sc-ion-alert-md::-webkit-scrollbar{display:none}}.alert-input.sc-ion-alert-md{padding-left:0;padding-right:0;padding-top:10px;padding-bottom:10px;width:100%;border:0;background:inherit;font:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.alert-button-group.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:100%}.alert-button-group-vertical.sc-ion-alert-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.alert-button.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;border:0;font-size:0.875rem;line-height:1.25rem;z-index:0}.alert-button.ion-focused.sc-ion-alert-md,.alert-tappable.ion-focused.sc-ion-alert-md{background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.alert-button-inner.sc-ion-alert-md{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit}.alert-input-disabled.sc-ion-alert-md,.alert-checkbox-button-disabled.sc-ion-alert-md .alert-button-inner.sc-ion-alert-md,.alert-radio-button-disabled.sc-ion-alert-md .alert-button-inner.sc-ion-alert-md{cursor:default;opacity:0.5;pointer-events:none}.alert-tappable.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;width:100%;border:0;background:transparent;font-size:inherit;line-height:initial;text-align:start;-webkit-appearance:none;-moz-appearance:none;appearance:none;contain:content}.alert-button.sc-ion-alert-md,.alert-checkbox.sc-ion-alert-md,.alert-input.sc-ion-alert-md,.alert-radio.sc-ion-alert-md{outline:none}.alert-radio-icon.sc-ion-alert-md,.alert-checkbox-icon.sc-ion-alert-md,.alert-checkbox-inner.sc-ion-alert-md{-webkit-box-sizing:border-box;box-sizing:border-box}textarea.alert-input.sc-ion-alert-md{min-height:37px;resize:none}.sc-ion-alert-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--max-width:280px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);font-size:0.875rem}.alert-wrapper.sc-ion-alert-md{border-radius:4px;-webkit-box-shadow:0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12);box-shadow:0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12)}.alert-head.sc-ion-alert-md{-webkit-padding-start:23px;padding-inline-start:23px;-webkit-padding-end:23px;padding-inline-end:23px;padding-top:20px;padding-bottom:15px;text-align:start}.alert-title.sc-ion-alert-md{color:var(--ion-text-color, #000);font-size:1.25rem;font-weight:500}.alert-sub-title.sc-ion-alert-md{color:var(--ion-text-color, #000);font-size:1rem}.alert-message.sc-ion-alert-md,.alert-input-group.sc-ion-alert-md{-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:20px;padding-bottom:20px;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.alert-message.sc-ion-alert-md{font-size:1rem}@media screen and (max-width: 767px){.alert-message.sc-ion-alert-md{max-height:266px}}.alert-message.sc-ion-alert-md:empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}.alert-head.sc-ion-alert-md+.alert-message.sc-ion-alert-md{padding-top:0}.alert-input.sc-ion-alert-md{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;border-bottom:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));color:var(--ion-text-color, #000)}.alert-input.sc-ion-alert-md::-webkit-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-moz-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md:-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-ms-input-placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::placeholder{color:var(--ion-placeholder-color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-family:inherit;font-weight:inherit}.alert-input.sc-ion-alert-md::-ms-clear{display:none}.alert-input.sc-ion-alert-md:focus{margin-bottom:4px;border-bottom:2px solid var(--ion-color-primary, #0054e9)}.alert-radio-group.sc-ion-alert-md,.alert-checkbox-group.sc-ion-alert-md{position:relative;border-top:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));border-bottom:1px solid var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));overflow:auto}@media screen and (max-width: 767px){.alert-radio-group.sc-ion-alert-md,.alert-checkbox-group.sc-ion-alert-md{max-height:266px}}.alert-tappable.sc-ion-alert-md{position:relative;min-height:48px}.alert-radio-label.sc-ion-alert-md{-webkit-padding-start:52px;padding-inline-start:52px;-webkit-padding-end:26px;padding-inline-end:26px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:1rem}.alert-radio-icon.sc-ion-alert-md{top:0;border-radius:50%;display:block;position:relative;width:20px;height:20px;border-width:2px;border-style:solid;border-color:var(--ion-color-step-550, var(--ion-background-color-step-550, #737373))}.alert-radio-icon.sc-ion-alert-md{inset-inline-start:26px}.alert-radio-inner.sc-ion-alert-md{top:3px;border-radius:50%;position:absolute;width:10px;height:10px;-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--ion-color-primary, #0054e9)}.alert-radio-inner.sc-ion-alert-md{inset-inline-start:3px}[aria-checked=true].sc-ion-alert-md .alert-radio-label.sc-ion-alert-md{color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626))}[aria-checked=true].sc-ion-alert-md .alert-radio-icon.sc-ion-alert-md{border-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-md .alert-radio-inner.sc-ion-alert-md{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}.alert-checkbox-label.sc-ion-alert-md{-webkit-padding-start:53px;padding-inline-start:53px;-webkit-padding-end:26px;padding-inline-end:26px;padding-top:13px;padding-bottom:13px;-ms-flex:1;flex:1;width:calc(100% - 53px);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:1rem}.alert-checkbox-icon.sc-ion-alert-md{top:0;border-radius:2px;position:relative;width:16px;height:16px;border-width:2px;border-style:solid;border-color:var(--ion-color-step-550, var(--ion-background-color-step-550, #737373));contain:strict}.alert-checkbox-icon.sc-ion-alert-md{inset-inline-start:26px}[aria-checked=true].sc-ion-alert-md .alert-checkbox-icon.sc-ion-alert-md{border-color:var(--ion-color-primary, #0054e9);background-color:var(--ion-color-primary, #0054e9)}[aria-checked=true].sc-ion-alert-md .alert-checkbox-inner.sc-ion-alert-md{top:0;position:absolute;width:6px;height:10px;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:2px;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--ion-color-primary-contrast, #fff)}[aria-checked=true].sc-ion-alert-md .alert-checkbox-inner.sc-ion-alert-md{inset-inline-start:3px}.alert-button-group.sc-ion-alert-md{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-wrap:wrap-reverse;flex-wrap:wrap-reverse;-ms-flex-pack:end;justify-content:flex-end}.alert-button.sc-ion-alert-md{border-radius:2px;-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:0;margin-bottom:0;-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;position:relative;background-color:transparent;color:var(--ion-color-primary, #0054e9);font-weight:500;text-align:end;text-transform:uppercase;overflow:hidden}.alert-button-inner.sc-ion-alert-md{-ms-flex-pack:end;justify-content:flex-end}@media screen and (min-width: 768px){.sc-ion-alert-md-h{--max-width:min(100vw - 96px, 560px);--max-height:min(100vh - 96px, 560px)}}\";\n\nconst Alert = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionAlertDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionAlertWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionAlertWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionAlertDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n        this.processedInputs = [];\n        this.processedButtons = [];\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * Array of buttons to be added to the alert.\n         */\n        this.buttons = [];\n        /**\n         * Array of input to show in the alert.\n         */\n        this.inputs = [];\n        /**\n         * If `true`, the alert will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, the alert will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the alert will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the alert will open. If `false`, the alert will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the alertController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the alert dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.processedButtons.find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    onKeydown(ev) {\n        var _a;\n        const inputTypes = new Set(this.processedInputs.map((i) => i.type));\n        /**\n         * Based on keyboard navigation requirements, the\n         * checkbox should not respond to the enter keydown event.\n         */\n        if (inputTypes.has('checkbox') && ev.key === 'Enter') {\n            ev.preventDefault();\n            return;\n        }\n        /**\n         * Ensure when alert container is being focused, and the user presses the tab + shift keys, the focus will be set to the last alert button.\n         */\n        if (ev.target.classList.contains('alert-wrapper')) {\n            if (ev.key === 'Tab' && ev.shiftKey) {\n                ev.preventDefault();\n                const lastChildBtn = (_a = this.wrapperEl) === null || _a === void 0 ? void 0 : _a.querySelector('.alert-button:last-child');\n                lastChildBtn.focus();\n                return;\n            }\n        }\n        // The only inputs we want to navigate between using arrow keys are the radios\n        // ignore the keydown event if it is not on a radio button\n        if (!inputTypes.has('radio') ||\n            (ev.target && !this.el.contains(ev.target)) ||\n            ev.target.classList.contains('alert-button')) {\n            return;\n        }\n        // Get all radios inside of the radio group and then\n        // filter out disabled radios since we need to skip those\n        const query = this.el.querySelectorAll('.alert-radio');\n        const radios = Array.from(query).filter((radio) => !radio.disabled);\n        // The focused radio is the one that shares the same id as\n        // the event target\n        const index = radios.findIndex((radio) => radio.id === ev.target.id);\n        // We need to know what the next radio element should\n        // be in order to change the focus\n        let nextEl;\n        // If hitting arrow down or arrow right, move to the next radio\n        // If we're on the last radio, move to the first radio\n        if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n            nextEl = index === radios.length - 1 ? radios[0] : radios[index + 1];\n        }\n        // If hitting arrow up or arrow left, move to the previous radio\n        // If we're on the first radio, move to the last radio\n        if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n            nextEl = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n        }\n        if (nextEl && radios.includes(nextEl)) {\n            const nextProcessed = this.processedInputs.find((input) => input.id === (nextEl === null || nextEl === void 0 ? void 0 : nextEl.id));\n            if (nextProcessed) {\n                this.rbClick(nextProcessed);\n                nextEl.focus();\n            }\n        }\n    }\n    buttonsChanged() {\n        const buttons = this.buttons;\n        this.processedButtons = buttons.map((btn) => {\n            return typeof btn === 'string' ? { text: btn, role: btn.toLowerCase() === 'cancel' ? 'cancel' : undefined } : btn;\n        });\n    }\n    inputsChanged() {\n        const inputs = this.inputs;\n        // Get the first input that is not disabled and the checked one\n        // If an enabled checked input exists, set it to be the focusable input\n        // otherwise we default to focus the first input\n        // This will only be used when the input is type radio\n        const first = inputs.find((input) => !input.disabled);\n        const checked = inputs.find((input) => input.checked && !input.disabled);\n        const focusable = checked || first;\n        // An alert can be created with several different inputs. Radios,\n        // checkboxes and inputs are all accepted, but they cannot be mixed.\n        const inputTypes = new Set(inputs.map((i) => i.type));\n        if (inputTypes.has('checkbox') && inputTypes.has('radio')) {\n            printIonWarning(`[ion-alert] - Alert cannot mix input types: ${Array.from(inputTypes.values()).join('/')}. Please see alert docs for more info.`);\n        }\n        this.inputType = inputTypes.values().next().value;\n        this.processedInputs = inputs.map((i, index) => {\n            var _a;\n            return ({\n                type: i.type || 'text',\n                name: i.name || `${index}`,\n                placeholder: i.placeholder || '',\n                value: i.value,\n                label: i.label,\n                checked: !!i.checked,\n                disabled: !!i.disabled,\n                id: i.id || `alert-input-${this.overlayIndex}-${index}`,\n                handler: i.handler,\n                min: i.min,\n                max: i.max,\n                cssClass: (_a = i.cssClass) !== null && _a !== void 0 ? _a : '',\n                attributes: i.attributes || {},\n                tabindex: i.type === 'radio' && i !== focusable ? -1 : 0,\n            });\n        });\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    componentWillLoad() {\n        var _a;\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n        this.inputsChanged();\n        this.buttonsChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    componentDidLoad() {\n        /**\n         * Only create gesture if:\n         * 1. A gesture does not already exist\n         * 2. App is running in iOS mode\n         * 3. A wrapper ref exists\n         */\n        if (!this.gesture && getIonMode(this) === 'ios' && this.wrapperEl) {\n            this.gesture = createButtonActiveGesture(this.wrapperEl, (refEl) => refEl.classList.contains('alert-button'));\n            this.gesture.enable(true);\n        }\n        /**\n         * If alert was rendered with isOpen=\"true\"\n         * then we should open alert immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Present the alert overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'alertEnter', iosEnterAnimation, mdEnterAnimation).then(() => {\n            var _a, _b;\n            /**\n             * Check if alert has only one button and no inputs.\n             * If so, then focus on the button. Otherwise, focus the alert wrapper.\n             * This will map to the default native alert behavior.\n             */\n            if (this.buttons.length === 1 && this.inputs.length === 0) {\n                const queryBtn = (_a = this.wrapperEl) === null || _a === void 0 ? void 0 : _a.querySelector('.alert-button');\n                queryBtn.focus();\n            }\n            else {\n                (_b = this.wrapperEl) === null || _b === void 0 ? void 0 : _b.focus();\n            }\n        });\n        unlock();\n    }\n    /**\n     * Dismiss the alert overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the alert.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the alert. Some examples include:\n     * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        const dismissed = await dismiss(this, data, role, 'alertLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the alert did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionAlertDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the alert will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionAlertWillDismiss');\n    }\n    rbClick(selectedInput) {\n        for (const input of this.processedInputs) {\n            input.checked = input === selectedInput;\n            input.tabindex = input === selectedInput ? 0 : -1;\n        }\n        this.activeId = selectedInput.id;\n        safeCall(selectedInput.handler, selectedInput);\n        forceUpdate(this);\n    }\n    cbClick(selectedInput) {\n        selectedInput.checked = !selectedInput.checked;\n        safeCall(selectedInput.handler, selectedInput);\n        forceUpdate(this);\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        const values = this.getValues();\n        if (isCancel(role)) {\n            return this.dismiss({ values }, role);\n        }\n        const returnData = await this.callButtonHandler(button, values);\n        if (returnData !== false) {\n            return this.dismiss(Object.assign({ values }, returnData), button.role);\n        }\n        return false;\n    }\n    async callButtonHandler(button, data) {\n        if (button === null || button === void 0 ? void 0 : button.handler) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const returnData = await safeCall(button.handler, data);\n            if (returnData === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n            if (typeof returnData === 'object') {\n                return returnData;\n            }\n        }\n        return {};\n    }\n    getValues() {\n        if (this.processedInputs.length === 0) {\n            // this is an alert without any options/inputs at all\n            return undefined;\n        }\n        if (this.inputType === 'radio') {\n            // this is an alert with radio buttons (single value select)\n            // return the one value which is checked, otherwise undefined\n            const checkedInput = this.processedInputs.find((i) => !!i.checked);\n            return checkedInput ? checkedInput.value : undefined;\n        }\n        if (this.inputType === 'checkbox') {\n            // this is an alert with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return this.processedInputs.filter((i) => i.checked).map((i) => i.value);\n        }\n        // this is an alert with text inputs\n        // return an object of all the values with the input name as the key\n        const values = {};\n        this.processedInputs.forEach((i) => {\n            values[i.name] = i.value || '';\n        });\n        return values;\n    }\n    renderAlertInputs() {\n        switch (this.inputType) {\n            case 'checkbox':\n                return this.renderCheckbox();\n            case 'radio':\n                return this.renderRadio();\n            default:\n                return this.renderInput();\n        }\n    }\n    renderCheckbox() {\n        const inputs = this.processedInputs;\n        const mode = getIonMode(this);\n        if (inputs.length === 0) {\n            return null;\n        }\n        return (h(\"div\", { class: \"alert-checkbox-group\" }, inputs.map((i) => (h(\"button\", { type: \"button\", onClick: () => this.cbClick(i), \"aria-checked\": `${i.checked}`, id: i.id, disabled: i.disabled, tabIndex: i.tabindex, role: \"checkbox\", class: Object.assign(Object.assign({}, getClassMap(i.cssClass)), { 'alert-tappable': true, 'alert-checkbox': true, 'alert-checkbox-button': true, 'ion-focusable': true, 'alert-checkbox-button-disabled': i.disabled || false }) }, h(\"div\", { class: \"alert-button-inner\" }, h(\"div\", { class: \"alert-checkbox-icon\" }, h(\"div\", { class: \"alert-checkbox-inner\" })), h(\"div\", { class: \"alert-checkbox-label\" }, i.label)), mode === 'md' && h(\"ion-ripple-effect\", null))))));\n    }\n    renderRadio() {\n        const inputs = this.processedInputs;\n        if (inputs.length === 0) {\n            return null;\n        }\n        return (h(\"div\", { class: \"alert-radio-group\", role: \"radiogroup\", \"aria-activedescendant\": this.activeId }, inputs.map((i) => (h(\"button\", { type: \"button\", onClick: () => this.rbClick(i), \"aria-checked\": `${i.checked}`, disabled: i.disabled, id: i.id, tabIndex: i.tabindex, class: Object.assign(Object.assign({}, getClassMap(i.cssClass)), { 'alert-radio-button': true, 'alert-tappable': true, 'alert-radio': true, 'ion-focusable': true, 'alert-radio-button-disabled': i.disabled || false }), role: \"radio\" }, h(\"div\", { class: \"alert-button-inner\" }, h(\"div\", { class: \"alert-radio-icon\" }, h(\"div\", { class: \"alert-radio-inner\" })), h(\"div\", { class: \"alert-radio-label\" }, i.label)))))));\n    }\n    renderInput() {\n        const inputs = this.processedInputs;\n        if (inputs.length === 0) {\n            return null;\n        }\n        return (h(\"div\", { class: \"alert-input-group\" }, inputs.map((i) => {\n            var _a, _b, _c, _d;\n            if (i.type === 'textarea') {\n                return (h(\"div\", { class: \"alert-input-wrapper\" }, h(\"textarea\", Object.assign({ placeholder: i.placeholder, value: i.value, id: i.id, tabIndex: i.tabindex }, i.attributes, { disabled: (_b = (_a = i.attributes) === null || _a === void 0 ? void 0 : _a.disabled) !== null && _b !== void 0 ? _b : i.disabled, class: inputClass(i), onInput: (e) => {\n                        var _a;\n                        i.value = e.target.value;\n                        if ((_a = i.attributes) === null || _a === void 0 ? void 0 : _a.onInput) {\n                            i.attributes.onInput(e);\n                        }\n                    } }))));\n            }\n            else {\n                return (h(\"div\", { class: \"alert-input-wrapper\" }, h(\"input\", Object.assign({ placeholder: i.placeholder, type: i.type, min: i.min, max: i.max, value: i.value, id: i.id, tabIndex: i.tabindex }, i.attributes, { disabled: (_d = (_c = i.attributes) === null || _c === void 0 ? void 0 : _c.disabled) !== null && _d !== void 0 ? _d : i.disabled, class: inputClass(i), onInput: (e) => {\n                        var _a;\n                        i.value = e.target.value;\n                        if ((_a = i.attributes) === null || _a === void 0 ? void 0 : _a.onInput) {\n                            i.attributes.onInput(e);\n                        }\n                    } }))));\n            }\n        })));\n    }\n    renderAlertButtons() {\n        const buttons = this.processedButtons;\n        const mode = getIonMode(this);\n        const alertButtonGroupClass = {\n            'alert-button-group': true,\n            'alert-button-group-vertical': buttons.length > 2,\n        };\n        return (h(\"div\", { class: alertButtonGroupClass }, buttons.map((button) => (h(\"button\", Object.assign({}, button.htmlAttributes, { type: \"button\", id: button.id, class: buttonClass(button), tabIndex: 0, onClick: () => this.buttonClick(button) }), h(\"span\", { class: \"alert-button-inner\" }, button.text), mode === 'md' && h(\"ion-ripple-effect\", null))))));\n    }\n    renderAlertMessage(msgId) {\n        const { customHTMLEnabled, message } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { id: msgId, class: \"alert-message\", innerHTML: sanitizeDOMString(message) });\n        }\n        return (h(\"div\", { id: msgId, class: \"alert-message\" }, message));\n    }\n    render() {\n        const { overlayIndex, header, subHeader, message, htmlAttributes } = this;\n        const mode = getIonMode(this);\n        const hdrId = `alert-${overlayIndex}-hdr`;\n        const msgId = `alert-${overlayIndex}-msg`;\n        const subHdrId = `alert-${overlayIndex}-sub-hdr`;\n        const role = this.inputs.length > 0 || this.buttons.length > 0 ? 'alertdialog' : 'alert';\n        /**\n         * Use both the header and subHeader ids if they are defined.\n         * If only the header is defined, use the header id.\n         * If only the subHeader is defined, use the subHeader id.\n         * If neither are defined, do not set aria-labelledby.\n         */\n        const ariaLabelledBy = header && subHeader ? `${hdrId} ${subHdrId}` : header ? hdrId : subHeader ? subHdrId : null;\n        return (h(Host, { key: '6025440b9cd369d4fac89e7e4296c84a10a0b8e0', tabindex: \"-1\", style: {\n                zIndex: `${20000 + overlayIndex}`,\n            }, class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), { [mode]: true, 'overlay-hidden': true, 'alert-translucent': this.translucent }), onIonAlertWillDismiss: this.dispatchCancelHandler, onIonBackdropTap: this.onBackdropTap }, h(\"ion-backdrop\", { key: '3cd5ca8b99cb95b11dd22ab41a820d841142896f', tappable: this.backdropDismiss }), h(\"div\", { key: '4cc62ae6e21424057d22aeef1e8fc77011e77cd5', tabindex: \"0\", \"aria-hidden\": \"true\" }), h(\"div\", Object.assign({ key: '364057a69f25aa88904df17bdcf7e5bf714e7830', class: \"alert-wrapper ion-overlay-wrapper\", role: role, \"aria-modal\": \"true\", \"aria-labelledby\": ariaLabelledBy, \"aria-describedby\": message !== undefined ? msgId : null, tabindex: \"0\", ref: (el) => (this.wrapperEl = el) }, htmlAttributes), h(\"div\", { key: '78694e3c0db2d408df3899fb1a90859bcc8d14cc', class: \"alert-head\" }, header && (h(\"h2\", { key: 'ec88ff3e4e1ea871b5975133fdcf4cac38b05e0f', id: hdrId, class: \"alert-title\" }, header)), subHeader && !header && (h(\"h2\", { key: '9b09bc8bb68af255ef8b7d22587acc946148e544', id: subHdrId, class: \"alert-sub-title\" }, subHeader)), subHeader && header && (h(\"h3\", { key: '99abe815f75d2df7f1b77c0df9f3436724fea76f', id: subHdrId, class: \"alert-sub-title\" }, subHeader))), this.renderAlertMessage(msgId), this.renderAlertInputs(), this.renderAlertButtons()), h(\"div\", { key: 'a43d0c22c0e46b1ef911f92ffeb253d7911b85f7', tabindex: \"0\", \"aria-hidden\": \"true\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"],\n        \"buttons\": [\"buttonsChanged\"],\n        \"inputs\": [\"inputsChanged\"]\n    }; }\n};\nconst inputClass = (input) => {\n    var _a, _b, _c;\n    return Object.assign(Object.assign({ 'alert-input': true, 'alert-input-disabled': ((_b = (_a = input.attributes) === null || _a === void 0 ? void 0 : _a.disabled) !== null && _b !== void 0 ? _b : input.disabled) || false }, getClassMap(input.cssClass)), getClassMap(input.attributes ? (_c = input.attributes.class) === null || _c === void 0 ? void 0 : _c.toString() : ''));\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'alert-button': true, 'ion-focusable': true, 'ion-activatable': true, [`alert-button-role-${button.role}`]: button.role !== undefined }, getClassMap(button.cssClass));\n};\nAlert.style = {\n    ios: alertIosCss,\n    md: alertMdCss\n};\n\nexport { Alert as ion_alert };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAClL,SAASC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,sBAAsB;AAC/F,SAASC,CAAC,IAAIC,yBAAyB,QAAQ,6BAA6B;AAC5E,SAAStB,CAAC,IAAIuB,GAAG,QAAQ,uBAAuB;AAChD,SAASF,CAAC,IAAIG,oBAAoB,QAAQ,+BAA+B;AACzE,SAAStB,CAAC,IAAIuB,wBAAwB,EAAEjB,CAAC,IAAIkB,uBAAuB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEjB,CAAC,IAAIkB,cAAc,EAAEhB,CAAC,IAAIiB,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,OAAO,EAAExB,CAAC,IAAIyB,WAAW,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,wBAAwB;AACvO,SAASJ,CAAC,IAAIK,WAAW,QAAQ,qBAAqB;AACtD,SAASnB,CAAC,IAAIoB,eAAe,QAAQ,yBAAyB;AAC9D,OAAO,sBAAsB;AAC7B,OAAO,yBAAyB;AAChC,OAAO,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAO,kCAAkC;AACzC,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;;AAEzC;AACA;AACA;AACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC1E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,MAAM;IAAEC,SAAS,EAAE;EAAa,CAAC,EACvD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,SAAS,EAAE;EAAW,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMa,iBAAiB,GAAIhB,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC1E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAW,CAAC,EACnD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMc,gBAAgB,GAAIjB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC1E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,MAAM;IAAEC,SAAS,EAAE;EAAa,CAAC,EACvD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,GAAG;IAAEC,SAAS,EAAE;EAAW,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMe,gBAAgB,GAAIlB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC9F,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMgB,WAAW,GAAG,88XAA88X;AAEl+X,MAAMC,UAAU,GAAG,+lVAA+lV;AAElnV,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBjE,gBAAgB,CAAC,IAAI,EAAEiE,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGhE,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACiE,WAAW,GAAGjE,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACkE,WAAW,GAAGlE,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACmE,UAAU,GAAGnE,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACoE,mBAAmB,GAAGpE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACqE,oBAAoB,GAAGrE,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACsE,oBAAoB,GAAGtE,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACuE,mBAAmB,GAAGvE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACwE,kBAAkB,GAAGlD,wBAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACmD,cAAc,GAAGpD,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACqD,iBAAiB,GAAGnD,uBAAuB,CAAC,CAAC;IAClD,IAAI,CAACoD,iBAAiB,GAAGzE,MAAM,CAAC0E,GAAG,CAAC,2BAA2B,EAAE7D,2BAA2B,CAAC;IAC7F,IAAI,CAAC8D,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAACvD,OAAO,CAACwD,SAAS,EAAEhE,QAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACiE,qBAAqB,GAAIC,EAAE,IAAK;MACjC,MAAMC,IAAI,GAAGD,EAAE,CAACE,MAAM,CAACD,IAAI;MAC3B,IAAIjE,QAAQ,CAACiE,IAAI,CAAC,EAAE;QAChB,MAAME,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACiB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,IAAI,KAAK,QAAQ,CAAC;QAC3E,IAAI,CAACK,iBAAiB,CAACH,YAAY,CAAC;MACxC;IACJ,CAAC;EACL;EACAI,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACrE,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIoE,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACnE,OAAO,CAAC,CAAC;IAClB;EACJ;EACAoE,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,OAAO;MAAEC,EAAE;MAAE7B;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAI4B,OAAO,EAAE;MACT5B,iBAAiB,CAAC8B,gBAAgB,CAACD,EAAE,EAAED,OAAO,CAAC;IACnD;EACJ;EACAG,SAASA,CAACd,EAAE,EAAE;IACV,IAAIe,EAAE;IACN,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC/B,eAAe,CAACgC,GAAG,CAAEnF,CAAC,IAAKA,CAAC,CAACoF,IAAI,CAAC,CAAC;IACnE;AACR;AACA;AACA;IACQ,IAAIH,UAAU,CAACI,GAAG,CAAC,UAAU,CAAC,IAAIpB,EAAE,CAACqB,GAAG,KAAK,OAAO,EAAE;MAClDrB,EAAE,CAACsB,cAAc,CAAC,CAAC;MACnB;IACJ;IACA;AACR;AACA;IACQ,IAAItB,EAAE,CAACuB,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC/C,IAAIzB,EAAE,CAACqB,GAAG,KAAK,KAAK,IAAIrB,EAAE,CAAC0B,QAAQ,EAAE;QACjC1B,EAAE,CAACsB,cAAc,CAAC,CAAC;QACnB,MAAMK,YAAY,GAAG,CAACZ,EAAE,GAAG,IAAI,CAACa,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7D,aAAa,CAAC,0BAA0B,CAAC;QAC5HyE,YAAY,CAACE,KAAK,CAAC,CAAC;QACpB;MACJ;IACJ;IACA;IACA;IACA,IAAI,CAACb,UAAU,CAACI,GAAG,CAAC,OAAO,CAAC,IACvBpB,EAAE,CAACuB,MAAM,IAAI,CAAC,IAAI,CAACX,EAAE,CAACa,QAAQ,CAACzB,EAAE,CAACuB,MAAM,CAAE,IAC3CvB,EAAE,CAACuB,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;MAC9C;IACJ;IACA;IACA;IACA,MAAMK,KAAK,GAAG,IAAI,CAAClB,EAAE,CAACmB,gBAAgB,CAAC,cAAc,CAAC;IACtD,MAAMC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,MAAM,CAAEC,KAAK,IAAK,CAACA,KAAK,CAACC,QAAQ,CAAC;IACnE;IACA;IACA,MAAMC,KAAK,GAAGN,MAAM,CAACO,SAAS,CAAEH,KAAK,IAAKA,KAAK,CAACI,EAAE,KAAKxC,EAAE,CAACuB,MAAM,CAACiB,EAAE,CAAC;IACpE;IACA;IACA,IAAIC,MAAM;IACV;IACA;IACA,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACC,QAAQ,CAAC1C,EAAE,CAACqB,GAAG,CAAC,EAAE;MAC9CoB,MAAM,GAAGH,KAAK,KAAKN,MAAM,CAACW,MAAM,GAAG,CAAC,GAAGX,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACM,KAAK,GAAG,CAAC,CAAC;IACxE;IACA;IACA;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACI,QAAQ,CAAC1C,EAAE,CAACqB,GAAG,CAAC,EAAE;MAC3CoB,MAAM,GAAGH,KAAK,KAAK,CAAC,GAAGN,MAAM,CAACA,MAAM,CAACW,MAAM,GAAG,CAAC,CAAC,GAAGX,MAAM,CAACM,KAAK,GAAG,CAAC,CAAC;IACxE;IACA,IAAIG,MAAM,IAAIT,MAAM,CAACU,QAAQ,CAACD,MAAM,CAAC,EAAE;MACnC,MAAMG,aAAa,GAAG,IAAI,CAAC1D,eAAe,CAACkB,IAAI,CAAEyC,KAAK,IAAKA,KAAK,CAACL,EAAE,MAAMC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,EAAE,CAAC,CAAC;MACpI,IAAII,aAAa,EAAE;QACf,IAAI,CAACE,OAAO,CAACF,aAAa,CAAC;QAC3BH,MAAM,CAACZ,KAAK,CAAC,CAAC;MAClB;IACJ;EACJ;EACAkB,cAAcA,CAAA,EAAG;IACb,MAAMxD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACJ,gBAAgB,GAAGI,OAAO,CAAC2B,GAAG,CAAE8B,GAAG,IAAK;MACzC,OAAO,OAAOA,GAAG,KAAK,QAAQ,GAAG;QAAEC,IAAI,EAAED,GAAG;QAAE/C,IAAI,EAAE+C,GAAG,CAACE,WAAW,CAAC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAGpD;MAAU,CAAC,GAAGkD,GAAG;IACrH,CAAC,CAAC;EACN;EACAG,aAAaA,CAAA,EAAG;IACZ,MAAM3D,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B;IACA;IACA;IACA;IACA,MAAM4D,KAAK,GAAG5D,MAAM,CAACY,IAAI,CAAEyC,KAAK,IAAK,CAACA,KAAK,CAACR,QAAQ,CAAC;IACrD,MAAMgB,OAAO,GAAG7D,MAAM,CAACY,IAAI,CAAEyC,KAAK,IAAKA,KAAK,CAACQ,OAAO,IAAI,CAACR,KAAK,CAACR,QAAQ,CAAC;IACxE,MAAMiB,SAAS,GAAGD,OAAO,IAAID,KAAK;IAClC;IACA;IACA,MAAMpC,UAAU,GAAG,IAAIC,GAAG,CAACzB,MAAM,CAAC0B,GAAG,CAAEnF,CAAC,IAAKA,CAAC,CAACoF,IAAI,CAAC,CAAC;IACrD,IAAIH,UAAU,CAACI,GAAG,CAAC,UAAU,CAAC,IAAIJ,UAAU,CAACI,GAAG,CAAC,OAAO,CAAC,EAAE;MACvD3G,eAAe,CAAC,+CAA+CwH,KAAK,CAACC,IAAI,CAAClB,UAAU,CAACuC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,wCAAwC,CAAC;IACrJ;IACA,IAAI,CAACC,SAAS,GAAGzC,UAAU,CAACuC,MAAM,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK;IACjD,IAAI,CAACzE,eAAe,GAAGM,MAAM,CAAC0B,GAAG,CAAC,CAACnF,CAAC,EAAEuG,KAAK,KAAK;MAC5C,IAAIvB,EAAE;MACN,OAAQ;QACJI,IAAI,EAAEpF,CAAC,CAACoF,IAAI,IAAI,MAAM;QACtByC,IAAI,EAAE7H,CAAC,CAAC6H,IAAI,IAAI,GAAGtB,KAAK,EAAE;QAC1BuB,WAAW,EAAE9H,CAAC,CAAC8H,WAAW,IAAI,EAAE;QAChCF,KAAK,EAAE5H,CAAC,CAAC4H,KAAK;QACdG,KAAK,EAAE/H,CAAC,CAAC+H,KAAK;QACdT,OAAO,EAAE,CAAC,CAACtH,CAAC,CAACsH,OAAO;QACpBhB,QAAQ,EAAE,CAAC,CAACtG,CAAC,CAACsG,QAAQ;QACtBG,EAAE,EAAEzG,CAAC,CAACyG,EAAE,IAAI,eAAe,IAAI,CAACuB,YAAY,IAAIzB,KAAK,EAAE;QACvD0B,OAAO,EAAEjI,CAAC,CAACiI,OAAO;QAClBC,GAAG,EAAElI,CAAC,CAACkI,GAAG;QACVC,GAAG,EAAEnI,CAAC,CAACmI,GAAG;QACVC,QAAQ,EAAE,CAACpD,EAAE,GAAGhF,CAAC,CAACoI,QAAQ,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QAC/DqD,UAAU,EAAErI,CAAC,CAACqI,UAAU,IAAI,CAAC,CAAC;QAC9BC,QAAQ,EAAEtI,CAAC,CAACoF,IAAI,KAAK,OAAO,IAAIpF,CAAC,KAAKuH,SAAS,GAAG,CAAC,CAAC,GAAG;MAC3D,CAAC;IACL,CAAC,CAAC;EACN;EACAgB,iBAAiBA,CAAA,EAAG;IAChBrI,cAAc,CAAC,IAAI,CAAC2E,EAAE,CAAC;IACvB,IAAI,CAACF,cAAc,CAAC,CAAC;EACzB;EACA6D,iBAAiBA,CAAA,EAAG;IAChB,IAAIxD,EAAE;IACN,IAAI,EAAE,CAACA,EAAE,GAAG,IAAI,CAACyD,cAAc,MAAM,IAAI,IAAIzD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,EAAE,CAAC,EAAE;MAC1EtG,YAAY,CAAC,IAAI,CAAC0E,EAAE,CAAC;IACzB;IACA,IAAI,CAACuC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACJ,cAAc,CAAC,CAAC;EACzB;EACA0B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC1F,iBAAiB,CAAC2F,mBAAmB,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACD,OAAO,GAAG7E,SAAS;IAC5B;EACJ;EACA+E,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACF,OAAO,IAAIhK,UAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAACiH,SAAS,EAAE;MAC/D,IAAI,CAAC+C,OAAO,GAAGnJ,yBAAyB,CAAC,IAAI,CAACoG,SAAS,EAAGkD,KAAK,IAAKA,KAAK,CAACtD,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC,CAAC;MAC7G,IAAI,CAACkD,OAAO,CAACI,MAAM,CAAC,IAAI,CAAC;IAC7B;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACnF,MAAM,KAAK,IAAI,EAAE;MACtBnE,GAAG,CAAC,MAAM,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsE,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACUtE,OAAOA,CAAA,EAAG;IAAA,IAAA4I,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMC,MAAM,SAASF,KAAI,CAAClG,cAAc,CAACqG,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAACnG,kBAAkB,CAACuG,eAAe,CAAC,CAAC;MAC/C,MAAMhJ,OAAO,CAAC4I,KAAI,EAAE,YAAY,EAAEpI,iBAAiB,EAAEkB,gBAAgB,CAAC,CAACuH,IAAI,CAAC,MAAM;QAC9E,IAAItE,EAAE,EAAEuE,EAAE;QACV;AACZ;AACA;AACA;AACA;QACY,IAAIN,KAAI,CAACzF,OAAO,CAACoD,MAAM,KAAK,CAAC,IAAIqC,KAAI,CAACxF,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAE;UACvD,MAAM4C,QAAQ,GAAG,CAACxE,EAAE,GAAGiE,KAAI,CAACpD,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7D,aAAa,CAAC,eAAe,CAAC;UAC7GqI,QAAQ,CAAC1D,KAAK,CAAC,CAAC;QACpB,CAAC,MACI;UACD,CAACyD,EAAE,GAAGN,KAAI,CAACpD,SAAS,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzD,KAAK,CAAC,CAAC;QACzE;MACJ,CAAC,CAAC;MACFqD,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU5I,OAAOA,CAACkJ,IAAI,EAAEvF,IAAI,EAAE;IAAA,IAAAwF,MAAA;IAAA,OAAAR,iBAAA;MACtB,MAAMC,MAAM,SAASO,MAAI,CAAC3G,cAAc,CAACqG,IAAI,CAAC,CAAC;MAC/C,MAAMO,SAAS,SAASpJ,OAAO,CAACmJ,MAAI,EAAED,IAAI,EAAEvF,IAAI,EAAE,YAAY,EAAEpC,iBAAiB,EAAEE,gBAAgB,CAAC;MACpG,IAAI2H,SAAS,EAAE;QACXD,MAAI,CAAC5G,kBAAkB,CAAC8G,iBAAiB,CAAC,CAAC;MAC/C;MACAT,MAAM,CAAC,CAAC;MACR,OAAOQ,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAOrJ,WAAW,CAAC,IAAI,CAACqE,EAAE,EAAE,oBAAoB,CAAC;EACrD;EACA;AACJ;AACA;EACIiF,aAAaA,CAAA,EAAG;IACZ,OAAOtJ,WAAW,CAAC,IAAI,CAACqE,EAAE,EAAE,qBAAqB,CAAC;EACtD;EACAkC,OAAOA,CAACgD,aAAa,EAAE;IACnB,KAAK,MAAMjD,KAAK,IAAI,IAAI,CAAC3D,eAAe,EAAE;MACtC2D,KAAK,CAACQ,OAAO,GAAGR,KAAK,KAAKiD,aAAa;MACvCjD,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,KAAKiD,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD;IACA,IAAI,CAACC,QAAQ,GAAGD,aAAa,CAACtD,EAAE;IAChC/F,QAAQ,CAACqJ,aAAa,CAAC9B,OAAO,EAAE8B,aAAa,CAAC;IAC9CjL,WAAW,CAAC,IAAI,CAAC;EACrB;EACAmL,OAAOA,CAACF,aAAa,EAAE;IACnBA,aAAa,CAACzC,OAAO,GAAG,CAACyC,aAAa,CAACzC,OAAO;IAC9C5G,QAAQ,CAACqJ,aAAa,CAAC9B,OAAO,EAAE8B,aAAa,CAAC;IAC9CjL,WAAW,CAAC,IAAI,CAAC;EACrB;EACMoL,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MACtB,MAAMhF,IAAI,GAAGiG,MAAM,CAACjG,IAAI;MACxB,MAAMsD,MAAM,GAAG4C,MAAI,CAACC,SAAS,CAAC,CAAC;MAC/B,IAAIpK,QAAQ,CAACiE,IAAI,CAAC,EAAE;QAChB,OAAOkG,MAAI,CAAC7J,OAAO,CAAC;UAAEiH;QAAO,CAAC,EAAEtD,IAAI,CAAC;MACzC;MACA,MAAMoG,UAAU,SAASF,MAAI,CAAC7F,iBAAiB,CAAC4F,MAAM,EAAE3C,MAAM,CAAC;MAC/D,IAAI8C,UAAU,KAAK,KAAK,EAAE;QACtB,OAAOF,MAAI,CAAC7J,OAAO,CAACgK,MAAM,CAACC,MAAM,CAAC;UAAEhD;QAAO,CAAC,EAAE8C,UAAU,CAAC,EAAEH,MAAM,CAACjG,IAAI,CAAC;MAC3E;MACA,OAAO,KAAK;IAAC;EACjB;EACMK,iBAAiBA,CAAC4F,MAAM,EAAEV,IAAI,EAAE;IAAA,OAAAP,iBAAA;MAClC,IAAIiB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAClC,OAAO,EAAE;QAChE;QACA;QACA,MAAMqC,UAAU,SAAS5J,QAAQ,CAACyJ,MAAM,CAAClC,OAAO,EAAEwB,IAAI,CAAC;QACvD,IAAIa,UAAU,KAAK,KAAK,EAAE;UACtB;UACA,OAAO,KAAK;QAChB;QACA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC,OAAOA,UAAU;QACrB;MACJ;MACA,OAAO,CAAC,CAAC;IAAC;EACd;EACAD,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAClH,eAAe,CAACyD,MAAM,KAAK,CAAC,EAAE;MACnC;MACA,OAAO7C,SAAS;IACpB;IACA,IAAI,IAAI,CAAC2D,SAAS,KAAK,OAAO,EAAE;MAC5B;MACA;MACA,MAAM+C,YAAY,GAAG,IAAI,CAACtH,eAAe,CAACkB,IAAI,CAAErE,CAAC,IAAK,CAAC,CAACA,CAAC,CAACsH,OAAO,CAAC;MAClE,OAAOmD,YAAY,GAAGA,YAAY,CAAC7C,KAAK,GAAG7D,SAAS;IACxD;IACA,IAAI,IAAI,CAAC2D,SAAS,KAAK,UAAU,EAAE;MAC/B;MACA;MACA,OAAO,IAAI,CAACvE,eAAe,CAACiD,MAAM,CAAEpG,CAAC,IAAKA,CAAC,CAACsH,OAAO,CAAC,CAACnC,GAAG,CAAEnF,CAAC,IAAKA,CAAC,CAAC4H,KAAK,CAAC;IAC5E;IACA;IACA;IACA,MAAMJ,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACrE,eAAe,CAACuH,OAAO,CAAE1K,CAAC,IAAK;MAChCwH,MAAM,CAACxH,CAAC,CAAC6H,IAAI,CAAC,GAAG7H,CAAC,CAAC4H,KAAK,IAAI,EAAE;IAClC,CAAC,CAAC;IACF,OAAOJ,MAAM;EACjB;EACAmD,iBAAiBA,CAAA,EAAG;IAChB,QAAQ,IAAI,CAACjD,SAAS;MAClB,KAAK,UAAU;QACX,OAAO,IAAI,CAACkD,cAAc,CAAC,CAAC;MAChC,KAAK,OAAO;QACR,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;MAC7B;QACI,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;IACjC;EACJ;EACAF,cAAcA,CAAA,EAAG;IACb,MAAMnH,MAAM,GAAG,IAAI,CAACN,eAAe;IACnC,MAAM4H,IAAI,GAAGnM,UAAU,CAAC,IAAI,CAAC;IAC7B,IAAI6E,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI;IACf;IACA,OAAQ7H,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAuB,CAAC,EAAEvH,MAAM,CAAC0B,GAAG,CAAEnF,CAAC,IAAMjB,CAAC,CAAC,QAAQ,EAAE;MAAEqG,IAAI,EAAE,QAAQ;MAAE6F,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChB,OAAO,CAACjK,CAAC,CAAC;MAAE,cAAc,EAAE,GAAGA,CAAC,CAACsH,OAAO,EAAE;MAAEb,EAAE,EAAEzG,CAAC,CAACyG,EAAE;MAAEH,QAAQ,EAAEtG,CAAC,CAACsG,QAAQ;MAAE4E,QAAQ,EAAElL,CAAC,CAACsI,QAAQ;MAAEpE,IAAI,EAAE,UAAU;MAAE8G,KAAK,EAAET,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7J,WAAW,CAACX,CAAC,CAACoI,QAAQ,CAAC,CAAC,EAAE;QAAE,gBAAgB,EAAE,IAAI;QAAE,gBAAgB,EAAE,IAAI;QAAE,uBAAuB,EAAE,IAAI;QAAE,eAAe,EAAE,IAAI;QAAE,gCAAgC,EAAEpI,CAAC,CAACsG,QAAQ,IAAI;MAAM,CAAC;IAAE,CAAC,EAAEvH,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAqB,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAsB,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAuB,CAAC,CAAC,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAuB,CAAC,EAAEhL,CAAC,CAAC+H,KAAK,CAAC,CAAC,EAAEgD,IAAI,KAAK,IAAI,IAAIhM,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC,CAAC;EACjsB;EACA8L,WAAWA,CAAA,EAAG;IACV,MAAMpH,MAAM,GAAG,IAAI,CAACN,eAAe;IACnC,IAAIM,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI;IACf;IACA,OAAQ7H,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE,mBAAmB;MAAE9G,IAAI,EAAE,YAAY;MAAE,uBAAuB,EAAE,IAAI,CAAC8F;IAAS,CAAC,EAAEvG,MAAM,CAAC0B,GAAG,CAAEnF,CAAC,IAAMjB,CAAC,CAAC,QAAQ,EAAE;MAAEqG,IAAI,EAAE,QAAQ;MAAE6F,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAClE,OAAO,CAAC/G,CAAC,CAAC;MAAE,cAAc,EAAE,GAAGA,CAAC,CAACsH,OAAO,EAAE;MAAEhB,QAAQ,EAAEtG,CAAC,CAACsG,QAAQ;MAAEG,EAAE,EAAEzG,CAAC,CAACyG,EAAE;MAAEyE,QAAQ,EAAElL,CAAC,CAACsI,QAAQ;MAAE0C,KAAK,EAAET,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7J,WAAW,CAACX,CAAC,CAACoI,QAAQ,CAAC,CAAC,EAAE;QAAE,oBAAoB,EAAE,IAAI;QAAE,gBAAgB,EAAE,IAAI;QAAE,aAAa,EAAE,IAAI;QAAE,eAAe,EAAE,IAAI;QAAE,6BAA6B,EAAEpI,CAAC,CAACsG,QAAQ,IAAI;MAAM,CAAC,CAAC;MAAEpC,IAAI,EAAE;IAAQ,CAAC,EAAEnF,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAqB,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAmB,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAoB,CAAC,CAAC,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAoB,CAAC,EAAEhL,CAAC,CAAC+H,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC;EACtrB;EACA+C,WAAWA,CAAA,EAAG;IACV,MAAMrH,MAAM,GAAG,IAAI,CAACN,eAAe;IACnC,IAAIM,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI;IACf;IACA,OAAQ7H,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAoB,CAAC,EAAEvH,MAAM,CAAC0B,GAAG,CAAEnF,CAAC,IAAK;MAC/D,IAAIgF,EAAE,EAAEuE,EAAE,EAAE4B,EAAE,EAAEC,EAAE;MAClB,IAAIpL,CAAC,CAACoF,IAAI,KAAK,UAAU,EAAE;QACvB,OAAQrG,CAAC,CAAC,KAAK,EAAE;UAAEiM,KAAK,EAAE;QAAsB,CAAC,EAAEjM,CAAC,CAAC,UAAU,EAAEwL,MAAM,CAACC,MAAM,CAAC;UAAE1C,WAAW,EAAE9H,CAAC,CAAC8H,WAAW;UAAEF,KAAK,EAAE5H,CAAC,CAAC4H,KAAK;UAAEnB,EAAE,EAAEzG,CAAC,CAACyG,EAAE;UAAEyE,QAAQ,EAAElL,CAAC,CAACsI;QAAS,CAAC,EAAEtI,CAAC,CAACqI,UAAU,EAAE;UAAE/B,QAAQ,EAAE,CAACiD,EAAE,GAAG,CAACvE,EAAE,GAAGhF,CAAC,CAACqI,UAAU,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,QAAQ,MAAM,IAAI,IAAIiD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGvJ,CAAC,CAACsG,QAAQ;UAAE0E,KAAK,EAAEK,UAAU,CAACrL,CAAC,CAAC;UAAEsL,OAAO,EAAG3M,CAAC,IAAK;YAChV,IAAIqG,EAAE;YACNhF,CAAC,CAAC4H,KAAK,GAAGjJ,CAAC,CAAC6G,MAAM,CAACoC,KAAK;YACxB,IAAI,CAAC5C,EAAE,GAAGhF,CAAC,CAACqI,UAAU,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsG,OAAO,EAAE;cACrEtL,CAAC,CAACqI,UAAU,CAACiD,OAAO,CAAC3M,CAAC,CAAC;YAC3B;UACJ;QAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,MACI;QACD,OAAQI,CAAC,CAAC,KAAK,EAAE;UAAEiM,KAAK,EAAE;QAAsB,CAAC,EAAEjM,CAAC,CAAC,OAAO,EAAEwL,MAAM,CAACC,MAAM,CAAC;UAAE1C,WAAW,EAAE9H,CAAC,CAAC8H,WAAW;UAAE1C,IAAI,EAAEpF,CAAC,CAACoF,IAAI;UAAE8C,GAAG,EAAElI,CAAC,CAACkI,GAAG;UAAEC,GAAG,EAAEnI,CAAC,CAACmI,GAAG;UAAEP,KAAK,EAAE5H,CAAC,CAAC4H,KAAK;UAAEnB,EAAE,EAAEzG,CAAC,CAACyG,EAAE;UAAEyE,QAAQ,EAAElL,CAAC,CAACsI;QAAS,CAAC,EAAEtI,CAAC,CAACqI,UAAU,EAAE;UAAE/B,QAAQ,EAAE,CAAC8E,EAAE,GAAG,CAACD,EAAE,GAAGnL,CAAC,CAACqI,UAAU,MAAM,IAAI,IAAI8C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7E,QAAQ,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGpL,CAAC,CAACsG,QAAQ;UAAE0E,KAAK,EAAEK,UAAU,CAACrL,CAAC,CAAC;UAAEsL,OAAO,EAAG3M,CAAC,IAAK;YACnX,IAAIqG,EAAE;YACNhF,CAAC,CAAC4H,KAAK,GAAGjJ,CAAC,CAAC6G,MAAM,CAACoC,KAAK;YACxB,IAAI,CAAC5C,EAAE,GAAGhF,CAAC,CAACqI,UAAU,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsG,OAAO,EAAE;cACrEtL,CAAC,CAACqI,UAAU,CAACiD,OAAO,CAAC3M,CAAC,CAAC;YAC3B;UACJ;QAAE,CAAC,CAAC,CAAC,CAAC;MACd;IACJ,CAAC,CAAC,CAAC;EACP;EACA4M,kBAAkBA,CAAA,EAAG;IACjB,MAAM/H,OAAO,GAAG,IAAI,CAACJ,gBAAgB;IACrC,MAAM2H,IAAI,GAAGnM,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4M,qBAAqB,GAAG;MAC1B,oBAAoB,EAAE,IAAI;MAC1B,6BAA6B,EAAEhI,OAAO,CAACoD,MAAM,GAAG;IACpD,CAAC;IACD,OAAQ7H,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAEQ;IAAsB,CAAC,EAAEhI,OAAO,CAAC2B,GAAG,CAAEgF,MAAM,IAAMpL,CAAC,CAAC,QAAQ,EAAEwL,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,MAAM,CAAC1B,cAAc,EAAE;MAAErD,IAAI,EAAE,QAAQ;MAAEqB,EAAE,EAAE0D,MAAM,CAAC1D,EAAE;MAAEuE,KAAK,EAAES,WAAW,CAACtB,MAAM,CAAC;MAAEe,QAAQ,EAAE,CAAC;MAAED,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACf,WAAW,CAACC,MAAM;IAAE,CAAC,CAAC,EAAEpL,CAAC,CAAC,MAAM,EAAE;MAAEiM,KAAK,EAAE;IAAqB,CAAC,EAAEb,MAAM,CAACjD,IAAI,CAAC,EAAE6D,IAAI,KAAK,IAAI,IAAIhM,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC,CAAC;EACrW;EACA2M,kBAAkBA,CAACC,KAAK,EAAE;IACtB,MAAM;MAAE1I,iBAAiB;MAAE2I;IAAQ,CAAC,GAAG,IAAI;IAC3C,IAAI3I,iBAAiB,EAAE;MACnB,OAAOlE,CAAC,CAAC,KAAK,EAAE;QAAE0H,EAAE,EAAEkF,KAAK;QAAEX,KAAK,EAAE,eAAe;QAAEa,SAAS,EAAEtM,iBAAiB,CAACqM,OAAO;MAAE,CAAC,CAAC;IACjG;IACA,OAAQ7M,CAAC,CAAC,KAAK,EAAE;MAAE0H,EAAE,EAAEkF,KAAK;MAAEX,KAAK,EAAE;IAAgB,CAAC,EAAEY,OAAO,CAAC;EACpE;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE9D,YAAY;MAAE+D,MAAM;MAAEC,SAAS;MAAEJ,OAAO;MAAEnD;IAAe,CAAC,GAAG,IAAI;IACzE,MAAMsC,IAAI,GAAGnM,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqN,KAAK,GAAG,SAASjE,YAAY,MAAM;IACzC,MAAM2D,KAAK,GAAG,SAAS3D,YAAY,MAAM;IACzC,MAAMkE,QAAQ,GAAG,SAASlE,YAAY,UAAU;IAChD,MAAM9D,IAAI,GAAG,IAAI,CAACT,MAAM,CAACmD,MAAM,GAAG,CAAC,IAAI,IAAI,CAACpD,OAAO,CAACoD,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,OAAO;IACxF;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMuF,cAAc,GAAGJ,MAAM,IAAIC,SAAS,GAAG,GAAGC,KAAK,IAAIC,QAAQ,EAAE,GAAGH,MAAM,GAAGE,KAAK,GAAGD,SAAS,GAAGE,QAAQ,GAAG,IAAI;IAClH,OAAQnN,CAAC,CAACE,IAAI,EAAE;MAAEqG,GAAG,EAAE,0CAA0C;MAAEgD,QAAQ,EAAE,IAAI;MAAE8D,KAAK,EAAE;QAClFC,MAAM,EAAE,GAAG,KAAK,GAAGrE,YAAY;MACnC,CAAC;MAAEgD,KAAK,EAAET,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7J,WAAW,CAAC,IAAI,CAACyH,QAAQ,CAAC,CAAC,EAAE;QAAE,CAAC2C,IAAI,GAAG,IAAI;QAAE,gBAAgB,EAAE,IAAI;QAAE,mBAAmB,EAAE,IAAI,CAACpH;MAAY,CAAC,CAAC;MAAE2I,qBAAqB,EAAE,IAAI,CAACtI,qBAAqB;MAAEuI,gBAAgB,EAAE,IAAI,CAACzI;IAAc,CAAC,EAAE/E,CAAC,CAAC,cAAc,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEkH,QAAQ,EAAE,IAAI,CAAC9I;IAAgB,CAAC,CAAC,EAAE3E,CAAC,CAAC,KAAK,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEgD,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,EAAEvJ,CAAC,CAAC,KAAK,EAAEwL,MAAM,CAACC,MAAM,CAAC;MAAElF,GAAG,EAAE,0CAA0C;MAAE0F,KAAK,EAAE,mCAAmC;MAAE9G,IAAI,EAAEA,IAAI;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEiI,cAAc;MAAE,kBAAkB,EAAEP,OAAO,KAAK7H,SAAS,GAAG4H,KAAK,GAAG,IAAI;MAAErD,QAAQ,EAAE,GAAG;MAAEmE,GAAG,EAAG5H,EAAE,IAAM,IAAI,CAACgB,SAAS,GAAGhB;IAAI,CAAC,EAAE4D,cAAc,CAAC,EAAE1J,CAAC,CAAC,KAAK,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAE0F,KAAK,EAAE;IAAa,CAAC,EAAEe,MAAM,IAAKhN,CAAC,CAAC,IAAI,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEmB,EAAE,EAAEwF,KAAK;MAAEjB,KAAK,EAAE;IAAc,CAAC,EAAEe,MAAM,CAAE,EAAEC,SAAS,IAAI,CAACD,MAAM,IAAKhN,CAAC,CAAC,IAAI,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEmB,EAAE,EAAEyF,QAAQ;MAAElB,KAAK,EAAE;IAAkB,CAAC,EAAEgB,SAAS,CAAE,EAAEA,SAAS,IAAID,MAAM,IAAKhN,CAAC,CAAC,IAAI,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEmB,EAAE,EAAEyF,QAAQ;MAAElB,KAAK,EAAE;IAAkB,CAAC,EAAEgB,SAAS,CAAE,CAAC,EAAE,IAAI,CAACN,kBAAkB,CAACC,KAAK,CAAC,EAAE,IAAI,CAAChB,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACY,kBAAkB,CAAC,CAAC,CAAC,EAAExM,CAAC,CAAC,KAAK,EAAE;MAAEuG,GAAG,EAAE,0CAA0C;MAAEgD,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC;EAC15C;EACA,IAAIzD,EAAEA,CAAA,EAAG;IAAE,OAAO1F,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuN,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB,CAAC;MAC7B,SAAS,EAAE,CAAC,gBAAgB,CAAC;MAC7B,QAAQ,EAAE,CAAC,eAAe;IAC9B,CAAC;EAAE;AACP,CAAC;AACD,MAAMrB,UAAU,GAAIvE,KAAK,IAAK;EAC1B,IAAI9B,EAAE,EAAEuE,EAAE,EAAE4B,EAAE;EACd,OAAOZ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAAE,aAAa,EAAE,IAAI;IAAE,sBAAsB,EAAE,CAAC,CAACjB,EAAE,GAAG,CAACvE,EAAE,GAAG8B,KAAK,CAACuB,UAAU,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,QAAQ,MAAM,IAAI,IAAIiD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGzC,KAAK,CAACR,QAAQ,KAAK;EAAM,CAAC,EAAE3F,WAAW,CAACmG,KAAK,CAACsB,QAAQ,CAAC,CAAC,EAAEzH,WAAW,CAACmG,KAAK,CAACuB,UAAU,GAAG,CAAC8C,EAAE,GAAGrE,KAAK,CAACuB,UAAU,CAAC2C,KAAK,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACxX,CAAC;AACD,MAAMlB,WAAW,GAAItB,MAAM,IAAK;EAC5B,OAAOI,MAAM,CAACC,MAAM,CAAC;IAAE,cAAc,EAAE,IAAI;IAAE,eAAe,EAAE,IAAI;IAAE,iBAAiB,EAAE,IAAI;IAAE,CAAC,qBAAqBL,MAAM,CAACjG,IAAI,EAAE,GAAGiG,MAAM,CAACjG,IAAI,KAAKH;EAAU,CAAC,EAAEpD,WAAW,CAACwJ,MAAM,CAAC/B,QAAQ,CAAC,CAAC;AACjM,CAAC;AACDjG,KAAK,CAACiK,KAAK,GAAG;EACVQ,GAAG,EAAE3K,WAAW;EAChB4K,EAAE,EAAE3K;AACR,CAAC;AAED,SAASC,KAAK,IAAI2K,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}