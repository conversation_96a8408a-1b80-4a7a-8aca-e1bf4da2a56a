{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction parseCookieValue(cookieStr, name) {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {}\nexport { XhrFactory, parseCookieValue };", "map": {"version": 3, "names": ["parseCookieValue", "cookieStr", "name", "encodeURIComponent", "cookie", "split", "eqIndex", "indexOf", "cookieName", "cookieValue", "slice", "trim", "decodeURIComponent", "XhrFactory"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/common/fesm2022/xhr.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction parseCookieValue(cookieStr, name) {\n    name = encodeURIComponent(name);\n    for (const cookie of cookieStr.split(';')) {\n        const eqIndex = cookie.indexOf('=');\n        const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n        if (cookieName.trim() === name) {\n            return decodeURIComponent(cookieValue);\n        }\n    }\n    return null;\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {\n}\n\nexport { XhrFactory, parseCookieValue };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACvCA,IAAI,GAAGC,kBAAkB,CAACD,IAAI,CAAC;EAC/B,KAAK,MAAME,MAAM,IAAIH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,EAAE;IACvC,MAAMC,OAAO,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAGH,OAAO,IAAI,CAAC,CAAC,GAAG,CAACF,MAAM,EAAE,EAAE,CAAC,GAAG,CAACA,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAEF,MAAM,CAACM,KAAK,CAACJ,OAAO,GAAG,CAAC,CAAC,CAAC;IACtH,IAAIE,UAAU,CAACG,IAAI,CAAC,CAAC,KAAKT,IAAI,EAAE;MAC5B,OAAOU,kBAAkB,CAACH,WAAW,CAAC;IAC1C;EACJ;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,CAAC;AAGjB,SAASA,UAAU,EAAEb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}