{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.125rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.375rem, 55.836px);--checkmark-width:1.5px}:host(.checkbox-disabled){opacity:0.3}\";\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}\";\nconst Checkbox = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inputId = `ion-cb-${checkboxIds++}`;\n    this.inputLabelId = `${this.inputId}-lbl`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    /**\n     * The name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * If `true`, the checkbox is selected.\n     */\n    this.checked = false;\n    /**\n     * If `true`, the checkbox will visually appear as indeterminate.\n     */\n    this.indeterminate = false;\n    /**\n     * If `true`, the user cannot interact with the checkbox.\n     */\n    this.disabled = false;\n    /**\n     * The value of the checkbox does not mean if it's checked or not, use the `checked`\n     * property for that.\n     *\n     * The value of a checkbox is analogous to the value of an `<input type=\"checkbox\">`,\n     * it's only used when the checkbox participates in a native `<form>`.\n     */\n    this.value = 'on';\n    /**\n     * Where to place the label relative to the checkbox.\n     * `\"start\"`: The label will appear to the left of the checkbox in LTR and to the right in RTL.\n     * `\"end\"`: The label will appear to the right of the checkbox in LTR and to the left in RTL.\n     * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n     * `\"stacked\"`: The label will appear above the checkbox regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n     */\n    this.labelPlacement = 'start';\n    /**\n     * If true, screen readers will announce it as a required field. This property\n     * works only for accessibility purposes, it will not prevent the form from\n     * submitting if the value is invalid.\n     */\n    this.required = false;\n    /**\n     * Sets the checked property and emits\n     * the ionChange event. Use this to update the\n     * checked state in response to user-generated\n     * actions such as a click.\n     */\n    this.setChecked = state => {\n      const isChecked = this.checked = state;\n      this.ionChange.emit({\n        checked: isChecked,\n        value: this.value\n      });\n    };\n    this.toggleChecked = ev => {\n      ev.preventDefault();\n      this.setFocus();\n      this.setChecked(!this.checked);\n      this.indeterminate = false;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onKeyDown = ev => {\n      if (ev.key === ' ') {\n        ev.preventDefault();\n        if (!this.disabled) {\n          this.toggleChecked(ev);\n        }\n      }\n    };\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      this.toggleChecked(ev);\n    };\n    /**\n     * Stops propagation when the display label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onDivLabelClick = ev => {\n      ev.stopPropagation();\n    };\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n  }\n  /** @internal */\n  setFocus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.focusEl) {\n        _this.focusEl.focus();\n      }\n    })();\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Responsible for rendering helper text and error text.\n   * This element should only be rendered if hint text is set.\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"checkbox-bottom\"\n    }, h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText));\n  }\n  render() {\n    const {\n      color,\n      checked,\n      disabled,\n      el,\n      getSVGPath,\n      indeterminate,\n      inheritedAttributes,\n      inputId,\n      justify,\n      labelPlacement,\n      name,\n      value,\n      alignment,\n      required\n    } = this;\n    const mode = getIonMode(this);\n    const path = getSVGPath(mode, indeterminate);\n    const hasLabelContent = el.textContent !== '';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    // The host element must have a checkbox role to ensure proper VoiceOver\n    // support in Safari for accessibility.\n    return h(Host, {\n      key: '26cbe7220e555107200e9b5deeae754aa534a80b',\n      role: \"checkbox\",\n      \"aria-checked\": indeterminate ? 'mixed' : `${checked}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-labelledby\": hasLabelContent ? this.inputLabelId : null,\n      \"aria-label\": inheritedAttributes['aria-label'] || null,\n      \"aria-disabled\": disabled ? 'true' : null,\n      tabindex: disabled ? undefined : 0,\n      onKeyDown: this.onKeyDown,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        interactive: true,\n        [`checkbox-justify-${justify}`]: justify !== undefined,\n        [`checkbox-alignment-${alignment}`]: alignment !== undefined,\n        [`checkbox-label-placement-${labelPlacement}`]: true\n      }),\n      onClick: this.onClick\n    }, h(\"label\", {\n      key: 'f025cec5ff08e8be4487b9cc0324616ca5dfae2a',\n      class: \"checkbox-wrapper\",\n      htmlFor: inputId\n    }, h(\"input\", Object.assign({\n      key: 'dc53f7e4e240dc2e18556e6350df2b5c3169f553',\n      type: \"checkbox\",\n      checked: checked ? true : undefined,\n      disabled: disabled,\n      id: inputId,\n      onChange: this.toggleChecked,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl,\n      required: required\n    }, inheritedAttributes)), h(\"div\", {\n      key: 'a625e9b50c3b617de8bbbfd624d772454fecaf2d',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabelContent\n      },\n      part: \"label\",\n      id: this.inputLabelId,\n      onClick: this.onDivLabelClick\n    }, h(\"slot\", {\n      key: '87d1a90691327945f4343406706e4ab27f453844'\n    }), this.renderHintText()), h(\"div\", {\n      key: 'b57fed8cdecee4df1ef0d57f157267ee77fac653',\n      class: \"native-wrapper\"\n    }, h(\"svg\", {\n      key: 'd472a06ec6c8b74dfb651415d2236db8080f6805',\n      class: \"checkbox-icon\",\n      viewBox: \"0 0 24 24\",\n      part: \"container\"\n    }, path))));\n  }\n  getSVGPath(mode, indeterminate) {\n    let path = indeterminate ? h(\"path\", {\n      d: \"M6 12L18 12\",\n      part: \"mark\"\n    }) : h(\"path\", {\n      d: \"M5.9,12.5l3.8,3.8l8.8-8.8\",\n      part: \"mark\"\n    });\n    if (mode === 'md') {\n      path = indeterminate ? h(\"path\", {\n        d: \"M2 12H22\",\n        part: \"mark\"\n      }) : h(\"path\", {\n        d: \"M1.73,12.91 8.1,19.28 22.79,4.59\",\n        part: \"mark\"\n      });\n    }\n    return path;\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n  ios: checkboxIosCss,\n  md: checkboxMdCss\n};\nexport { Checkbox as ion_checkbox };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "i", "inheritAriaAttributes", "a", "renderHiddenInput", "c", "createColorClasses", "hostContext", "checkboxIosCss", "checkboxMdCss", "Checkbox", "constructor", "hostRef", "ionChange", "ionFocus", "ionBlur", "inputId", "checkboxIds", "inputLabelId", "helperTextId", "errorTextId", "inheritedAttributes", "name", "checked", "indeterminate", "disabled", "value", "labelPlacement", "required", "setChecked", "state", "isChecked", "emit", "toggleChecked", "ev", "preventDefault", "setFocus", "onFocus", "onBlur", "onKeyDown", "key", "onClick", "onDivLabelClick", "stopPropagation", "componentWillLoad", "Object", "assign", "el", "_this", "_asyncToGenerator", "focusEl", "focus", "getHintTextID", "helperText", "errorText", "classList", "contains", "undefined", "renderHintText", "hasHintText", "class", "id", "part", "render", "color", "getSV<PERSON>ath", "justify", "alignment", "mode", "path", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "role", "tabindex", "interactive", "htmlFor", "type", "onChange", "ref", "viewBox", "style", "ios", "md", "ion_checkbox"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\n\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.125rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.375rem, 55.836px);--checkmark-width:1.5px}:host(.checkbox-disabled){opacity:0.3}\";\n\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}\";\n\nconst Checkbox = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-cb-${checkboxIds++}`;\n        this.inputLabelId = `${this.inputId}-lbl`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.inheritedAttributes = {};\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the checkbox is selected.\n         */\n        this.checked = false;\n        /**\n         * If `true`, the checkbox will visually appear as indeterminate.\n         */\n        this.indeterminate = false;\n        /**\n         * If `true`, the user cannot interact with the checkbox.\n         */\n        this.disabled = false;\n        /**\n         * The value of the checkbox does not mean if it's checked or not, use the `checked`\n         * property for that.\n         *\n         * The value of a checkbox is analogous to the value of an `<input type=\"checkbox\">`,\n         * it's only used when the checkbox participates in a native `<form>`.\n         */\n        this.value = 'on';\n        /**\n         * Where to place the label relative to the checkbox.\n         * `\"start\"`: The label will appear to the left of the checkbox in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the checkbox in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the checkbox regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n         */\n        this.labelPlacement = 'start';\n        /**\n         * If true, screen readers will announce it as a required field. This property\n         * works only for accessibility purposes, it will not prevent the form from\n         * submitting if the value is invalid.\n         */\n        this.required = false;\n        /**\n         * Sets the checked property and emits\n         * the ionChange event. Use this to update the\n         * checked state in response to user-generated\n         * actions such as a click.\n         */\n        this.setChecked = (state) => {\n            const isChecked = (this.checked = state);\n            this.ionChange.emit({\n                checked: isChecked,\n                value: this.value,\n            });\n        };\n        this.toggleChecked = (ev) => {\n            ev.preventDefault();\n            this.setFocus();\n            this.setChecked(!this.checked);\n            this.indeterminate = false;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.onKeyDown = (ev) => {\n            if (ev.key === ' ') {\n                ev.preventDefault();\n                if (!this.disabled) {\n                    this.toggleChecked(ev);\n                }\n            }\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            this.toggleChecked(ev);\n        };\n        /**\n         * Stops propagation when the display label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onDivLabelClick = (ev) => {\n            ev.stopPropagation();\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n    /** @internal */\n    async setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    /**\n     * Responsible for rendering helper text and error text.\n     * This element should only be rendered if hint text is set.\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return (h(\"div\", { class: \"checkbox-bottom\" }, h(\"div\", { id: helperTextId, class: \"helper-text\", part: \"supporting-text helper-text\" }, helperText), h(\"div\", { id: errorTextId, class: \"error-text\", part: \"supporting-text error-text\" }, errorText)));\n    }\n    render() {\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inheritedAttributes, inputId, justify, labelPlacement, name, value, alignment, required, } = this;\n        const mode = getIonMode(this);\n        const path = getSVGPath(mode, indeterminate);\n        const hasLabelContent = el.textContent !== '';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        // The host element must have a checkbox role to ensure proper VoiceOver\n        // support in Safari for accessibility.\n        return (h(Host, { key: '26cbe7220e555107200e9b5deeae754aa534a80b', role: \"checkbox\", \"aria-checked\": indeterminate ? 'mixed' : `${checked}`, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId, \"aria-labelledby\": hasLabelContent ? this.inputLabelId : null, \"aria-label\": inheritedAttributes['aria-label'] || null, \"aria-disabled\": disabled ? 'true' : null, tabindex: disabled ? undefined : 0, onKeyDown: this.onKeyDown, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                interactive: true,\n                [`checkbox-justify-${justify}`]: justify !== undefined,\n                [`checkbox-alignment-${alignment}`]: alignment !== undefined,\n                [`checkbox-label-placement-${labelPlacement}`]: true,\n            }), onClick: this.onClick }, h(\"label\", { key: 'f025cec5ff08e8be4487b9cc0324616ca5dfae2a', class: \"checkbox-wrapper\", htmlFor: inputId }, h(\"input\", Object.assign({ key: 'dc53f7e4e240dc2e18556e6350df2b5c3169f553', type: \"checkbox\", checked: checked ? true : undefined, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl), required: required }, inheritedAttributes)), h(\"div\", { key: 'a625e9b50c3b617de8bbbfd624d772454fecaf2d', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabelContent,\n            }, part: \"label\", id: this.inputLabelId, onClick: this.onDivLabelClick }, h(\"slot\", { key: '87d1a90691327945f4343406706e4ab27f453844' }), this.renderHintText()), h(\"div\", { key: 'b57fed8cdecee4df1ef0d57f157267ee77fac653', class: \"native-wrapper\" }, h(\"svg\", { key: 'd472a06ec6c8b74dfb651415d2236db8080f6805', class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path)))));\n    }\n    getSVGPath(mode, indeterminate) {\n        let path = indeterminate ? (h(\"path\", { d: \"M6 12L18 12\", part: \"mark\" })) : (h(\"path\", { d: \"M5.9,12.5l3.8,3.8l8.8-8.8\", part: \"mark\" }));\n        if (mode === 'md') {\n            path = indeterminate ? (h(\"path\", { d: \"M2 12H22\", part: \"mark\" })) : (h(\"path\", { d: \"M1.73,12.91 8.1,19.28 22.79,4.59\", part: \"mark\" }));\n        }\n        return path;\n    }\n    get el() { return getElement(this); }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n    ios: checkboxIosCss,\n    md: checkboxMdCss\n};\n\nexport { Checkbox as ion_checkbox };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC7H,SAASC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,uBAAuB;AAC1F,SAASC,CAAC,IAAIC,kBAAkB,EAAEZ,CAAC,IAAIa,WAAW,QAAQ,qBAAqB;AAE/E,MAAMC,cAAc,GAAG,qjMAAqjM;AAE5kM,MAAMC,aAAa,GAAG,w8MAAw8M;AAE99M,MAAMC,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBrB,gBAAgB,CAAC,IAAI,EAAEqB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGpB,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqB,QAAQ,GAAGrB,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsB,OAAO,GAAGtB,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACuB,OAAO,GAAG,UAAUC,WAAW,EAAE,EAAE;IACxC,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACF,OAAO,MAAM;IACzC,IAAI,CAACG,YAAY,GAAG,GAAG,IAAI,CAACH,OAAO,cAAc;IACjD,IAAI,CAACI,WAAW,GAAG,GAAG,IAAI,CAACJ,OAAO,aAAa;IAC/C,IAAI,CAACK,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACN,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAIC,KAAK,IAAK;MACzB,MAAMC,SAAS,GAAI,IAAI,CAACR,OAAO,GAAGO,KAAM;MACxC,IAAI,CAACjB,SAAS,CAACmB,IAAI,CAAC;QAChBT,OAAO,EAAEQ,SAAS;QAClBL,KAAK,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACO,aAAa,GAAIC,EAAE,IAAK;MACzBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACnB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACP,UAAU,CAAC,CAAC,IAAI,CAACN,OAAO,CAAC;MAC9B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC9B,CAAC;IACD,IAAI,CAACa,OAAO,GAAG,MAAM;MACjB,IAAI,CAACvB,QAAQ,CAACkB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACM,MAAM,GAAG,MAAM;MAChB,IAAI,CAACvB,OAAO,CAACiB,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACO,SAAS,GAAIL,EAAE,IAAK;MACrB,IAAIA,EAAE,CAACM,GAAG,KAAK,GAAG,EAAE;QAChBN,EAAE,CAACC,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE;UAChB,IAAI,CAACQ,aAAa,CAACC,EAAE,CAAC;QAC1B;MACJ;IACJ,CAAC;IACD,IAAI,CAACO,OAAO,GAAIP,EAAE,IAAK;MACnB,IAAI,IAAI,CAACT,QAAQ,EAAE;QACf;MACJ;MACA,IAAI,CAACQ,aAAa,CAACC,EAAE,CAAC;IAC1B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACQ,eAAe,GAAIR,EAAE,IAAK;MAC3BA,EAAE,CAACS,eAAe,CAAC,CAAC;IACxB,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvB,mBAAmB,GAAGwB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5C,qBAAqB,CAAC,IAAI,CAAC6C,EAAE,CAAC,CAAC;EAChF;EACA;EACMX,QAAQA,CAAA,EAAG;IAAA,IAAAY,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAID,KAAI,CAACE,OAAO,EAAE;QACdF,KAAI,CAACE,OAAO,CAACC,KAAK,CAAC,CAAC;MACxB;IAAC;EACL;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEL,EAAE;MAAEM,UAAU;MAAEC,SAAS;MAAEnC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAI2B,EAAE,CAACQ,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIT,EAAE,CAACQ,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIF,SAAS,EAAE;MAC3F,OAAOlC,WAAW;IACtB;IACA,IAAIiC,UAAU,EAAE;MACZ,OAAOlC,YAAY;IACvB;IACA,OAAOsC,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEL,UAAU;MAAEC,SAAS;MAAEnC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE;AACR;AACA;AACA;IACQ,MAAMuC,WAAW,GAAG,CAAC,CAACN,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,IAAI,CAACK,WAAW,EAAE;MACd;IACJ;IACA,OAAQjE,CAAC,CAAC,KAAK,EAAE;MAAEkE,KAAK,EAAE;IAAkB,CAAC,EAAElE,CAAC,CAAC,KAAK,EAAE;MAAEmE,EAAE,EAAE1C,YAAY;MAAEyC,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE;IAA8B,CAAC,EAAET,UAAU,CAAC,EAAE3D,CAAC,CAAC,KAAK,EAAE;MAAEmE,EAAE,EAAEzC,WAAW;MAAEwC,KAAK,EAAE,YAAY;MAAEE,IAAI,EAAE;IAA6B,CAAC,EAAER,SAAS,CAAC,CAAC;EAC5P;EACAS,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,KAAK;MAAEzC,OAAO;MAAEE,QAAQ;MAAEsB,EAAE;MAAEkB,UAAU;MAAEzC,aAAa;MAAEH,mBAAmB;MAAEL,OAAO;MAAEkD,OAAO;MAAEvC,cAAc;MAAEL,IAAI;MAAEI,KAAK;MAAEyC,SAAS;MAAEvC;IAAU,CAAC,GAAG,IAAI;IAClK,MAAMwC,IAAI,GAAGxE,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyE,IAAI,GAAGJ,UAAU,CAACG,IAAI,EAAE5C,aAAa,CAAC;IAC5C,MAAM8C,eAAe,GAAGvB,EAAE,CAACwB,WAAW,KAAK,EAAE;IAC7CnE,iBAAiB,CAAC,IAAI,EAAE2C,EAAE,EAAEzB,IAAI,EAAEC,OAAO,GAAGG,KAAK,GAAG,EAAE,EAAED,QAAQ,CAAC;IACjE;IACA;IACA,OAAQ/B,CAAC,CAACI,IAAI,EAAE;MAAE0C,GAAG,EAAE,0CAA0C;MAAEgC,IAAI,EAAE,UAAU;MAAE,cAAc,EAAEhD,aAAa,GAAG,OAAO,GAAG,GAAGD,OAAO,EAAE;MAAE,kBAAkB,EAAE,IAAI,CAAC6B,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAK,IAAI,CAAChC,WAAW;MAAE,iBAAiB,EAAEkD,eAAe,GAAG,IAAI,CAACpD,YAAY,GAAG,IAAI;MAAE,YAAY,EAAEG,mBAAmB,CAAC,YAAY,CAAC,IAAI,IAAI;MAAE,eAAe,EAAEI,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEgD,QAAQ,EAAEhD,QAAQ,GAAGgC,SAAS,GAAG,CAAC;MAAElB,SAAS,EAAE,IAAI,CAACA,SAAS;MAAEqB,KAAK,EAAEtD,kBAAkB,CAAC0D,KAAK,EAAE;QAC7e,CAACI,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE7D,WAAW,CAAC,UAAU,EAAEwC,EAAE,CAAC;QACtC,kBAAkB,EAAExB,OAAO;QAC3B,mBAAmB,EAAEE,QAAQ;QAC7B,wBAAwB,EAAED,aAAa;QACvCkD,WAAW,EAAE,IAAI;QACjB,CAAC,oBAAoBR,OAAO,EAAE,GAAGA,OAAO,KAAKT,SAAS;QACtD,CAAC,sBAAsBU,SAAS,EAAE,GAAGA,SAAS,KAAKV,SAAS;QAC5D,CAAC,4BAA4B9B,cAAc,EAAE,GAAG;MACpD,CAAC,CAAC;MAAEc,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAE/C,CAAC,CAAC,OAAO,EAAE;MAAE8C,GAAG,EAAE,0CAA0C;MAAEoB,KAAK,EAAE,kBAAkB;MAAEe,OAAO,EAAE3D;IAAQ,CAAC,EAAEtB,CAAC,CAAC,OAAO,EAAEmD,MAAM,CAACC,MAAM,CAAC;MAAEN,GAAG,EAAE,0CAA0C;MAAEoC,IAAI,EAAE,UAAU;MAAErD,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGkC,SAAS;MAAEhC,QAAQ,EAAEA,QAAQ;MAAEoC,EAAE,EAAE7C,OAAO;MAAE6D,QAAQ,EAAE,IAAI,CAAC5C,aAAa;MAAEI,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAEwC,GAAG,EAAG5B,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA,OAAQ;MAAEtB,QAAQ,EAAEA;IAAS,CAAC,EAAEP,mBAAmB,CAAC,CAAC,EAAE3B,CAAC,CAAC,KAAK,EAAE;MAAE8C,GAAG,EAAE,0CAA0C;MAAEoB,KAAK,EAAE;QAChiB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACU;MAClC,CAAC;MAAER,IAAI,EAAE,OAAO;MAAED,EAAE,EAAE,IAAI,CAAC3C,YAAY;MAAEuB,OAAO,EAAE,IAAI,CAACC;IAAgB,CAAC,EAAEhD,CAAC,CAAC,MAAM,EAAE;MAAE8C,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE,IAAI,CAACkB,cAAc,CAAC,CAAC,CAAC,EAAEhE,CAAC,CAAC,KAAK,EAAE;MAAE8C,GAAG,EAAE,0CAA0C;MAAEoB,KAAK,EAAE;IAAiB,CAAC,EAAElE,CAAC,CAAC,KAAK,EAAE;MAAE8C,GAAG,EAAE,0CAA0C;MAAEoB,KAAK,EAAE,eAAe;MAAEmB,OAAO,EAAE,WAAW;MAAEjB,IAAI,EAAE;IAAY,CAAC,EAAEO,IAAI,CAAC,CAAC,CAAC,CAAC;EACxY;EACAJ,UAAUA,CAACG,IAAI,EAAE5C,aAAa,EAAE;IAC5B,IAAI6C,IAAI,GAAG7C,aAAa,GAAI9B,CAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,aAAa;MAAEsE,IAAI,EAAE;IAAO,CAAC,CAAC,GAAKpE,CAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,2BAA2B;MAAEsE,IAAI,EAAE;IAAO,CAAC,CAAE;IAC1I,IAAIM,IAAI,KAAK,IAAI,EAAE;MACfC,IAAI,GAAG7C,aAAa,GAAI9B,CAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,UAAU;QAAEsE,IAAI,EAAE;MAAO,CAAC,CAAC,GAAKpE,CAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,kCAAkC;QAAEsE,IAAI,EAAE;MAAO,CAAC,CAAE;IAC9I;IACA,OAAOO,IAAI;EACf;EACA,IAAItB,EAAEA,CAAA,EAAG;IAAE,OAAO/C,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAIiB,WAAW,GAAG,CAAC;AACnBP,QAAQ,CAACsE,KAAK,GAAG;EACbC,GAAG,EAAEzE,cAAc;EACnB0E,EAAE,EAAEzE;AACR,CAAC;AAED,SAASC,QAAQ,IAAIyE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}