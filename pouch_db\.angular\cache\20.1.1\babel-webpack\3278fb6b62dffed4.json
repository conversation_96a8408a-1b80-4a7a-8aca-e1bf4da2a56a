{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\n/**\n * iOS Loading Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Loading Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Enter Animation\n */\nconst mdEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst loadingIosCss = \".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst loadingMdCss = \".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst Loading = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionLoadingDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionLoadingWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionLoadingWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionLoadingDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.presented = false;\n    /** @internal */\n    this.hasController = false;\n    /**\n     * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n     */\n    this.keyboardClose = true;\n    /**\n     * Number of milliseconds to wait before dismissing the loading indicator.\n     */\n    this.duration = 0;\n    /**\n     * If `true`, the loading indicator will be dismissed when the backdrop is clicked.\n     */\n    this.backdropDismiss = false;\n    /**\n     * If `true`, a backdrop will be displayed behind the loading indicator.\n     */\n    this.showBackdrop = true;\n    /**\n     * If `true`, the loading indicator will be translucent.\n     * Only applies when the mode is `\"ios\"` and the device supports\n     * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n     */\n    this.translucent = false;\n    /**\n     * If `true`, the loading indicator will animate.\n     */\n    this.animated = true;\n    /**\n     * If `true`, the loading indicator will open. If `false`, the loading indicator will close.\n     * Use this if you need finer grained control over presentation, otherwise\n     * just use the loadingController or the `trigger` property.\n     * Note: `isOpen` will not automatically be set back to `false` when\n     * the loading indicator dismisses. You will need to do that in your code.\n     */\n    this.isOpen = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  componentWillLoad() {\n    var _a;\n    if (this.spinner === undefined) {\n      const mode = getIonMode(this);\n      this.spinner = config.get('loadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    /**\n     * If loading indicator was rendered with isOpen=\"true\"\n     * then we should open loading indicator immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  /**\n   * Present the loading overlay after it has been created.\n   */\n  present() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this.lockController.lock();\n      yield _this.delegateController.attachViewToDom();\n      yield present(_this, 'loadingEnter', iosEnterAnimation, mdEnterAnimation);\n      if (_this.duration > 0) {\n        _this.durationTimeout = setTimeout(() => _this.dismiss(), _this.duration + 10);\n      }\n      unlock();\n    })();\n  }\n  /**\n   * Dismiss the loading overlay after it has been presented.\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the loading.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the loading. Some examples include:\n   * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n   */\n  dismiss(data, role) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this2.lockController.lock();\n      if (_this2.durationTimeout) {\n        clearTimeout(_this2.durationTimeout);\n      }\n      const dismissed = yield dismiss(_this2, data, role, 'loadingLeave', iosLeaveAnimation, mdLeaveAnimation);\n      if (dismissed) {\n        _this2.delegateController.removeViewFromDom();\n      }\n      unlock();\n      return dismissed;\n    })();\n  }\n  /**\n   * Returns a promise that resolves when the loading did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionLoadingDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the loading will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionLoadingWillDismiss');\n  }\n  renderLoadingMessage(msgId) {\n    const {\n      customHTMLEnabled,\n      message\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"loading-content\",\n        id: msgId,\n        innerHTML: sanitizeDOMString(message)\n      });\n    }\n    return h(\"div\", {\n      class: \"loading-content\",\n      id: msgId\n    }, message);\n  }\n  render() {\n    const {\n      message,\n      spinner,\n      htmlAttributes,\n      overlayIndex\n    } = this;\n    const mode = getIonMode(this);\n    const msgId = `loading-${overlayIndex}-msg`;\n    /**\n     * If the message is defined, use that as the label.\n     * Otherwise, don't set aria-labelledby.\n     */\n    const ariaLabelledBy = message !== undefined ? msgId : null;\n    return h(Host, Object.assign({\n      key: '4497183ce220242abe19ae15f328f9a92ccafbbc',\n      role: \"dialog\",\n      \"aria-modal\": \"true\",\n      \"aria-labelledby\": ariaLabelledBy,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${40000 + this.overlayIndex}`\n      },\n      onIonBackdropTap: this.onBackdropTap,\n      class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), {\n        [mode]: true,\n        'overlay-hidden': true,\n        'loading-translucent': this.translucent\n      })\n    }), h(\"ion-backdrop\", {\n      key: '231dec84e424a2dc358ce95b84d6035cf43e4dea',\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: 'c9af29b6e6bb49a217396a5c874bbfb8835a926c',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", {\n      key: 'a8659863743cdeccbe1ba810eaabfd3ebfcb86f3',\n      class: \"loading-wrapper ion-overlay-wrapper\"\n    }, spinner && h(\"div\", {\n      key: '3b346f39bc71691bd8686556a1e142198a7b12fa',\n      class: \"loading-spinner\"\n    }, h(\"ion-spinner\", {\n      key: '8dc2bf1556e5138e262827f1516c59ecd09f3520',\n      name: spinner,\n      \"aria-hidden\": \"true\"\n    })), message !== undefined && this.renderLoadingMessage(msgId)), h(\"div\", {\n      key: '054164c0dbae9a0e0973dd3c8e28f5b771820310',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nLoading.style = {\n  ios: loadingIosCss,\n  md: loadingMdCss\n};\nexport { Loading as ion_loading };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "l", "config", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "raf", "c", "createLockController", "createDelegateController", "createTriggerController", "B", "BACKDROP", "prepareOverlay", "setOverlayId", "f", "present", "g", "dismiss", "eventMethod", "getClassMap", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "keyframes", "offset", "opacity", "transform", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "loadingIosCss", "loadingMdCss", "Loading", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "customHTMLEnabled", "get", "presented", "hasController", "keyboardClose", "<PERSON><PERSON><PERSON><PERSON>", "showBackdrop", "translucent", "animated", "isOpen", "onBackdropTap", "undefined", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "trigger", "el", "addClickListener", "connectedCallback", "componentWillLoad", "_a", "spinner", "mode", "htmlAttributes", "id", "componentDidLoad", "disconnectedCallback", "removeClickListener", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "durationTimeout", "setTimeout", "data", "role", "_this2", "clearTimeout", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "renderLoadingMessage", "msgId", "message", "class", "innerHTML", "render", "overlayIndex", "ariaLabelledBy", "Object", "assign", "key", "tabindex", "style", "zIndex", "onIonBackdropTap", "cssClass", "visible", "tappable", "name", "watchers", "ios", "md", "ion_loading"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-loading.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\n/**\n * iOS Loading Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.01, transform: 'scale(1.1)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Loading Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.99, transform: 'scale(1)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.9)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.01, transform: 'scale(1.1)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.99, transform: 'scale(1)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.9)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst loadingIosCss = \".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}\";\n\nconst loadingMdCss = \".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}\";\n\nconst Loading = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionLoadingDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionLoadingWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionLoadingWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionLoadingDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * Number of milliseconds to wait before dismissing the loading indicator.\n         */\n        this.duration = 0;\n        /**\n         * If `true`, the loading indicator will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = false;\n        /**\n         * If `true`, a backdrop will be displayed behind the loading indicator.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the loading indicator will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the loading indicator will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the loading indicator will open. If `false`, the loading indicator will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the loadingController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the loading indicator dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    componentWillLoad() {\n        var _a;\n        if (this.spinner === undefined) {\n            const mode = getIonMode(this);\n            this.spinner = config.get('loadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n        }\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * If loading indicator was rendered with isOpen=\"true\"\n         * then we should open loading indicator immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n    }\n    /**\n     * Present the loading overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'loadingEnter', iosEnterAnimation, mdEnterAnimation);\n        if (this.duration > 0) {\n            this.durationTimeout = setTimeout(() => this.dismiss(), this.duration + 10);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the loading overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the loading.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the loading. Some examples include:\n     * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        if (this.durationTimeout) {\n            clearTimeout(this.durationTimeout);\n        }\n        const dismissed = await dismiss(this, data, role, 'loadingLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the loading did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionLoadingDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the loading will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionLoadingWillDismiss');\n    }\n    renderLoadingMessage(msgId) {\n        const { customHTMLEnabled, message } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"loading-content\", id: msgId, innerHTML: sanitizeDOMString(message) });\n        }\n        return (h(\"div\", { class: \"loading-content\", id: msgId }, message));\n    }\n    render() {\n        const { message, spinner, htmlAttributes, overlayIndex } = this;\n        const mode = getIonMode(this);\n        const msgId = `loading-${overlayIndex}-msg`;\n        /**\n         * If the message is defined, use that as the label.\n         * Otherwise, don't set aria-labelledby.\n         */\n        const ariaLabelledBy = message !== undefined ? msgId : null;\n        return (h(Host, Object.assign({ key: '4497183ce220242abe19ae15f328f9a92ccafbbc', role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": ariaLabelledBy, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${40000 + this.overlayIndex}`,\n            }, onIonBackdropTap: this.onBackdropTap, class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), { [mode]: true, 'overlay-hidden': true, 'loading-translucent': this.translucent }) }), h(\"ion-backdrop\", { key: '231dec84e424a2dc358ce95b84d6035cf43e4dea', visible: this.showBackdrop, tappable: this.backdropDismiss }), h(\"div\", { key: 'c9af29b6e6bb49a217396a5c874bbfb8835a926c', tabindex: \"0\", \"aria-hidden\": \"true\" }), h(\"div\", { key: 'a8659863743cdeccbe1ba810eaabfd3ebfcb86f3', class: \"loading-wrapper ion-overlay-wrapper\" }, spinner && (h(\"div\", { key: '3b346f39bc71691bd8686556a1e142198a7b12fa', class: \"loading-spinner\" }, h(\"ion-spinner\", { key: '8dc2bf1556e5138e262827f1516c59ecd09f3520', name: spinner, \"aria-hidden\": \"true\" }))), message !== undefined && this.renderLoadingMessage(msgId)), h(\"div\", { key: '054164c0dbae9a0e0973dd3c8e28f5b771820310', tabindex: \"0\", \"aria-hidden\": \"true\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nLoading.style = {\n    ios: loadingIosCss,\n    md: loadingMdCss\n};\n\nexport { Loading as ion_loading };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC1I,SAASC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,sBAAsB;AAC/F,SAAShB,CAAC,IAAIiB,GAAG,QAAQ,uBAAuB;AAChD,SAASC,CAAC,IAAIC,oBAAoB,QAAQ,+BAA+B;AACzE,SAASjB,CAAC,IAAIkB,wBAAwB,EAAEd,CAAC,IAAIe,uBAAuB,EAAEC,CAAC,IAAIC,QAAQ,EAAEd,CAAC,IAAIe,cAAc,EAAEb,CAAC,IAAIc,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,OAAO,EAAErB,CAAC,IAAIsB,WAAW,QAAQ,wBAAwB;AACzM,SAASF,CAAC,IAAIG,WAAW,QAAQ,qBAAqB;AACtD,SAASb,CAAC,IAAIc,eAAe,QAAQ,yBAAyB;AAC9D,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,kCAAkC;;AAEzC;AACA;AACA;AACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAa,CAAC,EACrD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAW,CAAC,CACnD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMa,iBAAiB,GAAIhB,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAW,CAAC,EACnD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMc,gBAAgB,GAAIjB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAa,CAAC,EACrD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAW,CAAC,CACnD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMe,gBAAgB,GAAIlB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAW,CAAC,EACnD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMgB,aAAa,GAAG,wgEAAwgE;AAE9hE,MAAMC,YAAY,GAAG,osDAAosD;AAEztD,MAAMC,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjBxD,gBAAgB,CAAC,IAAI,EAAEwD,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGvD,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACwD,WAAW,GAAGxD,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACyD,WAAW,GAAGzD,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC0D,UAAU,GAAG1D,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC2D,mBAAmB,GAAG3D,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4D,oBAAoB,GAAG5D,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC6D,oBAAoB,GAAG7D,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC8D,mBAAmB,GAAG9D,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC+D,kBAAkB,GAAG9C,wBAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAAC+C,cAAc,GAAGhD,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACiD,iBAAiB,GAAG/C,uBAAuB,CAAC,CAAC;IAClD,IAAI,CAACgD,iBAAiB,GAAGhE,MAAM,CAACiE,GAAG,CAAC,2BAA2B,EAAExD,2BAA2B,CAAC;IAC7F,IAAI,CAACyD,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACzB,QAAQ,GAAG,CAAC;IACjB;AACR;AACA;IACQ,IAAI,CAAC0B,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAClD,OAAO,CAACmD,SAAS,EAAEzD,QAAQ,CAAC;IACrC,CAAC;EACL;EACA0D,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACxD,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIuD,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACtD,OAAO,CAAC,CAAC;IAClB;EACJ;EACAuD,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,OAAO;MAAEC,EAAE;MAAElB;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIiB,OAAO,EAAE;MACTjB,iBAAiB,CAACmB,gBAAgB,CAACD,EAAE,EAAED,OAAO,CAAC;IACnD;EACJ;EACAG,iBAAiBA,CAAA,EAAG;IAChBhE,cAAc,CAAC,IAAI,CAAC8D,EAAE,CAAC;IACvB,IAAI,CAACF,cAAc,CAAC,CAAC;EACzB;EACAK,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,IAAI,IAAI,CAACC,OAAO,KAAKX,SAAS,EAAE;MAC5B,MAAMY,IAAI,GAAGrF,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACoF,OAAO,GAAGtF,MAAM,CAACiE,GAAG,CAAC,gBAAgB,EAAEjE,MAAM,CAACiE,GAAG,CAAC,SAAS,EAAEsB,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC7G;IACA,IAAI,EAAE,CAACF,EAAE,GAAG,IAAI,CAACG,cAAc,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,EAAE,CAAC,EAAE;MAC1ErE,YAAY,CAAC,IAAI,CAAC6D,EAAE,CAAC;IACzB;EACJ;EACAS,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACjB,MAAM,KAAK,IAAI,EAAE;MACtB7D,GAAG,CAAC,MAAM,IAAI,CAACU,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACyD,cAAc,CAAC,CAAC;EACzB;EACAY,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC5B,iBAAiB,CAAC6B,mBAAmB,CAAC,CAAC;EAChD;EACA;AACJ;AACA;EACUtE,OAAOA,CAAA,EAAG;IAAA,IAAAuE,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMC,MAAM,SAASF,KAAI,CAAC/B,cAAc,CAACkC,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAAChC,kBAAkB,CAACoC,eAAe,CAAC,CAAC;MAC/C,MAAM3E,OAAO,CAACuE,KAAI,EAAE,cAAc,EAAEjE,iBAAiB,EAAEkB,gBAAgB,CAAC;MACxE,IAAI+C,KAAI,CAAClD,QAAQ,GAAG,CAAC,EAAE;QACnBkD,KAAI,CAACK,eAAe,GAAGC,UAAU,CAAC,MAAMN,KAAI,CAACrE,OAAO,CAAC,CAAC,EAAEqE,KAAI,CAAClD,QAAQ,GAAG,EAAE,CAAC;MAC/E;MACAoD,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUvE,OAAOA,CAAC4E,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MACtB,MAAMC,MAAM,SAASO,MAAI,CAACxC,cAAc,CAACkC,IAAI,CAAC,CAAC;MAC/C,IAAIM,MAAI,CAACJ,eAAe,EAAE;QACtBK,YAAY,CAACD,MAAI,CAACJ,eAAe,CAAC;MACtC;MACA,MAAMM,SAAS,SAAShF,OAAO,CAAC8E,MAAI,EAAEF,IAAI,EAAEC,IAAI,EAAE,cAAc,EAAExD,iBAAiB,EAAEE,gBAAgB,CAAC;MACtG,IAAIyD,SAAS,EAAE;QACXF,MAAI,CAACzC,kBAAkB,CAAC4C,iBAAiB,CAAC,CAAC;MAC/C;MACAV,MAAM,CAAC,CAAC;MACR,OAAOS,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAOjF,WAAW,CAAC,IAAI,CAACwD,EAAE,EAAE,sBAAsB,CAAC;EACvD;EACA;AACJ;AACA;EACI0B,aAAaA,CAAA,EAAG;IACZ,OAAOlF,WAAW,CAAC,IAAI,CAACwD,EAAE,EAAE,uBAAuB,CAAC;EACxD;EACA2B,oBAAoBA,CAACC,KAAK,EAAE;IACxB,MAAM;MAAE7C,iBAAiB;MAAE8C;IAAQ,CAAC,GAAG,IAAI;IAC3C,IAAI9C,iBAAiB,EAAE;MACnB,OAAO7D,CAAC,CAAC,KAAK,EAAE;QAAE4G,KAAK,EAAE,iBAAiB;QAAEtB,EAAE,EAAEoB,KAAK;QAAEG,SAAS,EAAErG,iBAAiB,CAACmG,OAAO;MAAE,CAAC,CAAC;IACnG;IACA,OAAQ3G,CAAC,CAAC,KAAK,EAAE;MAAE4G,KAAK,EAAE,iBAAiB;MAAEtB,EAAE,EAAEoB;IAAM,CAAC,EAAEC,OAAO,CAAC;EACtE;EACAG,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEH,OAAO;MAAExB,OAAO;MAAEE,cAAc;MAAE0B;IAAa,CAAC,GAAG,IAAI;IAC/D,MAAM3B,IAAI,GAAGrF,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2G,KAAK,GAAG,WAAWK,YAAY,MAAM;IAC3C;AACR;AACA;AACA;IACQ,MAAMC,cAAc,GAAGL,OAAO,KAAKnC,SAAS,GAAGkC,KAAK,GAAG,IAAI;IAC3D,OAAQ1G,CAAC,CAACE,IAAI,EAAE+G,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE,0CAA0C;MAAEjB,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEc,cAAc;MAAEI,QAAQ,EAAE;IAAK,CAAC,EAAE/B,cAAc,EAAE;MAAEgC,KAAK,EAAE;QAC7LC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACP,YAAY;MACxC,CAAC;MAAEQ,gBAAgB,EAAE,IAAI,CAAChD,aAAa;MAAEqC,KAAK,EAAEK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3F,WAAW,CAAC,IAAI,CAACiG,QAAQ,CAAC,CAAC,EAAE;QAAE,CAACpC,IAAI,GAAG,IAAI;QAAE,gBAAgB,EAAE,IAAI;QAAE,qBAAqB,EAAE,IAAI,CAAChB;MAAY,CAAC;IAAE,CAAC,CAAC,EAAEpE,CAAC,CAAC,cAAc,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEM,OAAO,EAAE,IAAI,CAACtD,YAAY;MAAEuD,QAAQ,EAAE,IAAI,CAACxD;IAAgB,CAAC,CAAC,EAAElE,CAAC,CAAC,KAAK,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,EAAEpH,CAAC,CAAC,KAAK,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEP,KAAK,EAAE;IAAsC,CAAC,EAAEzB,OAAO,IAAKnF,CAAC,CAAC,KAAK,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEP,KAAK,EAAE;IAAkB,CAAC,EAAE5G,CAAC,CAAC,aAAa,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEQ,IAAI,EAAExC,OAAO;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,EAAEwB,OAAO,KAAKnC,SAAS,IAAI,IAAI,CAACiC,oBAAoB,CAACC,KAAK,CAAC,CAAC,EAAE1G,CAAC,CAAC,KAAK,EAAE;MAAEmH,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC;EACp5B;EACA,IAAItC,EAAEA,CAAA,EAAG;IAAE,OAAO1E,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD7E,OAAO,CAACsE,KAAK,GAAG;EACZQ,GAAG,EAAEhF,aAAa;EAClBiF,EAAE,EAAEhF;AACR,CAAC;AAED,SAASC,OAAO,IAAIgF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}