{"ast": null, "code": "import { distinctUntilChanged } from './distinctUntilChanged';\nexport function distinctUntilKeyChanged(key, compare) {\n  return distinctUntilChanged((x, y) => compare ? compare(x[key], y[key]) : x[key] === y[key]);\n}", "map": {"version": 3, "names": ["distinctUntilChanged", "distinctUntilKeyChanged", "key", "compare", "x", "y"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/rxjs/dist/esm/internal/operators/distinctUntilKeyChanged.js"], "sourcesContent": ["import { distinctUntilChanged } from './distinctUntilChanged';\nexport function distinctUntilKeyChanged(key, compare) {\n    return distinctUntilChanged((x, y) => (compare ? compare(x[key], y[key]) : x[key] === y[key]));\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,OAAO,SAASC,uBAAuBA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAClD,OAAOH,oBAAoB,CAAC,CAACI,CAAC,EAAEC,CAAC,KAAMF,OAAO,GAAGA,OAAO,CAACC,CAAC,CAACF,GAAG,CAAC,EAAEG,CAAC,CAACH,GAAG,CAAC,CAAC,GAAGE,CAAC,CAACF,GAAG,CAAC,KAAKG,CAAC,CAACH,GAAG,CAAE,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}