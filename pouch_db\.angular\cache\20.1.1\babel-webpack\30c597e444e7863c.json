{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomePage } from './home.page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomePage\n}];\nexport class HomePageRoutingModule {\n  static #_ = this.ɵfac = function HomePageRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomePageRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HomePageRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomePageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomePage", "routes", "path", "component", "HomePageRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { HomePage } from './home.page';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: HomePage,\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class HomePageRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,QAAQ,QAAQ,aAAa;;;AAEtC,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,qBAAqB;EAAA,QAAAC,CAAA,G;qCAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA,G;UAArBF;EAAqB;EAAA,QAAAG,EAAA,G;cAHtBR,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEXK,qBAAqB;IAAAK,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFtBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}