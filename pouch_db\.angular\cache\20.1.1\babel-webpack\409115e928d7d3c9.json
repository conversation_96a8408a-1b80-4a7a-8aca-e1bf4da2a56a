{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount = Infinity) {\n  let config;\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount\n    };\n  }\n  const {\n    count = Infinity,\n    delay,\n    resetOnSuccess = false\n  } = config;\n  return count <= 0 ? identity : operate((source, subscriber) => {\n    let soFar = 0;\n    let innerSub;\n    const subscribeForRetry = () => {\n      let syncUnsub = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, value => {\n        if (resetOnSuccess) {\n          soFar = 0;\n        }\n        subscriber.next(value);\n      }, undefined, err => {\n        if (soFar++ < count) {\n          const resub = () => {\n            if (innerSub) {\n              innerSub.unsubscribe();\n              innerSub = null;\n              subscribeForRetry();\n            } else {\n              syncUnsub = true;\n            }\n          };\n          if (delay != null) {\n            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n              notifierSubscriber.unsubscribe();\n              resub();\n            }, () => {\n              subscriber.complete();\n            });\n            notifier.subscribe(notifierSubscriber);\n          } else {\n            resub();\n          }\n        } else {\n          subscriber.error(err);\n        }\n      }));\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRetry();\n      }\n    };\n    subscribeForRetry();\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "identity", "timer", "innerFrom", "retry", "config<PERSON>r<PERSON>ount", "Infinity", "config", "count", "delay", "resetOnSuccess", "source", "subscriber", "soFar", "innerSub", "subscribeForRetry", "syncUnsub", "subscribe", "value", "next", "undefined", "err", "resub", "unsubscribe", "notifier", "notifierSubscriber", "complete", "error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/rxjs/dist/esm/internal/operators/retry.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount = Infinity) {\n    let config;\n    if (configOrCount && typeof configOrCount === 'object') {\n        config = configOrCount;\n    }\n    else {\n        config = {\n            count: configOrCount,\n        };\n    }\n    const { count = Infinity, delay, resetOnSuccess: resetOnSuccess = false } = config;\n    return count <= 0\n        ? identity\n        : operate((source, subscriber) => {\n            let soFar = 0;\n            let innerSub;\n            const subscribeForRetry = () => {\n                let syncUnsub = false;\n                innerSub = source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                    if (resetOnSuccess) {\n                        soFar = 0;\n                    }\n                    subscriber.next(value);\n                }, undefined, (err) => {\n                    if (soFar++ < count) {\n                        const resub = () => {\n                            if (innerSub) {\n                                innerSub.unsubscribe();\n                                innerSub = null;\n                                subscribeForRetry();\n                            }\n                            else {\n                                syncUnsub = true;\n                            }\n                        };\n                        if (delay != null) {\n                            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n                            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n                                notifierSubscriber.unsubscribe();\n                                resub();\n                            }, () => {\n                                subscriber.complete();\n                            });\n                            notifier.subscribe(notifierSubscriber);\n                        }\n                        else {\n                            resub();\n                        }\n                    }\n                    else {\n                        subscriber.error(err);\n                    }\n                }));\n                if (syncUnsub) {\n                    innerSub.unsubscribe();\n                    innerSub = null;\n                    subscribeForRetry();\n                }\n            };\n            subscribeForRetry();\n        });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,KAAKA,CAACC,aAAa,GAAGC,QAAQ,EAAE;EAC5C,IAAIC,MAAM;EACV,IAAIF,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IACpDE,MAAM,GAAGF,aAAa;EAC1B,CAAC,MACI;IACDE,MAAM,GAAG;MACLC,KAAK,EAAEH;IACX,CAAC;EACL;EACA,MAAM;IAAEG,KAAK,GAAGF,QAAQ;IAAEG,KAAK;IAAkBC,cAAc,GAAG;EAAM,CAAC,GAAGH,MAAM;EAClF,OAAOC,KAAK,IAAI,CAAC,GACXP,QAAQ,GACRF,OAAO,CAAC,CAACY,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ;IACZ,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC5B,IAAIC,SAAS,GAAG,KAAK;MACrBF,QAAQ,GAAGH,MAAM,CAACM,SAAS,CAACjB,wBAAwB,CAACY,UAAU,EAAGM,KAAK,IAAK;QACxE,IAAIR,cAAc,EAAE;UAChBG,KAAK,GAAG,CAAC;QACb;QACAD,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;MAC1B,CAAC,EAAEE,SAAS,EAAGC,GAAG,IAAK;QACnB,IAAIR,KAAK,EAAE,GAAGL,KAAK,EAAE;UACjB,MAAMc,KAAK,GAAGA,CAAA,KAAM;YAChB,IAAIR,QAAQ,EAAE;cACVA,QAAQ,CAACS,WAAW,CAAC,CAAC;cACtBT,QAAQ,GAAG,IAAI;cACfC,iBAAiB,CAAC,CAAC;YACvB,CAAC,MACI;cACDC,SAAS,GAAG,IAAI;YACpB;UACJ,CAAC;UACD,IAAIP,KAAK,IAAI,IAAI,EAAE;YACf,MAAMe,QAAQ,GAAG,OAAOf,KAAK,KAAK,QAAQ,GAAGP,KAAK,CAACO,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAACY,GAAG,EAAER,KAAK,CAAC,CAAC;YACxF,MAAMY,kBAAkB,GAAGzB,wBAAwB,CAACY,UAAU,EAAE,MAAM;cAClEa,kBAAkB,CAACF,WAAW,CAAC,CAAC;cAChCD,KAAK,CAAC,CAAC;YACX,CAAC,EAAE,MAAM;cACLV,UAAU,CAACc,QAAQ,CAAC,CAAC;YACzB,CAAC,CAAC;YACFF,QAAQ,CAACP,SAAS,CAACQ,kBAAkB,CAAC;UAC1C,CAAC,MACI;YACDH,KAAK,CAAC,CAAC;UACX;QACJ,CAAC,MACI;UACDV,UAAU,CAACe,KAAK,CAACN,GAAG,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAIL,SAAS,EAAE;QACXF,QAAQ,CAACS,WAAW,CAAC,CAAC;QACtBT,QAAQ,GAAG,IAAI;QACfC,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACDA,iBAAiB,CAAC,CAAC;EACvB,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}