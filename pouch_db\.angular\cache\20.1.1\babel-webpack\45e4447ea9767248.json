{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { BrowserModule, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, KeyEventsPlugin as ɵKeyEventsPlugin } from './browser.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, ɵglobal as _global, ApplicationRef, InjectionToken, ɵConsole as _Console, Optional, Injector, NgModule, forwardRef, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, ɵwithI18nSupport as _withI18nSupport, ɵwithEventReplay as _withEventReplay, ɵwithIncrementalHydration as _withIncrementalHydration, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, Version } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS } from './dom_renderer.mjs';\nexport { EventManager, REMOVE_STYLES_ON_COMPONENT_DESTROY, DomRendererFactory2 as ɵDomRendererFactory2, SharedStylesHost as ɵSharedStylesHost } from './dom_renderer.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  _doc;\n  _dom;\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = _getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static ɵfac = function Meta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: Meta.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  _doc;\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static ɵfac = function Title_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: Title.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/// <reference path=\"../../../goog.d.ts\" />\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = _global['ng'] = _global['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  msPerTick;\n  numTicks;\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  appRef;\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (_getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/// <reference types=\"hammerjs\" />\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nclass HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events = [];\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides = {};\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options;\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGestureConfig)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  _config;\n  _injector;\n  loader;\n  _loaderPromise = null;\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(_Console);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(_Console);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(_Console);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n *\n * @deprecated The hammer integration is deprecated. Replace it by your own implementation.\n */\nclass HammerModule {\n  static ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(__ngFactoryType__) {\n      let __ngConditionalFactory__ = null;\n      if (__ngFactoryType__) {\n        __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n      } else {\n        __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n      }\n      return __ngConditionalFactory__;\n    },\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return _unwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n      default:\n        throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return _bypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return _bypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return _bypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return _bypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return _bypassSanitizationTrustResourceUrl(value);\n  }\n  static ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: DomSanitizerImpl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @publicApi 20.0\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @publicApi 20.0\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(_ZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi 17.0\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    throw new _RuntimeError(5001 /* RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES */, 'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], _withDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : _withHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.1.2');\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };", "map": {"version": 3, "names": ["BrowserModule", "bootstrapApplication", "createApplication", "platformBrowser", "provideProtractorTestingSupport", "BrowserDomAdapter", "ɵBrowserDomAdapter", "BrowserGetTestability", "ɵBrowserGetTestability", "DomEventsPlugin", "ɵDomEventsPlugin", "KeyEventsPlugin", "ɵKeyEventsPlugin", "ɵgetDOM", "_getDOM", "DOCUMENT", "i0", "Injectable", "Inject", "ɵglobal", "_global", "ApplicationRef", "InjectionToken", "ɵConsole", "_Console", "Optional", "Injector", "NgModule", "forwardRef", "ɵRuntimeError", "_RuntimeError", "ɵXSS_SECURITY_URL", "_XSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "_allowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "_unwrapSafeValue", "ɵ_sanitizeUrl", "__sanitizeUrl", "ɵ_sanitizeHtml", "__sanitizeHtml", "ɵbypassSanitizationTrustHtml", "_bypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "_bypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "_bypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "_bypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "_bypassSanitizationTrustResourceUrl", "ɵwithI18nSupport", "_withI18nSupport", "ɵwithEventReplay", "_withEventReplay", "ɵwithIncrementalHydration", "_withIncrementalHydration", "makeEnvironmentProviders", "ɵwithDomHydration", "_withDomHydration", "ENVIRONMENT_INITIALIZER", "inject", "NgZone", "ɵZONELESS_ENABLED", "_ZONELESS_ENABLED", "ɵformatRuntimeError", "_formatRuntimeError", "Version", "EventManagerPlugin", "EVENT_MANAGER_PLUGINS", "EventManager", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "DomRendererFactory2", "ɵDomRendererFactory2", "SharedStylesHost", "ɵSharedStylesHost", "ɵwithHttpTransferCache", "_withHttpTransferCache", "Meta", "_doc", "_dom", "constructor", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "result", "push", "getTag", "attrSelector", "querySelector", "getTags", "list", "querySelectorAll", "slice", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "remove", "elem", "filter", "_containsAttributes", "undefined", "element", "createElement", "head", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "el", "Object", "keys", "for<PERSON>ach", "prop", "setAttribute", "_getMetaKeyMap", "attr", "name", "every", "key", "getAttribute", "META_KEYS_MAP", "ɵfac", "Meta_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "decorators", "httpEquiv", "Title", "getTitle", "title", "setTitle", "newTitle", "Title_Factory", "exportNgVar", "value", "COMPILED", "ng", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "appRef", "ref", "injector", "get", "timeChangeDetection", "config", "record", "profileName", "console", "profile", "start", "performance", "now", "tick", "end", "profileEnd", "log", "toFixed", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "indexOf", "n", "isElementNode", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "options", "buildHammer", "mc", "Hammer", "set", "enable", "eventName", "HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "_injector", "loader", "_loaderPromise", "doc", "supports", "hasOwnProperty", "toLowerCase", "isCustomEvent", "window", "_console", "warn", "addEventListener", "handler", "zone", "manager", "getZone", "runOutsideAngular", "cancelRegistration", "deregister", "then", "catch", "callback", "eventObj", "runGuarded", "on", "off", "destroy", "HammerGesturesPlugin_Factory", "HammerModule", "HammerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useClass", "multi", "deps", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizer_Factory", "__ngConditionalFactory__", "DomSanitizerImpl", "useExisting", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "DomSanitizerImpl_Factory", "HydrationFeatureKind", "hydrationFeature", "ɵkind", "ɵproviders", "ɵoptions", "withNoHttpTransferCache", "NoHttpTransferCache", "withHttpTransferCacheOptions", "HttpTransferCacheOptions", "withI18nSupport", "I18nSupport", "withEventReplay", "EventReplay", "withIncrementalHydration", "IncrementalHydration", "provideZoneJsCompatibilityDetector", "useValue", "ngZone", "isZoneless", "message", "provideClientHydration", "features", "featuresKind", "Set", "add", "length", "hasHttpTransferCacheOptions", "has", "VERSION", "ɵDomSanitizerImpl", "ɵHammerGesturesPlugin"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { BrowserModule, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, KeyEventsPlugin as ɵKeyEventsPlugin } from './browser.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, ɵglobal as _global, ApplicationRef, InjectionToken, ɵConsole as _Console, Optional, Injector, NgModule, forwardRef, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, ɵwithI18nSupport as _withI18nSupport, ɵwithEventReplay as _withEventReplay, ɵwithIncrementalHydration as _withIncrementalHydration, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, Version } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS } from './dom_renderer.mjs';\nexport { EventManager, REMOVE_STYLES_ON_COMPONENT_DESTROY, DomRendererFactory2 as ɵDomRendererFactory2, SharedStylesHost as ɵSharedStylesHost } from './dom_renderer.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    _doc;\n    _dom;\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = _getDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter((elem) => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Meta, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv',\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    _doc;\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Title, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/// <reference path=\"../../../goog.d.ts\" />\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = (_global['ng'] = _global['ng'] || {});\n        ng[name] = value;\n    }\n}\n\nclass ChangeDetectionPerfRecord {\n    msPerTick;\n    numTicks;\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    appRef;\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```ts\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        if (record && 'profile' in console && typeof console.profile === 'function') {\n            console.profile(profileName);\n        }\n        const start = performance.now();\n        let numTicks = 0;\n        while (numTicks < 5 || performance.now() - start < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performance.now();\n        if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n            console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        console.log(`ran ${numTicks} change detection cycles`);\n        console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null\n                ? elementMatches(debugElement.nativeElement, selector)\n                : false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (_getDOM().isElementNode(n)) {\n        return ((n.matches && n.matches(selector)) ||\n            (n.msMatchesSelector && n.msMatchesSelector(selector)) ||\n            (n.webkitMatchesSelector && n.webkitMatchesSelector(selector)));\n    }\n    return false;\n}\n\n/// <reference types=\"hammerjs\" />\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true,\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nclass HammerGestureConfig {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    overrides = {};\n    /**\n     * Properties whose default values can be overridden for a given event.\n     * Different sets of properties apply to different events.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    options;\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGestureConfig });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    _config;\n    _injector;\n    loader;\n    _loaderPromise = null;\n    constructor(doc, _config, _injector, loader) {\n        super(doc);\n        this._config = _config;\n        this._injector = _injector;\n        this.loader = loader;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // Get a `Console` through an injector to tree-shake the\n                // class when it is unused in production.\n                const _console = this._injector.get(_Console);\n                _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        const _console = this._injector.get(_Console);\n                        _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            }).catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    const _console = this._injector.get(_Console);\n                    _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.Injector }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGesturesPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }] });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n *\n * @deprecated The hammer integration is deprecated. Replace it by your own implementation.\n */\nclass HammerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerModule, providers: [\n            {\n                provide: EVENT_MANAGER_PLUGINS,\n                useClass: HammerGesturesPlugin,\n                multi: true,\n                deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]],\n            },\n            { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig },\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]],\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig },\n                    ],\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(() => DomSanitizerImpl) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nclass DomSanitizerImpl extends DomSanitizer {\n    _doc;\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return _unwrapSafeValue(value);\n                }\n                return __sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return _unwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return _unwrapSafeValue(value);\n                }\n                throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return _unwrapSafeValue(value);\n                }\n                return __sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return _unwrapSafeValue(value);\n                }\n                throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n            default:\n                throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return _bypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return _bypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return _bypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return _bypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return _bypassSanitizationTrustResourceUrl(value);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.1.2\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n    HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n    HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n    HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n    HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n    HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n    return { ɵkind, ɵproviders };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n    // This feature has no providers and acts as a flag that turns off\n    // HTTP transfer cache (which otherwise is turned on by default).\n    return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n    // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n    return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @publicApi 20.0\n */\nfunction withI18nSupport() {\n    return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n    return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @publicApi 20.0\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n    return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n    return [\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            useValue: () => {\n                const ngZone = inject(NgZone);\n                const isZoneless = inject(_ZONELESS_ENABLED);\n                // Checking `ngZone instanceof NgZone` would be insufficient here,\n                // because custom implementations might use NgZone as a base class.\n                if (!isZoneless && ngZone.constructor !== NgZone) {\n                    const console = inject(_Console);\n                    const message = _formatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' +\n                        'that uses a custom or a noop Zone.js implementation. ' +\n                        'This is not yet a fully supported configuration.');\n                    console.warn(message);\n                }\n            },\n            multi: true,\n        },\n    ];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi 17.0\n */\nfunction provideClientHydration(...features) {\n    const providers = [];\n    const featuresKind = new Set();\n    for (const { ɵproviders, ɵkind } of features) {\n        featuresKind.add(ɵkind);\n        if (ɵproviders.length) {\n            providers.push(ɵproviders);\n        }\n    }\n    const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n    if (typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) &&\n        hasHttpTransferCacheOptions) {\n        throw new _RuntimeError(5001 /* RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES */, 'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n    }\n    return makeEnvironmentProviders([\n        typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [],\n        _withDomHydration(),\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions\n            ? []\n            : _withHttpTransferCache({}),\n        providers,\n    ]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.1.2');\n\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,aAAa,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,+BAA+B,EAAEC,iBAAiB,IAAIC,kBAAkB,EAAEC,qBAAqB,IAAIC,sBAAsB,EAAEC,eAAe,IAAIC,gBAAgB,EAAEC,eAAe,IAAIC,gBAAgB,QAAQ,eAAe;AAC5S,SAASC,OAAO,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,iBAAiB;AAC9D,SAASF,OAAO,QAAQ,iBAAiB;AACzC,OAAO,KAAKG,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,IAAIC,OAAO,EAAEC,cAAc,EAAEC,cAAc,EAAEC,QAAQ,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,IAAIC,aAAa,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,IAAIC,gCAAgC,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,aAAa,IAAIC,aAAa,EAAEC,cAAc,IAAIC,cAAc,EAAEC,4BAA4B,IAAIC,4BAA4B,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,8BAA8B,IAAIC,8BAA8B,EAAEC,2BAA2B,IAAIC,2BAA2B,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,wBAAwB,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,OAAO,QAAQ,eAAe;AACjkC,SAASC,kBAAkB,EAAEC,qBAAqB,QAAQ,oBAAoB;AAC9E,SAASC,YAAY,EAAEC,kCAAkC,EAAEC,mBAAmB,IAAIC,oBAAoB,EAAEC,gBAAgB,IAAIC,iBAAiB,QAAQ,oBAAoB;AACzK,SAASC,sBAAsB,IAAIC,sBAAsB,QAAQ,sBAAsB;;AAEvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,CAAC;EACPC,IAAI;EACJC,IAAI;EACJC,WAAWA,CAACF,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGnE,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqE,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEN,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACLM,MAAM,CAACC,IAAI,CAAC,IAAI,CAACL,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOK,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACb,IAAI,CAACc,aAAa,CAAC,QAAQD,YAAY,GAAG,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACF,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAMG,IAAI,CAAC,eAAe,IAAI,CAAChB,IAAI,CAACiB,gBAAgB,CAAC,QAAQJ,YAAY,GAAG,CAAC;IAC7E,OAAOG,IAAI,GAAG,EAAE,CAACE,KAAK,CAACC,IAAI,CAACH,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,SAASA,CAAChB,GAAG,EAAEiB,QAAQ,EAAE;IACrB,IAAI,CAACjB,GAAG,EACJ,OAAO,IAAI;IACfiB,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAAClB,GAAG,CAAC;IAC/C,MAAMmB,IAAI,GAAG,IAAI,CAACX,MAAM,CAACS,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACpB,GAAG,EAAEmB,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACjB,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIqB,SAASA,CAACZ,YAAY,EAAE;IACpB,IAAI,CAACa,gBAAgB,CAAC,IAAI,CAACd,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIa,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAACtB,IAAI,CAAC0B,MAAM,CAACJ,IAAI,CAAC;IAC1B;EACJ;EACAjB,mBAAmBA,CAACiB,IAAI,EAAElB,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMgB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAMK,IAAI,GAAG,IAAI,CAACb,OAAO,CAACM,QAAQ,CAAC,CAACQ,MAAM,CAAED,IAAI,IAAK,IAAI,CAACE,mBAAmB,CAACP,IAAI,EAAEK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7F,IAAIA,IAAI,KAAKG,SAAS,EAClB,OAAOH,IAAI;IACnB;IACA,MAAMI,OAAO,GAAG,IAAI,CAAC/B,IAAI,CAACgC,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACT,yBAAyB,CAACD,IAAI,EAAES,OAAO,CAAC;IAC7C,MAAME,IAAI,GAAG,IAAI,CAAClC,IAAI,CAACmC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDD,IAAI,CAACE,WAAW,CAACJ,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACAR,yBAAyBA,CAACpB,GAAG,EAAEiC,EAAE,EAAE;IAC/BC,MAAM,CAACC,IAAI,CAACnC,GAAG,CAAC,CAACoC,OAAO,CAAEC,IAAI,IAAKJ,EAAE,CAACK,YAAY,CAAC,IAAI,CAACC,cAAc,CAACF,IAAI,CAAC,EAAErC,GAAG,CAACqC,IAAI,CAAC,CAAC,CAAC;IACzF,OAAOJ,EAAE;EACb;EACAf,cAAcA,CAAClB,GAAG,EAAE;IAChB,MAAMwC,IAAI,GAAGxC,GAAG,CAACyC,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAO,GAAGD,IAAI,KAAKxC,GAAG,CAACwC,IAAI,CAAC,GAAG;EACnC;EACAd,mBAAmBA,CAAC1B,GAAG,EAAEwB,IAAI,EAAE;IAC3B,OAAOU,MAAM,CAACC,IAAI,CAACnC,GAAG,CAAC,CAAC0C,KAAK,CAAEC,GAAG,IAAKnB,IAAI,CAACoB,YAAY,CAAC,IAAI,CAACL,cAAc,CAACI,GAAG,CAAC,CAAC,KAAK3C,GAAG,CAAC2C,GAAG,CAAC,CAAC;EACpG;EACAJ,cAAcA,CAACF,IAAI,EAAE;IACjB,OAAOQ,aAAa,CAACR,IAAI,CAAC,IAAIA,IAAI;EACtC;EACA,OAAOS,IAAI,YAAAC,aAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFrD,IAAI,EAAd/D,EAAE,CAAAqH,QAAA,CAA8BtH,QAAQ;EAAA;EACjI,OAAOuH,KAAK,kBAD6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EACYzD,IAAI;IAAA0D,OAAA,EAAJ1D,IAAI,CAAAmD,IAAA;IAAAQ,UAAA,EAAc;EAAM;AACnI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F3H,EAAE,CAAA4H,iBAAA,CAGJ7D,IAAI,EAAc,CAAC;IAClG8D,IAAI,EAAE5H,UAAU;IAChB6H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE9B,SAAS;IAAEgC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAAC/H,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAMkH,aAAa,GAAG;EAClBe,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,CAAC;EACRjE,IAAI;EACJE,WAAWA,CAACF,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACIkE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClE,IAAI,CAACmE,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAACrE,IAAI,CAACmE,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;EACA,OAAOnB,IAAI,YAAAoB,cAAAlB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFa,KAAK,EA7CfjI,EAAE,CAAAqH,QAAA,CA6C+BtH,QAAQ;EAAA;EAClI,OAAOuH,KAAK,kBA9C6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EA8CYS,KAAK;IAAAR,OAAA,EAALQ,KAAK,CAAAf,IAAA;IAAAQ,UAAA,EAAc;EAAM;AACpI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhD6F3H,EAAE,CAAA4H,iBAAA,CAgDJK,KAAK,EAAc,CAAC;IACnGJ,IAAI,EAAE5H,UAAU;IAChB6H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE9B,SAAS;IAAEgC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAAC/H,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwI,WAAWA,CAAC1B,IAAI,EAAE2B,KAAK,EAAE;EAC9B,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAItI,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;IAChDsI,EAAE,CAAC7B,IAAI,CAAC,GAAG2B,KAAK;EACpB;AACJ;AAEA,MAAMG,yBAAyB,CAAC;EAC5BC,SAAS;EACTC,QAAQ;EACR3E,WAAWA,CAAC0E,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,MAAM;EACN7E,WAAWA,CAAC8E,GAAG,EAAE;IACb,IAAI,CAACD,MAAM,GAAGC,GAAG,CAACC,QAAQ,CAACC,GAAG,CAAC7I,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8I,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,IAAID,MAAM,IAAI,SAAS,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,EAAE;MACzED,OAAO,CAACC,OAAO,CAACF,WAAW,CAAC;IAChC;IACA,MAAMG,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAId,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAIa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,GAAG,GAAG,EAAE;MACpD,IAAI,CAACV,MAAM,CAACa,IAAI,CAAC,CAAC;MAClBf,QAAQ,EAAE;IACd;IACA,MAAMgB,GAAG,GAAGH,WAAW,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAIN,MAAM,IAAI,YAAY,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACO,UAAU,KAAK,UAAU,EAAE;MAC/EP,OAAO,CAACO,UAAU,CAACR,WAAW,CAAC;IACnC;IACA,MAAMV,SAAS,GAAG,CAACiB,GAAG,GAAGJ,KAAK,IAAIZ,QAAQ;IAC1CU,OAAO,CAACQ,GAAG,CAAC,OAAOlB,QAAQ,0BAA0B,CAAC;IACtDU,OAAO,CAACQ,GAAG,CAAC,GAAGnB,SAAS,CAACoB,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IACnD,OAAO,IAAIrB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AAEA,MAAMoB,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAClB,GAAG,EAAE;EAC3BT,WAAW,CAAC0B,oBAAoB,EAAE,IAAInB,eAAe,CAACE,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,iBAAiBA,CAAA,EAAG;EACzB5B,WAAW,CAAC0B,oBAAoB,EAAE,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAACjF,QAAQ,EAAE;IACjB,OAAQkF,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACnCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAEnF,QAAQ,CAAC,GACpD,KAAK;IACf,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOqF,SAASA,CAAC7C,IAAI,EAAE;IACnB,OAAQ8C,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACC,OAAO,CAAChD,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS4C,cAAcA,CAACK,CAAC,EAAEzF,QAAQ,EAAE;EACjC,IAAIvF,OAAO,CAAC,CAAC,CAACiL,aAAa,CAACD,CAAC,CAAC,EAAE;IAC5B,OAASA,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,CAAC3F,QAAQ,CAAC,IACpCyF,CAAC,CAACG,iBAAiB,IAAIH,CAAC,CAACG,iBAAiB,CAAC5F,QAAQ,CAAE,IACrDyF,CAAC,CAACI,qBAAqB,IAAIJ,CAAC,CAACI,qBAAqB,CAAC7F,QAAQ,CAAE;EACtE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA,MAAM8F,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI9K,cAAc,CAAC,OAAOqH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0D,aAAa,GAAG,IAAI/K,cAAc,CAAC,OAAOqH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2D,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;AACA;EACIC,MAAM,GAAG,EAAE;EACX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAAS,GAAG,CAAC,CAAC;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC1F,OAAO,EAAE;IACjB,MAAM2F,EAAE,GAAG,IAAIC,MAAM,CAAC5F,OAAO,EAAE,IAAI,CAACyF,OAAO,CAAC;IAC5CE,EAAE,CAACzC,GAAG,CAAC,OAAO,CAAC,CAAC2C,GAAG,CAAC;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCH,EAAE,CAACzC,GAAG,CAAC,QAAQ,CAAC,CAAC2C,GAAG,CAAC;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACP,SAAS,EAAE;MACpCG,EAAE,CAACzC,GAAG,CAAC6C,SAAS,CAAC,CAACF,GAAG,CAAC,IAAI,CAACL,SAAS,CAACO,SAAS,CAAC,CAAC;IACpD;IACA,OAAOJ,EAAE;EACb;EACA,OAAOzE,IAAI,YAAA8E,4BAAA5E,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkE,mBAAmB;EAAA;EACtH,OAAOhE,KAAK,kBA1U6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EA0UY8D,mBAAmB;IAAA7D,OAAA,EAAnB6D,mBAAmB,CAAApE;EAAA;AAC9H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA5U6F3H,EAAE,CAAA4H,iBAAA,CA4UJ0D,mBAAmB,EAAc,CAAC;IACjHzD,IAAI,EAAE5H;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMgM,oBAAoB,SAAS5I,kBAAkB,CAAC;EAClD6I,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,cAAc,GAAG,IAAI;EACrBnI,WAAWA,CAACoI,GAAG,EAAEJ,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACzC,KAAK,CAACE,GAAG,CAAC;IACV,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAG,QAAQA,CAACR,SAAS,EAAE;IAChB,IAAI,CAACZ,WAAW,CAACqB,cAAc,CAACT,SAAS,CAACU,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACC,aAAa,CAACX,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAACY,MAAM,CAACf,MAAM,IAAI,CAAC,IAAI,CAACQ,MAAM,EAAE;MAChC,IAAI,OAAOzE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA;QACA,MAAMiF,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACjD,GAAG,CAAC1I,QAAQ,CAAC;QAC7CoM,QAAQ,CAACC,IAAI,CAAC,QAAQd,SAAS,mDAAmD,GAC9E,iDAAiD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAe,gBAAgBA,CAAC9G,OAAO,EAAE+F,SAAS,EAAEgB,OAAO,EAAE;IAC1C,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC;IACnCnB,SAAS,GAAGA,SAAS,CAACU,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACE,MAAM,CAACf,MAAM,IAAI,IAAI,CAACQ,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIW,IAAI,CAACG,iBAAiB,CAAC,MAAM,IAAI,CAACf,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIgB,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDJ,IAAI,CAACG,iBAAiB,CAAC,MAAM,IAAI,CAACd,cAAc,CAACiB,IAAI,CAAC,MAAM;QACxD;QACA,IAAI,CAACX,MAAM,CAACf,MAAM,EAAE;UAChB,IAAI,OAAOjE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,MAAMiF,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACjD,GAAG,CAAC1I,QAAQ,CAAC;YAC7CoM,QAAQ,CAACC,IAAI,CAAC,mEAAmE,CAAC;UACtF;UACAQ,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAACP,gBAAgB,CAAC9G,OAAO,EAAE+F,SAAS,EAAEgB,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;QACX,IAAI,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,MAAMiF,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACjD,GAAG,CAAC1I,QAAQ,CAAC;UAC7CoM,QAAQ,CAACC,IAAI,CAAC,QAAQd,SAAS,6CAA6C,GACxE,0BAA0B,CAAC;QACnC;QACAsB,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAOL,IAAI,CAACG,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAMxB,EAAE,GAAG,IAAI,CAACO,OAAO,CAACR,WAAW,CAAC1F,OAAO,CAAC;MAC5C,MAAMwH,QAAQ,GAAG,SAAAA,CAAUC,QAAQ,EAAE;QACjCT,IAAI,CAACU,UAAU,CAAC,YAAY;UACxBX,OAAO,CAACU,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACD9B,EAAE,CAACgC,EAAE,CAAC5B,SAAS,EAAEyB,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACT7B,EAAE,CAACiC,GAAG,CAAC7B,SAAS,EAAEyB,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAO7B,EAAE,CAACkC,OAAO,KAAK,UAAU,EAAE;UAClClC,EAAE,CAACkC,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAnB,aAAaA,CAACX,SAAS,EAAE;IACrB,OAAO,IAAI,CAACG,OAAO,CAACX,MAAM,CAACV,OAAO,CAACkB,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;EACA,OAAO7E,IAAI,YAAA4G,6BAAA1G,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6E,oBAAoB,EAjb9BjM,EAAE,CAAAqH,QAAA,CAib8CtH,QAAQ,GAjbxDC,EAAE,CAAAqH,QAAA,CAibmE+D,qBAAqB,GAjb1FpL,EAAE,CAAAqH,QAAA,CAibqGrH,EAAE,CAACU,QAAQ,GAjblHV,EAAE,CAAAqH,QAAA,CAib6HgE,aAAa;EAAA;EACrO,OAAO/D,KAAK,kBAlb6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EAkbYyE,oBAAoB;IAAAxE,OAAA,EAApBwE,oBAAoB,CAAA/E;EAAA;AAC/H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KApb6F3H,EAAE,CAAA4H,iBAAA,CAobJqE,oBAAoB,EAAc,CAAC;IAClHpE,IAAI,EAAE5H;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE4H,IAAI,EAAE9B,SAAS;IAAEgC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAAC/H,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8H,IAAI,EAAEyD,mBAAmB;IAAEvD,UAAU,EAAE,CAAC;MAC5CF,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAACsD,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEvD,IAAI,EAAE7H,EAAE,CAACU;EAAS,CAAC,EAAE;IAAEmH,IAAI,EAAE9B,SAAS;IAAEgC,UAAU,EAAE,CAAC;MACzDF,IAAI,EAAEpH;IACV,CAAC,EAAE;MACCoH,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAACuD,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0C,YAAY,CAAC;EACf,OAAO7G,IAAI,YAAA8G,qBAAA5G,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2G,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBAjd8EjO,EAAE,CAAAkO,gBAAA;IAAArG,IAAA,EAidSkG;EAAY;EAChH,OAAOI,IAAI,kBAld8EnO,EAAE,CAAAoO,gBAAA;IAAAC,SAAA,EAkdkC,CACrH;MACIC,OAAO,EAAEhL,qBAAqB;MAC9BiL,QAAQ,EAAEtC,oBAAoB;MAC9BuC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,CAAC1O,QAAQ,EAAEqL,qBAAqB,EAAE1K,QAAQ,EAAE,CAAC,IAAID,QAAQ,CAAC,CAAC,EAAE4K,aAAa,CAAC;IACrF,CAAC,EACD;MAAEiD,OAAO,EAAElD,qBAAqB;MAAEmD,QAAQ,EAAEjD;IAAoB,CAAC;EACpE;AACT;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KA5d6F3H,EAAE,CAAA4H,iBAAA,CA4dJmG,YAAY,EAAc,CAAC;IAC1GlG,IAAI,EAAElH,QAAQ;IACdmH,IAAI,EAAE,CAAC;MACCuG,SAAS,EAAE,CACP;QACIC,OAAO,EAAEhL,qBAAqB;QAC9BiL,QAAQ,EAAEtC,oBAAoB;QAC9BuC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,CAAC1O,QAAQ,EAAEqL,qBAAqB,EAAE1K,QAAQ,EAAE,CAAC,IAAID,QAAQ,CAAC,CAAC,EAAE4K,aAAa,CAAC;MACrF,CAAC,EACD;QAAEiD,OAAO,EAAElD,qBAAqB;QAAEmD,QAAQ,EAAEjD;MAAoB,CAAC;IAEzE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,YAAY,CAAC;EACf,OAAOxH,IAAI,YAAAyH,qBAAAvH,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsH,YAAY;EAAA;EAC/G,OAAOpH,KAAK,kBA5gB6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EA4gBYkH,YAAY;IAAAjH,OAAA,WAAAkH,qBAAAvH,iBAAA;MAAA,IAAAwH,wBAAA;MAAA,IAAAxH,iBAAA;QAAAwH,wBAAA,QAAAxH,iBAAA,IAAZsH,YAAY;MAAA;QAAAE,wBAAA,GA5gB1B5O,EAAE,CAAAqH,QAAA,CA4gB+EwH,gBAAgB;MAAA;MAAA,OAAAD,wBAAA;IAAA;IAAAlH,UAAA,EAAzD;EAAM;AAC3I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9gB6F3H,EAAE,CAAA4H,iBAAA,CA8gBJ8G,YAAY,EAAc,CAAC;IAC1G7G,IAAI,EAAE5H,UAAU;IAChB6H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE,MAAM;MAAEoH,WAAW,EAAElO,UAAU,CAAC,MAAMiO,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,MAAMA,gBAAgB,SAASH,YAAY,CAAC;EACxC1K,IAAI;EACJE,WAAWA,CAACF,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA+K,QAAQA,CAACC,GAAG,EAAExG,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQwG,GAAG;MACP,KAAK/N,eAAe,CAACgO,IAAI;QACrB,OAAOzG,KAAK;MAChB,KAAKvH,eAAe,CAACiO,IAAI;QACrB,IAAI/N,gCAAgC,CAACqH,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAOnH,gBAAgB,CAACmH,KAAK,CAAC;QAClC;QACA,OAAO/G,cAAc,CAAC,IAAI,CAACuC,IAAI,EAAEmL,MAAM,CAAC3G,KAAK,CAAC,CAAC,CAAC4G,QAAQ,CAAC,CAAC;MAC9D,KAAKnO,eAAe,CAACoO,KAAK;QACtB,IAAIlO,gCAAgC,CAACqH,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAOnH,gBAAgB,CAACmH,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAKvH,eAAe,CAACqO,MAAM;QACvB,IAAInO,gCAAgC,CAACqH,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAOnH,gBAAgB,CAACmH,KAAK,CAAC;QAClC;QACA,MAAM,IAAI1H,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,uCAAuC,CAAC;MAChD,KAAK1G,eAAe,CAACsO,GAAG;QACpB,IAAIpO,gCAAgC,CAACqH,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAOnH,gBAAgB,CAACmH,KAAK,CAAC;QAClC;QACA,OAAOjH,aAAa,CAAC4N,MAAM,CAAC3G,KAAK,CAAC,CAAC;MACvC,KAAKvH,eAAe,CAACuO,YAAY;QAC7B,IAAIrO,gCAAgC,CAACqH,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAOnH,gBAAgB,CAACmH,KAAK,CAAC;QAClC;QACA,MAAM,IAAI1H,aAAa,CAAC,IAAI,CAAC,yDAAyD,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAChI,oDAAoD3G,iBAAiB,GAAG,CAAC;MACjF;QACI,MAAM,IAAIF,aAAa,CAAC,IAAI,CAAC,oDAAoD,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC3H,8BAA8BqH,GAAG,SAAShO,iBAAiB,GAAG,CAAC;IAC3E;EACJ;EACAyO,uBAAuBA,CAACjH,KAAK,EAAE;IAC3B,OAAO7G,4BAA4B,CAAC6G,KAAK,CAAC;EAC9C;EACAkH,wBAAwBA,CAAClH,KAAK,EAAE;IAC5B,OAAO3G,6BAA6B,CAAC2G,KAAK,CAAC;EAC/C;EACAmH,yBAAyBA,CAACnH,KAAK,EAAE;IAC7B,OAAOzG,8BAA8B,CAACyG,KAAK,CAAC;EAChD;EACAoH,sBAAsBA,CAACpH,KAAK,EAAE;IAC1B,OAAOvG,2BAA2B,CAACuG,KAAK,CAAC;EAC7C;EACAqH,8BAA8BA,CAACrH,KAAK,EAAE;IAClC,OAAOrG,mCAAmC,CAACqG,KAAK,CAAC;EACrD;EACA,OAAOtB,IAAI,YAAA4I,yBAAA1I,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyH,gBAAgB,EA7kB1B7O,EAAE,CAAAqH,QAAA,CA6kB0CtH,QAAQ;EAAA;EAC7I,OAAOuH,KAAK,kBA9kB6EtH,EAAE,CAAAuH,kBAAA;IAAAC,KAAA,EA8kBYqH,gBAAgB;IAAApH,OAAA,EAAhBoH,gBAAgB,CAAA3H,IAAA;IAAAQ,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhlB6F3H,EAAE,CAAA4H,iBAAA,CAglBJiH,gBAAgB,EAAc,CAAC;IAC9GhH,IAAI,EAAE5H,UAAU;IAChB6H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE9B,SAAS;IAAEgC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE3H,MAAM;MACZ4H,IAAI,EAAE,CAAC/H,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgQ,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EAC7FA,oBAAoB,CAACA,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B;EACvGA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7EA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7EA,oBAAoB,CAACA,oBAAoB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;AACnG,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,UAAU,GAAG,EAAE,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;EAC7D,OAAO;IAAEF,KAAK;IAAEC;EAAW,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAA,EAAG;EAC/B;EACA;EACA,OAAOJ,gBAAgB,CAACD,oBAAoB,CAACM,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAC7E,OAAO,EAAE;EAC3C;EACA,OAAOuE,gBAAgB,CAACD,oBAAoB,CAACQ,wBAAwB,EAAEzM,sBAAsB,CAAC2H,OAAO,CAAC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+E,eAAeA,CAAA,EAAG;EACvB,OAAOR,gBAAgB,CAACD,oBAAoB,CAACU,WAAW,EAAEpO,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqO,eAAeA,CAAA,EAAG;EACvB,OAAOV,gBAAgB,CAACD,oBAAoB,CAACY,WAAW,EAAEpO,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqO,wBAAwBA,CAAA,EAAG;EAChC,OAAOZ,gBAAgB,CAACD,oBAAoB,CAACc,oBAAoB,EAAEpO,yBAAyB,CAAC,CAAC,CAAC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA,SAASqO,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CACH;IACIxC,OAAO,EAAEzL,uBAAuB;IAChCkO,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMC,MAAM,GAAGlO,MAAM,CAACC,MAAM,CAAC;MAC7B,MAAMkO,UAAU,GAAGnO,MAAM,CAACG,iBAAiB,CAAC;MAC5C;MACA;MACA,IAAI,CAACgO,UAAU,IAAID,MAAM,CAAC9M,WAAW,KAAKnB,MAAM,EAAE;QAC9C,MAAMwG,OAAO,GAAGzG,MAAM,CAACtC,QAAQ,CAAC;QAChC,MAAM0Q,OAAO,GAAG/N,mBAAmB,CAAC,CAAC,IAAI,CAAC,oDAAoD,iEAAiE,GAC3J,uDAAuD,GACvD,kDAAkD,CAAC;QACvDoG,OAAO,CAACsD,IAAI,CAACqE,OAAO,CAAC;MACzB;IACJ,CAAC;IACD1C,KAAK,EAAE;EACX,CAAC,CACJ;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2C,sBAAsBA,CAAC,GAAGC,QAAQ,EAAE;EACzC,MAAM/C,SAAS,GAAG,EAAE;EACpB,MAAMgD,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,KAAK,MAAM;IAAEpB,UAAU;IAAED;EAAM,CAAC,IAAImB,QAAQ,EAAE;IAC1CC,YAAY,CAACE,GAAG,CAACtB,KAAK,CAAC;IACvB,IAAIC,UAAU,CAACsB,MAAM,EAAE;MACnBnD,SAAS,CAAC1J,IAAI,CAACuL,UAAU,CAAC;IAC9B;EACJ;EACA,MAAMuB,2BAA2B,GAAGJ,YAAY,CAACK,GAAG,CAAC3B,oBAAoB,CAACQ,wBAAwB,CAAC;EACnG,IAAI,OAAO5I,SAAS,KAAK,WAAW,IAChCA,SAAS,IACT0J,YAAY,CAACK,GAAG,CAAC3B,oBAAoB,CAACM,mBAAmB,CAAC,IAC1DoB,2BAA2B,EAAE;IAC7B,MAAM,IAAI3Q,aAAa,CAAC,IAAI,CAAC,uDAAuD,sKAAsK,CAAC;EAC/P;EACA,OAAO4B,wBAAwB,CAAC,CAC5B,OAAOiF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAGmJ,kCAAkC,CAAC,CAAC,GAAG,EAAE,EACzFlO,iBAAiB,CAAC,CAAC,EACnByO,YAAY,CAACK,GAAG,CAAC3B,oBAAoB,CAACM,mBAAmB,CAAC,IAAIoB,2BAA2B,GACnF,EAAE,GACF3N,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAChCuK,SAAS,CACZ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsD,OAAO,GAAG,IAAIvO,OAAO,CAAC,QAAQ,CAAC;AAErC,SAASgH,EAAE,EAAEsE,YAAY,EAAEpL,qBAAqB,EAAED,kBAAkB,EAAE+H,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEyC,YAAY,EAAEgC,oBAAoB,EAAEhM,IAAI,EAAEkE,KAAK,EAAE0J,OAAO,EAAExH,iBAAiB,EAAED,gBAAgB,EAAEiH,sBAAsB,EAAET,eAAe,EAAEJ,4BAA4B,EAAEE,eAAe,EAAEI,wBAAwB,EAAER,uBAAuB,EAAEvB,gBAAgB,IAAI+C,iBAAiB,EAAE3F,oBAAoB,IAAI4F,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}