{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * Current injector value used by `inject`.\n * - `undefined`: it is an error to call `inject`\n * - `null`: `inject` can be called but there is no injector (limp-mode).\n * - Injector instance: Use the injector for resolution.\n */\nlet _currentInjector = undefined;\nfunction getCurrentInjector() {\n  return _currentInjector;\n}\nfunction setCurrentInjector(injector) {\n  const former = _currentInjector;\n  _currentInjector = injector;\n  return former;\n}\nfunction inject(token, options) {\n  const currentInjector = getCurrentInjector();\n  if (!currentInjector) {\n    throw new Error('Current injector is not set.');\n  }\n  if (!token.ɵprov) {\n    throw new Error('Token is not an injectable');\n  }\n  return currentInjector.retrieve(token, options);\n}\n\n/**\n * Value returned if the key-value pair couldn't be found in the context\n * hierarchy.\n */\nconst NOT_FOUND = Symbol('NotFound');\n/**\n * Error thrown when the key-value pair couldn't be found in the context\n * hierarchy. Context can be attached below.\n */\nclass NotFoundError extends Error {\n  name = 'ɵNotFound';\n  constructor(message) {\n    super(message);\n  }\n}\n/**\n * Type guard for checking if an unknown value is a NotFound.\n */\nfunction isNotFound(e) {\n  return e === NOT_FOUND || e?.name === 'ɵNotFound';\n}\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, inject, isNotFound, setCurrentInjector };", "map": {"version": 3, "names": ["_currentInjector", "undefined", "getCurrentInjector", "setCurrentInjector", "injector", "former", "inject", "token", "options", "currentInjector", "Error", "ɵprov", "retrieve", "NOT_FOUND", "Symbol", "NotFoundError", "name", "constructor", "message", "isNotFound", "e"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/core/fesm2022/not_found.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * Current injector value used by `inject`.\n * - `undefined`: it is an error to call `inject`\n * - `null`: `inject` can be called but there is no injector (limp-mode).\n * - Injector instance: Use the injector for resolution.\n */\nlet _currentInjector = undefined;\nfunction getCurrentInjector() {\n    return _currentInjector;\n}\nfunction setCurrentInjector(injector) {\n    const former = _currentInjector;\n    _currentInjector = injector;\n    return former;\n}\nfunction inject(token, options) {\n    const currentInjector = getCurrentInjector();\n    if (!currentInjector) {\n        throw new Error('Current injector is not set.');\n    }\n    if (!token.ɵprov) {\n        throw new Error('Token is not an injectable');\n    }\n    return currentInjector.retrieve(token, options);\n}\n\n/**\n * Value returned if the key-value pair couldn't be found in the context\n * hierarchy.\n */\nconst NOT_FOUND = Symbol('NotFound');\n/**\n * Error thrown when the key-value pair couldn't be found in the context\n * hierarchy. Context can be attached below.\n */\nclass NotFoundError extends Error {\n    name = 'ɵNotFound';\n    constructor(message) {\n        super(message);\n    }\n}\n/**\n * Type guard for checking if an unknown value is a NotFound.\n */\nfunction isNotFound(e) {\n    return e === NOT_FOUND || e?.name === 'ɵNotFound';\n}\n\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, inject, isNotFound, setCurrentInjector };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,gBAAgB,GAAGC,SAAS;AAChC,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,OAAOF,gBAAgB;AAC3B;AACA,SAASG,kBAAkBA,CAACC,QAAQ,EAAE;EAClC,MAAMC,MAAM,GAAGL,gBAAgB;EAC/BA,gBAAgB,GAAGI,QAAQ;EAC3B,OAAOC,MAAM;AACjB;AACA,SAASC,MAAMA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5B,MAAMC,eAAe,GAAGP,kBAAkB,CAAC,CAAC;EAC5C,IAAI,CAACO,eAAe,EAAE;IAClB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;EACnD;EACA,IAAI,CAACH,KAAK,CAACI,KAAK,EAAE;IACd,MAAM,IAAID,KAAK,CAAC,4BAA4B,CAAC;EACjD;EACA,OAAOD,eAAe,CAACG,QAAQ,CAACL,KAAK,EAAEC,OAAO,CAAC;AACnD;;AAEA;AACA;AACA;AACA;AACA,MAAMK,SAAS,GAAGC,MAAM,CAAC,UAAU,CAAC;AACpC;AACA;AACA;AACA;AACA,MAAMC,aAAa,SAASL,KAAK,CAAC;EAC9BM,IAAI,GAAG,WAAW;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,KAAKP,SAAS,IAAIO,CAAC,EAAEJ,IAAI,KAAK,WAAW;AACrD;AAEA,SAASH,SAAS,EAAEE,aAAa,EAAEb,kBAAkB,EAAEI,MAAM,EAAEa,UAAU,EAAEhB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}