{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nvar KeyboardResize;\n(function (KeyboardResize) {\n  /**\n   * Only the `body` HTML element will be resized.\n   * Relative units are not affected, because the viewport does not change.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Body\"] = \"body\";\n  /**\n   * Only the `ion-app` HTML element will be resized.\n   * Use it only for Ionic Framework apps.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Ionic\"] = \"ionic\";\n  /**\n   * The whole native Web View will be resized when the keyboard shows/hides.\n   * This affects the `vh` relative unit.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Native\"] = \"native\";\n  /**\n   * Neither the app nor the Web View are resized.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n      return capacitor.Plugins.Keyboard;\n    }\n    return undefined;\n  },\n  getResizeMode() {\n    const engine = this.getEngine();\n    if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n      return Promise.resolve(undefined);\n    }\n    return engine.getResizeMode().catch(e => {\n      if (e.code === ExceptionCode.Unimplemented) {\n        // If the native implementation is not available\n        // we treat it the same as if the plugin is not available.\n        return undefined;\n      }\n      throw e;\n    });\n  }\n};\nexport { Keyboard as K, KeyboardResize as a };", "map": {"version": 3, "names": ["g", "getCapacitor", "ExceptionCode", "KeyboardResize", "Keyboard", "getEngine", "capacitor", "isPluginAvailable", "Plugins", "undefined", "getResizeMode", "engine", "Promise", "resolve", "catch", "e", "code", "Unimplemented", "K", "a"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/keyboard-CUw4ekVy.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\n\nvar ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\n\nvar KeyboardResize;\n(function (KeyboardResize) {\n    /**\n     * Only the `body` HTML element will be resized.\n     * Relative units are not affected, because the viewport does not change.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Body\"] = \"body\";\n    /**\n     * Only the `ion-app` HTML element will be resized.\n     * Use it only for Ionic Framework apps.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Ionic\"] = \"ionic\";\n    /**\n     * The whole native Web View will be resized when the keyboard shows/hides.\n     * This affects the `vh` relative unit.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Native\"] = \"native\";\n    /**\n     * Neither the app nor the Web View are resized.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n            return capacitor.Plugins.Keyboard;\n        }\n        return undefined;\n    },\n    getResizeMode() {\n        const engine = this.getEngine();\n        if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n            return Promise.resolve(undefined);\n        }\n        return engine.getResizeMode().catch((e) => {\n            if (e.code === ExceptionCode.Unimplemented) {\n                // If the native implementation is not available\n                // we treat it the same as if the plugin is not available.\n                return undefined;\n            }\n            throw e;\n        });\n    },\n};\n\nexport { Keyboard as K, KeyboardResize as a };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,YAAY,QAAQ,yBAAyB;AAE3D,IAAIC,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa;AAChD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AAEzC,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;EAC/B;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnC;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;AACnC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAMC,QAAQ,GAAG;EACbC,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAGL,YAAY,CAAC,CAAC;IAChC,IAAIK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,UAAU,CAAC,EAAE;MAC/F,OAAOD,SAAS,CAACE,OAAO,CAACJ,QAAQ;IACrC;IACA,OAAOK,SAAS;EACpB,CAAC;EACDC,aAAaA,CAAA,EAAG;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,EAAEM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,aAAa,CAAC,EAAE;MACzE,OAAOE,OAAO,CAACC,OAAO,CAACJ,SAAS,CAAC;IACrC;IACA,OAAOE,MAAM,CAACD,aAAa,CAAC,CAAC,CAACI,KAAK,CAAEC,CAAC,IAAK;MACvC,IAAIA,CAAC,CAACC,IAAI,KAAKd,aAAa,CAACe,aAAa,EAAE;QACxC;QACA;QACA,OAAOR,SAAS;MACpB;MACA,MAAMM,CAAC;IACX,CAAC,CAAC;EACN;AACJ,CAAC;AAED,SAASX,QAAQ,IAAIc,CAAC,EAAEf,cAAc,IAAIgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}