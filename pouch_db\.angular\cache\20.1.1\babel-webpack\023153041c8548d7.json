{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, inject, isNotFound, setCurrentInjector } from '../not_found.mjs';\nfunction defineInjectable(opts) {\n  return {\n    token: opts.token,\n    providedIn: opts.providedIn || null,\n    factory: opts.factory,\n    value: undefined\n  };\n}\nfunction registerInjectable(ctor, declaration) {\n  ctor.ɵprov = declaration;\n  return ctor;\n}\nexport { defineInjectable, registerInjectable };", "map": {"version": 3, "names": ["NOT_FOUND", "NotFoundError", "getCurrentInjector", "inject", "isNotFound", "setCurrentInjector", "defineInjectable", "opts", "token", "providedIn", "factory", "value", "undefined", "registerInjectable", "ctor", "declaration", "ɵprov"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/core/fesm2022/primitives/di.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, inject, isNotFound, setCurrentInjector } from '../not_found.mjs';\n\nfunction defineInjectable(opts) {\n    return {\n        token: opts.token,\n        providedIn: opts.providedIn || null,\n        factory: opts.factory,\n        value: undefined,\n    };\n}\nfunction registerInjectable(ctor, declaration) {\n    ctor.ɵprov = declaration;\n    return ctor;\n}\n\nexport { defineInjectable, registerInjectable };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,kBAAkB;AAEvH,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,OAAO;IACHC,KAAK,EAAED,IAAI,CAACC,KAAK;IACjBC,UAAU,EAAEF,IAAI,CAACE,UAAU,IAAI,IAAI;IACnCC,OAAO,EAAEH,IAAI,CAACG,OAAO;IACrBC,KAAK,EAAEC;EACX,CAAC;AACL;AACA,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,WAAW,EAAE;EAC3CD,IAAI,CAACE,KAAK,GAAGD,WAAW;EACxB,OAAOD,IAAI;AACf;AAEA,SAASR,gBAAgB,EAAEO,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}