{"ast": null, "code": "// src/app-data/index.ts\nvar BUILD = {\n  allRenderFn: false,\n  element: true,\n  event: true,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTargetWindow: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetParent: false,\n  hostListenerTarget: true,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  prop: true,\n  propMutable: true,\n  reflect: true,\n  scoped: true,\n  shadowDom: true,\n  slot: true,\n  cssAnnotations: true,\n  state: true,\n  style: true,\n  formAssociated: false,\n  svg: true,\n  updatable: true,\n  vdomAttribute: true,\n  vdomXlink: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomRef: true,\n  vdomPropOrAttr: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  watchCallback: true,\n  taskQueue: true,\n  hotModuleReplacement: false,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  hydrateServerSide: false,\n  hydrateClientSide: false,\n  lifecycleDOMEvents: false,\n  lazyLoad: false,\n  profile: false,\n  slotRelocation: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  appendChildSlotFix: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  cloneNodeFix: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  // TODO(STENCIL-1305): remove this option\n  scriptDataOpts: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  scopedSlotTextContentFix: false,\n  // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n  shadowDomShim: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  slotChildNodesFix: false,\n  invisiblePrehydration: true,\n  propBoolean: true,\n  propNumber: true,\n  propString: true,\n  constructableCSS: true,\n  devTools: false,\n  shadowDelegatesFocus: true,\n  initializeNextTick: false,\n  asyncLoading: true,\n  asyncQueue: false,\n  transformTagName: false,\n  attachStyles: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  experimentalSlotFixes: false\n};\nvar Env = {};\nvar NAMESPACE = /* default */\n\"app\";\nexport { BUILD, Env, NAMESPACE };", "map": {"version": 3, "names": ["BUILD", "allRenderFn", "element", "event", "hasRenderFn", "hostListener", "hostListenerTargetWindow", "hostListenerTargetDocument", "hostListenerTargetBody", "hostListenerTargetParent", "hostListenerTarget", "member", "method", "mode", "observeAttribute", "prop", "propMutable", "reflect", "scoped", "shadowDom", "slot", "cssAnnotations", "state", "style", "formAssociated", "svg", "updatable", "vdomAttribute", "vdomXlink", "vdomClass", "vdomFunctional", "vdomKey", "vdomListener", "vdomRef", "vdomPropOrAttr", "v<PERSON><PERSON><PERSON>", "vdomStyle", "vdomText", "watchCallback", "taskQueue", "hotModuleReplacement", "isDebug", "isDev", "isTesting", "hydrateServerSide", "hydrateClientSide", "lifecycleDOMEvents", "lazyLoad", "profile", "slotRelocation", "appendChildSlotFix", "cloneNodeFix", "hydratedAttribute", "hydratedClass", "scriptDataOpts", "scopedSlotTextContentFix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slotChildNodesFix", "invisiblePrehydration", "propBoolean", "propNumber", "propString", "constructableCSS", "devTools", "shadowDelegatesFocus", "initializeNextTick", "asyncLoading", "asyncQueue", "transformTagName", "attachStyles", "experimentalSlotFixes", "Env", "NAMESPACE"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@stencil/core/internal/app-data/index.js"], "sourcesContent": ["// src/app-data/index.ts\nvar BUILD = {\n  allRenderFn: false,\n  element: true,\n  event: true,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTargetWindow: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetParent: false,\n  hostListenerTarget: true,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  prop: true,\n  propMutable: true,\n  reflect: true,\n  scoped: true,\n  shadowDom: true,\n  slot: true,\n  cssAnnotations: true,\n  state: true,\n  style: true,\n  formAssociated: false,\n  svg: true,\n  updatable: true,\n  vdomAttribute: true,\n  vdomXlink: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomRef: true,\n  vdomPropOrAttr: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  watchCallback: true,\n  taskQueue: true,\n  hotModuleReplacement: false,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  hydrateServerSide: false,\n  hydrateClientSide: false,\n  lifecycleDOMEvents: false,\n  lazyLoad: false,\n  profile: false,\n  slotRelocation: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  appendChildSlotFix: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  cloneNodeFix: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  // TODO(STENCIL-1305): remove this option\n  scriptDataOpts: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  scopedSlotTextContentFix: false,\n  // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n  shadowDomShim: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  slotChildNodesFix: false,\n  invisiblePrehydration: true,\n  propBoolean: true,\n  propNumber: true,\n  propString: true,\n  constructableCSS: true,\n  devTools: false,\n  shadowDelegatesFocus: true,\n  initializeNextTick: false,\n  asyncLoading: true,\n  asyncQueue: false,\n  transformTagName: false,\n  attachStyles: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  experimentalSlotFixes: false\n};\nvar Env = {};\nvar NAMESPACE = (\n  /* default */\n  \"app\"\n);\nexport {\n  BUILD,\n  Env,\n  NAMESPACE\n};\n"], "mappings": "AAAA;AACA,IAAIA,KAAK,GAAG;EACVC,WAAW,EAAE,KAAK;EAClBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,wBAAwB,EAAE,IAAI;EAC9BC,0BAA0B,EAAE,IAAI;EAChCC,sBAAsB,EAAE,IAAI;EAC5BC,wBAAwB,EAAE,KAAK;EAC/BC,kBAAkB,EAAE,IAAI;EACxBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EACVC,gBAAgB,EAAE,IAAI;EACtBC,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE,IAAI;EACjBC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,IAAI;EACVC,cAAc,EAAE,IAAI;EACpBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,IAAI;EACXC,cAAc,EAAE,KAAK;EACrBC,GAAG,EAAE,IAAI;EACTC,SAAS,EAAE,IAAI;EACfC,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,IAAI;EACpBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,IAAI;EACfC,oBAAoB,EAAE,KAAK;EAC3BC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,KAAK;EACZC,SAAS,EAAE,KAAK;EAChBC,iBAAiB,EAAE,KAAK;EACxBC,iBAAiB,EAAE,KAAK;EACxBC,kBAAkB,EAAE,KAAK;EACzBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,KAAK;EACdC,cAAc,EAAE,IAAI;EACpB;EACAC,kBAAkB,EAAE,KAAK;EACzB;EACAC,YAAY,EAAE,KAAK;EACnBC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,IAAI;EACnB;EACAC,cAAc,EAAE,KAAK;EACrB;EACAC,wBAAwB,EAAE,KAAK;EAC/B;EACAC,aAAa,EAAE,KAAK;EACpB;EACAC,iBAAiB,EAAE,KAAK;EACxBC,qBAAqB,EAAE,IAAI;EAC3BC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,gBAAgB,EAAE,IAAI;EACtBC,QAAQ,EAAE,KAAK;EACfC,oBAAoB,EAAE,IAAI;EAC1BC,kBAAkB,EAAE,KAAK;EACzBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,KAAK;EACjBC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE,IAAI;EAClB;EACAC,qBAAqB,EAAE;AACzB,CAAC;AACD,IAAIC,GAAG,GAAG,CAAC,CAAC;AACZ,IAAIC,SAAS,GACX;AACA,KACD;AACD,SACExE,KAAK,EACLuE,GAAG,EACHC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}