{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, n as forceUpdate, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { d as debounceEvent, b as inheritAttributes, c as componentOnReady, r as raf } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { a as arrowBackSharp, s as searchOutline, e as searchSharp, b as closeCircle, d as closeSharp } from './index-BLV6ykCk.js';\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #0054e9);--clear-button-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:17px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, var(--ion-text-color-step-100, #1a1a1a));--clear-button-color:initial;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\nconst Searchbar = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionClear = createEvent(this, \"ionClear\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.isCancelVisible = false;\n    this.shouldAlignLeft = true;\n    this.inputId = `ion-searchbar-${searchbarIds++}`;\n    this.inheritedAttributes = {};\n    this.focused = false;\n    this.noAnimate = true;\n    /**\n     * If `true`, enable searchbar animation.\n     */\n    this.animated = false;\n    /**\n     * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n     * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n     */\n    this.autocapitalize = 'off';\n    /**\n     * Set the input's autocomplete property.\n     */\n    this.autocomplete = 'off';\n    /**\n     * Set the input's autocorrect property.\n     */\n    this.autocorrect = 'off';\n    /**\n     * Set the cancel button icon. Only applies to `md` mode.\n     * Defaults to `arrow-back-sharp`.\n     */\n    this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n    /**\n     * Set the cancel button text. Only applies to `ios` mode.\n     */\n    this.cancelButtonText = 'Cancel';\n    /**\n     * If `true`, the user cannot interact with the input.\n     */\n    this.disabled = false;\n    /**\n     * If used in a form, set the name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * Set the input's placeholder.\n     * `placeholder` can accept either plaintext or HTML as a string.\n     * To display characters normally reserved for HTML, they\n     * must be escaped. For example `<Ionic>` would become\n     * `&lt;Ionic&gt;`\n     *\n     * For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)\n     */\n    this.placeholder = 'Search';\n    /**\n     * Sets the behavior for the cancel button. Defaults to `\"never\"`.\n     * Setting to `\"focus\"` shows the cancel button on focus.\n     * Setting to `\"never\"` hides the cancel button.\n     * Setting to `\"always\"` shows the cancel button regardless\n     * of focus state.\n     */\n    this.showCancelButton = 'never';\n    /**\n     * Sets the behavior for the clear button. Defaults to `\"focus\"`.\n     * Setting to `\"focus\"` shows the clear button on focus if the\n     * input is not empty.\n     * Setting to `\"never\"` hides the clear button.\n     * Setting to `\"always\"` shows the clear button regardless\n     * of focus state, but only if the input is not empty.\n     */\n    this.showClearButton = 'always';\n    /**\n     * If `true`, enable spellcheck on the input.\n     */\n    this.spellcheck = false;\n    /**\n     * Set the type of the input.\n     */\n    this.type = 'search';\n    /**\n     * the value of the searchbar.\n     */\n    this.value = '';\n    /**\n     * Clears the input field and triggers the control change.\n     */\n    this.onClearInput = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (shouldFocus) {\n        _this.ionClear.emit();\n        return new Promise(resolve => {\n          // setTimeout() fixes https://github.com/ionic-team/ionic-framework/issues/7527\n          // wait for 4 frames\n          setTimeout(() => {\n            const value = _this.getValue();\n            if (value !== '') {\n              _this.value = '';\n              _this.emitInputChange();\n              /**\n               * When tapping clear button\n               * ensure input is focused after\n               * clearing input so users\n               * can quickly start typing.\n               */\n              if (shouldFocus && !_this.focused) {\n                _this.setFocus();\n                /**\n                 * The setFocus call above will clear focusedValue,\n                 * but ionChange will never have gotten a chance to\n                 * fire. Manually revert focusedValue so onBlur can\n                 * compare against what was in the box before the clear.\n                 */\n                _this.focusedValue = value;\n              }\n            }\n            resolve();\n          }, 16 * 4);\n        });\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    /**\n     * Clears the input field and tells the input to blur since\n     * the clearInput function doesn't want the input to blur\n     * then calls the custom cancel function if the user passed one in.\n     */\n    this.onCancelSearchbar = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (ev) {\n        if (ev) {\n          ev.preventDefault();\n          ev.stopPropagation();\n        }\n        _this.ionCancel.emit();\n        // get cached values before clearing the input\n        const value = _this.getValue();\n        const focused = _this.focused;\n        yield _this.onClearInput();\n        /**\n         * If there used to be something in the box, and we weren't focused\n         * beforehand (meaning no blur fired that would already handle this),\n         * manually fire ionChange.\n         */\n        if (value && !focused) {\n          _this.emitValueChange(ev);\n        }\n        if (_this.nativeInput) {\n          _this.nativeInput.blur();\n        }\n      });\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    /**\n     * Update the Searchbar input value when the input changes\n     */\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value;\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    /**\n     * Sets the Searchbar to not focused and checks if it should align left\n     * based on whether there is a value in the searchbar or not.\n     */\n    this.onBlur = ev => {\n      this.focused = false;\n      this.ionBlur.emit();\n      this.positionElements();\n      if (this.focusedValue !== this.value) {\n        this.emitValueChange(ev);\n      }\n      this.focusedValue = undefined;\n    };\n    /**\n     * Sets the Searchbar to focused and active on input focus.\n     */\n    this.onFocus = () => {\n      this.focused = true;\n      this.focusedValue = this.value;\n      this.ionFocus.emit();\n      this.positionElements();\n    };\n  }\n  /**\n   * lang and dir are globally enumerated attributes.\n   * As a result, creating these as properties\n   * can have unintended side effects. Instead, we\n   * listen for attribute changes and inherit them\n   * to the inner `<input>` element.\n   */\n  onLangChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      lang: newValue\n    });\n    forceUpdate(this);\n  }\n  onDirChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      dir: newValue\n    });\n    forceUpdate(this);\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  valueChanged() {\n    const inputEl = this.nativeInput;\n    const value = this.getValue();\n    if (inputEl && inputEl.value !== value) {\n      inputEl.value = value;\n    }\n  }\n  showCancelButtonChanged() {\n    requestAnimationFrame(() => {\n      this.positionElements();\n      forceUpdate(this);\n    });\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.positionElements();\n    this.debounceChanged();\n    setTimeout(() => {\n      this.noAnimate = false;\n    }, 300);\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      searchbar: true\n    });\n  }\n  /**\n   * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n   * `input.focus()`.\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  setFocus() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.nativeInput) {\n        _this2.nativeInput.focus();\n      }\n    })();\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  getInputElement() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this3.nativeInput) {\n        yield new Promise(resolve => componentOnReady(_this3.el, resolve));\n      }\n      return Promise.resolve(_this3.nativeInput);\n    })();\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    this.ionInput.emit({\n      value,\n      event\n    });\n  }\n  /**\n   * Positions the input search icon, placeholder, and the cancel button\n   * based on the input value and if it is focused. (ios only)\n   */\n  positionElements() {\n    const value = this.getValue();\n    const prevAlignLeft = this.shouldAlignLeft;\n    const mode = getIonMode(this);\n    const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n    this.shouldAlignLeft = shouldAlignLeft;\n    if (mode !== 'ios') {\n      return;\n    }\n    if (prevAlignLeft !== shouldAlignLeft) {\n      this.positionPlaceholder();\n    }\n    if (this.animated) {\n      this.positionCancelButton();\n    }\n  }\n  /**\n   * Positions the input placeholder\n   */\n  positionPlaceholder() {\n    const inputEl = this.nativeInput;\n    if (!inputEl) {\n      return;\n    }\n    const rtl = isRTL(this.el);\n    const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n    if (this.shouldAlignLeft) {\n      inputEl.removeAttribute('style');\n      iconEl.removeAttribute('style');\n    } else {\n      // Create a dummy span to get the placeholder width\n      const doc = document;\n      const tempSpan = doc.createElement('span');\n      tempSpan.innerText = this.placeholder || '';\n      doc.body.appendChild(tempSpan);\n      // Get the width of the span then remove it\n      raf(() => {\n        const textWidth = tempSpan.offsetWidth;\n        tempSpan.remove();\n        // Calculate the input padding\n        const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n        // Calculate the icon margin\n        /**\n         * We take the icon width to account\n         * for any text scales applied to the icon\n         * such as Dynamic Type on iOS as well as 8px\n         * of padding.\n         */\n        const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n        // Set the input padding start and icon margin start\n        if (rtl) {\n          inputEl.style.paddingRight = inputLeft;\n          iconEl.style.marginRight = iconLeft;\n        } else {\n          inputEl.style.paddingLeft = inputLeft;\n          iconEl.style.marginLeft = iconLeft;\n        }\n      });\n    }\n  }\n  /**\n   * Show the iOS Cancel button on focus, hide it offscreen otherwise\n   */\n  positionCancelButton() {\n    const rtl = isRTL(this.el);\n    const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n    const shouldShowCancel = this.shouldShowCancelButton();\n    if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n      const cancelStyle = cancelButton.style;\n      this.isCancelVisible = shouldShowCancel;\n      if (shouldShowCancel) {\n        if (rtl) {\n          cancelStyle.marginLeft = '0';\n        } else {\n          cancelStyle.marginRight = '0';\n        }\n      } else {\n        const offset = cancelButton.offsetWidth;\n        if (offset > 0) {\n          if (rtl) {\n            cancelStyle.marginLeft = -offset + 'px';\n          } else {\n            cancelStyle.marginRight = -offset + 'px';\n          }\n        }\n      }\n    }\n  }\n  getValue() {\n    return this.value || '';\n  }\n  hasValue() {\n    return this.getValue() !== '';\n  }\n  /**\n   * Determines whether or not the cancel button should be visible onscreen.\n   * Cancel button should be shown if one of two conditions applies:\n   * 1. `showCancelButton` is set to `always`.\n   * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowCancelButton() {\n    if (this.showCancelButton === 'never' || this.showCancelButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Determines whether or not the clear button should be visible onscreen.\n   * Clear button should be shown if one of two conditions applies:\n   * 1. `showClearButton` is set to `always`.\n   * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowClearButton() {\n    if (this.showClearButton === 'never' || this.showClearButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  render() {\n    const {\n      cancelButtonText,\n      autocapitalize\n    } = this;\n    const animated = this.animated && config.getBoolean('animated', true);\n    const mode = getIonMode(this);\n    const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n    const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n    const shouldShowCancelButton = this.shouldShowCancelButton();\n    const cancelButton = this.showCancelButton !== 'never' && h(\"button\", {\n      key: '19e18775856db87daeb4b9e3d7bca0461915a0df',\n      \"aria-label\": cancelButtonText,\n      \"aria-hidden\": shouldShowCancelButton ? undefined : 'true',\n      type: \"button\",\n      tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined,\n      onMouseDown: this.onCancelSearchbar,\n      onTouchStart: this.onCancelSearchbar,\n      class: \"searchbar-cancel-button\"\n    }, h(\"div\", {\n      key: 'b3bbdcc033f3bd3441d619e4a252cef0dad4d07e',\n      \"aria-hidden\": \"true\"\n    }, mode === 'md' ? h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: this.cancelButtonIcon,\n      lazy: false\n    }) : cancelButtonText));\n    return h(Host, {\n      key: '074aa60e051bfb3225e87d44bbb6346c59c73574',\n      role: \"search\",\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'searchbar-animated': animated,\n        'searchbar-disabled': this.disabled,\n        'searchbar-no-animate': animated && this.noAnimate,\n        'searchbar-has-value': this.hasValue(),\n        'searchbar-left-aligned': this.shouldAlignLeft,\n        'searchbar-has-focus': this.focused,\n        'searchbar-should-show-clear': this.shouldShowClearButton(),\n        'searchbar-should-show-cancel': this.shouldShowCancelButton()\n      })\n    }, h(\"div\", {\n      key: '54f58a79fe36e85d9295157303f1be89c98bbdaf',\n      class: \"searchbar-input-container\"\n    }, h(\"input\", Object.assign({\n      key: 'f991a37fcf54d26b7ad10d89084764e03d97b9de',\n      \"aria-label\": \"search text\",\n      disabled: this.disabled,\n      ref: el => this.nativeInput = el,\n      class: \"searchbar-input\",\n      inputMode: this.inputmode,\n      enterKeyHint: this.enterkeyhint,\n      name: this.name,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      placeholder: this.placeholder,\n      type: this.type,\n      value: this.getValue(),\n      autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      spellcheck: this.spellcheck\n    }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", {\n      key: '8b44dd90a3292c5cf872ef16a8520675f5673494',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: searchIcon,\n      lazy: false,\n      class: \"searchbar-search-icon\"\n    }), h(\"button\", {\n      key: '79d9cfed8f01268044f82811a35d323a12dca749',\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      \"no-blur\": true,\n      class: \"searchbar-clear-button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: () => this.onClearInput(true)\n    }, h(\"ion-icon\", {\n      key: 'aa3b9fa8a61f853236783ac7bcd0b113ea65ece2',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: clearIcon,\n      lazy: false,\n      class: \"searchbar-clear-icon\"\n    }))), mode === 'ios' && cancelButton);\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"lang\": [\"onLangChanged\"],\n      \"dir\": [\"onDirChanged\"],\n      \"debounce\": [\"debounceChanged\"],\n      \"value\": [\"valueChanged\"],\n      \"showCancelButton\": [\"showCancelButtonChanged\"]\n    };\n  }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n  ios: searchbarIosCss,\n  md: searchbarMdCss\n};\nexport { Searchbar as ion_searchbar };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "l", "config", "n", "forceUpdate", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "debounceEvent", "b", "inheritAttributes", "c", "componentOnReady", "raf", "i", "isRTL", "createColorClasses", "a", "arrowBackSharp", "s", "searchOutline", "searchSharp", "closeCircle", "closeSharp", "searchbarIosCss", "searchbarMdCss", "Searchbar", "constructor", "hostRef", "_this", "ionInput", "ionChange", "ionCancel", "ionClear", "ionBlur", "ionFocus", "ionStyle", "isCancelVisible", "shouldAlignLeft", "inputId", "searchbarIds", "inheritedAttributes", "focused", "noAnimate", "animated", "autocapitalize", "autocomplete", "autocorrect", "cancelButtonIcon", "get", "cancelButtonText", "disabled", "name", "placeholder", "showCancelButton", "showClearButton", "spellcheck", "type", "value", "onClearInput", "_ref", "_asyncToGenerator", "shouldFocus", "emit", "Promise", "resolve", "setTimeout", "getValue", "emitInputChange", "setFocus", "focusedValue", "_x", "apply", "arguments", "onCancelSearchbar", "_ref2", "ev", "preventDefault", "stopPropagation", "emitValueChange", "nativeInput", "blur", "_x2", "onInput", "input", "target", "onChange", "onBlur", "positionElements", "undefined", "onFocus", "onLangChanged", "newValue", "Object", "assign", "lang", "onDirChanged", "dir", "debounce<PERSON><PERSON>ed", "debounce", "originalIonInput", "valueChanged", "inputEl", "showCancelButtonChanged", "requestAnimationFrame", "connectedCallback", "emitStyle", "componentWillLoad", "el", "componentDidLoad", "searchbar", "_this2", "focus", "getInputElement", "_this3", "event", "toString", "prevAlignLeft", "mode", "trim", "positionPlaceholder", "positionCancelButton", "rtl", "iconEl", "shadowRoot", "querySelector", "removeAttribute", "doc", "document", "tempSpan", "createElement", "innerText", "body", "append<PERSON><PERSON><PERSON>", "textWidth", "offsetWidth", "remove", "inputLeft", "iconLeft", "clientWidth", "style", "paddingRight", "marginRight", "paddingLeft", "marginLeft", "cancelButton", "shouldShowCancel", "shouldShowCancelButton", "cancelStyle", "offset", "hasValue", "shouldShowClearButton", "render", "getBoolean", "clearIcon", "searchIcon", "key", "tabIndex", "onMouseDown", "onTouchStart", "class", "icon", "lazy", "role", "color", "ref", "inputMode", "inputmode", "enterKeyHint", "enterkeyhint", "<PERSON><PERSON><PERSON><PERSON>", "minlength", "max<PERSON><PERSON><PERSON>", "maxlength", "autoCapitalize", "autoComplete", "autoCorrect", "onPointerDown", "onClick", "watchers", "ios", "md", "ion_searchbar"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-searchbar.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, n as forceUpdate, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { d as debounceEvent, b as inheritAttributes, c as componentOnReady, r as raf } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { a as arrowBackSharp, s as searchOutline, e as searchSharp, b as closeCircle, d as closeSharp } from './index-BLV6ykCk.js';\n\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #0054e9);--clear-button-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:17px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\n\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, var(--ion-text-color-step-100, #1a1a1a));--clear-button-color:initial;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\n\nconst Searchbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionClear = createEvent(this, \"ionClear\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.isCancelVisible = false;\n        this.shouldAlignLeft = true;\n        this.inputId = `ion-searchbar-${searchbarIds++}`;\n        this.inheritedAttributes = {};\n        this.focused = false;\n        this.noAnimate = true;\n        /**\n         * If `true`, enable searchbar animation.\n         */\n        this.animated = false;\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'off';\n        /**\n         * Set the input's autocomplete property.\n         */\n        this.autocomplete = 'off';\n        /**\n         * Set the input's autocorrect property.\n         */\n        this.autocorrect = 'off';\n        /**\n         * Set the cancel button icon. Only applies to `md` mode.\n         * Defaults to `arrow-back-sharp`.\n         */\n        this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n        /**\n         * Set the cancel button text. Only applies to `ios` mode.\n         */\n        this.cancelButtonText = 'Cancel';\n        /**\n         * If `true`, the user cannot interact with the input.\n         */\n        this.disabled = false;\n        /**\n         * If used in a form, set the name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * Set the input's placeholder.\n         * `placeholder` can accept either plaintext or HTML as a string.\n         * To display characters normally reserved for HTML, they\n         * must be escaped. For example `<Ionic>` would become\n         * `&lt;Ionic&gt;`\n         *\n         * For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)\n         */\n        this.placeholder = 'Search';\n        /**\n         * Sets the behavior for the cancel button. Defaults to `\"never\"`.\n         * Setting to `\"focus\"` shows the cancel button on focus.\n         * Setting to `\"never\"` hides the cancel button.\n         * Setting to `\"always\"` shows the cancel button regardless\n         * of focus state.\n         */\n        this.showCancelButton = 'never';\n        /**\n         * Sets the behavior for the clear button. Defaults to `\"focus\"`.\n         * Setting to `\"focus\"` shows the clear button on focus if the\n         * input is not empty.\n         * Setting to `\"never\"` hides the clear button.\n         * Setting to `\"always\"` shows the clear button regardless\n         * of focus state, but only if the input is not empty.\n         */\n        this.showClearButton = 'always';\n        /**\n         * If `true`, enable spellcheck on the input.\n         */\n        this.spellcheck = false;\n        /**\n         * Set the type of the input.\n         */\n        this.type = 'search';\n        /**\n         * the value of the searchbar.\n         */\n        this.value = '';\n        /**\n         * Clears the input field and triggers the control change.\n         */\n        this.onClearInput = async (shouldFocus) => {\n            this.ionClear.emit();\n            return new Promise((resolve) => {\n                // setTimeout() fixes https://github.com/ionic-team/ionic-framework/issues/7527\n                // wait for 4 frames\n                setTimeout(() => {\n                    const value = this.getValue();\n                    if (value !== '') {\n                        this.value = '';\n                        this.emitInputChange();\n                        /**\n                         * When tapping clear button\n                         * ensure input is focused after\n                         * clearing input so users\n                         * can quickly start typing.\n                         */\n                        if (shouldFocus && !this.focused) {\n                            this.setFocus();\n                            /**\n                             * The setFocus call above will clear focusedValue,\n                             * but ionChange will never have gotten a chance to\n                             * fire. Manually revert focusedValue so onBlur can\n                             * compare against what was in the box before the clear.\n                             */\n                            this.focusedValue = value;\n                        }\n                    }\n                    resolve();\n                }, 16 * 4);\n            });\n        };\n        /**\n         * Clears the input field and tells the input to blur since\n         * the clearInput function doesn't want the input to blur\n         * then calls the custom cancel function if the user passed one in.\n         */\n        this.onCancelSearchbar = async (ev) => {\n            if (ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            this.ionCancel.emit();\n            // get cached values before clearing the input\n            const value = this.getValue();\n            const focused = this.focused;\n            await this.onClearInput();\n            /**\n             * If there used to be something in the box, and we weren't focused\n             * beforehand (meaning no blur fired that would already handle this),\n             * manually fire ionChange.\n             */\n            if (value && !focused) {\n                this.emitValueChange(ev);\n            }\n            if (this.nativeInput) {\n                this.nativeInput.blur();\n            }\n        };\n        /**\n         * Update the Searchbar input value when the input changes\n         */\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value;\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        /**\n         * Sets the Searchbar to not focused and checks if it should align left\n         * based on whether there is a value in the searchbar or not.\n         */\n        this.onBlur = (ev) => {\n            this.focused = false;\n            this.ionBlur.emit();\n            this.positionElements();\n            if (this.focusedValue !== this.value) {\n                this.emitValueChange(ev);\n            }\n            this.focusedValue = undefined;\n        };\n        /**\n         * Sets the Searchbar to focused and active on input focus.\n         */\n        this.onFocus = () => {\n            this.focused = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit();\n            this.positionElements();\n        };\n    }\n    /**\n     * lang and dir are globally enumerated attributes.\n     * As a result, creating these as properties\n     * can have unintended side effects. Instead, we\n     * listen for attribute changes and inherit them\n     * to the inner `<input>` element.\n     */\n    onLangChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { lang: newValue });\n        forceUpdate(this);\n    }\n    onDirChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { dir: newValue });\n        forceUpdate(this);\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    valueChanged() {\n        const inputEl = this.nativeInput;\n        const value = this.getValue();\n        if (inputEl && inputEl.value !== value) {\n            inputEl.value = value;\n        }\n    }\n    showCancelButtonChanged() {\n        requestAnimationFrame(() => {\n            this.positionElements();\n            forceUpdate(this);\n        });\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.positionElements();\n        this.debounceChanged();\n        setTimeout(() => {\n            this.noAnimate = false;\n        }, 300);\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            searchbar: true,\n        });\n    }\n    /**\n     * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n     * `input.focus()`.\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    /**\n     * Positions the input search icon, placeholder, and the cancel button\n     * based on the input value and if it is focused. (ios only)\n     */\n    positionElements() {\n        const value = this.getValue();\n        const prevAlignLeft = this.shouldAlignLeft;\n        const mode = getIonMode(this);\n        const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n        this.shouldAlignLeft = shouldAlignLeft;\n        if (mode !== 'ios') {\n            return;\n        }\n        if (prevAlignLeft !== shouldAlignLeft) {\n            this.positionPlaceholder();\n        }\n        if (this.animated) {\n            this.positionCancelButton();\n        }\n    }\n    /**\n     * Positions the input placeholder\n     */\n    positionPlaceholder() {\n        const inputEl = this.nativeInput;\n        if (!inputEl) {\n            return;\n        }\n        const rtl = isRTL(this.el);\n        const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n        if (this.shouldAlignLeft) {\n            inputEl.removeAttribute('style');\n            iconEl.removeAttribute('style');\n        }\n        else {\n            // Create a dummy span to get the placeholder width\n            const doc = document;\n            const tempSpan = doc.createElement('span');\n            tempSpan.innerText = this.placeholder || '';\n            doc.body.appendChild(tempSpan);\n            // Get the width of the span then remove it\n            raf(() => {\n                const textWidth = tempSpan.offsetWidth;\n                tempSpan.remove();\n                // Calculate the input padding\n                const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n                // Calculate the icon margin\n                /**\n                 * We take the icon width to account\n                 * for any text scales applied to the icon\n                 * such as Dynamic Type on iOS as well as 8px\n                 * of padding.\n                 */\n                const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n                // Set the input padding start and icon margin start\n                if (rtl) {\n                    inputEl.style.paddingRight = inputLeft;\n                    iconEl.style.marginRight = iconLeft;\n                }\n                else {\n                    inputEl.style.paddingLeft = inputLeft;\n                    iconEl.style.marginLeft = iconLeft;\n                }\n            });\n        }\n    }\n    /**\n     * Show the iOS Cancel button on focus, hide it offscreen otherwise\n     */\n    positionCancelButton() {\n        const rtl = isRTL(this.el);\n        const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n        const shouldShowCancel = this.shouldShowCancelButton();\n        if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n            const cancelStyle = cancelButton.style;\n            this.isCancelVisible = shouldShowCancel;\n            if (shouldShowCancel) {\n                if (rtl) {\n                    cancelStyle.marginLeft = '0';\n                }\n                else {\n                    cancelStyle.marginRight = '0';\n                }\n            }\n            else {\n                const offset = cancelButton.offsetWidth;\n                if (offset > 0) {\n                    if (rtl) {\n                        cancelStyle.marginLeft = -offset + 'px';\n                    }\n                    else {\n                        cancelStyle.marginRight = -offset + 'px';\n                    }\n                }\n            }\n        }\n    }\n    getValue() {\n        return this.value || '';\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    /**\n     * Determines whether or not the cancel button should be visible onscreen.\n     * Cancel button should be shown if one of two conditions applies:\n     * 1. `showCancelButton` is set to `always`.\n     * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowCancelButton() {\n        if (this.showCancelButton === 'never' || (this.showCancelButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Determines whether or not the clear button should be visible onscreen.\n     * Clear button should be shown if one of two conditions applies:\n     * 1. `showClearButton` is set to `always`.\n     * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowClearButton() {\n        if (this.showClearButton === 'never' || (this.showClearButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    render() {\n        const { cancelButtonText, autocapitalize } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const mode = getIonMode(this);\n        const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n        const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n        const shouldShowCancelButton = this.shouldShowCancelButton();\n        const cancelButton = this.showCancelButton !== 'never' && (h(\"button\", { key: '19e18775856db87daeb4b9e3d7bca0461915a0df', \"aria-label\": cancelButtonText, \"aria-hidden\": shouldShowCancelButton ? undefined : 'true', type: \"button\", tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined, onMouseDown: this.onCancelSearchbar, onTouchStart: this.onCancelSearchbar, class: \"searchbar-cancel-button\" }, h(\"div\", { key: 'b3bbdcc033f3bd3441d619e4a252cef0dad4d07e', \"aria-hidden\": \"true\" }, mode === 'md' ? (h(\"ion-icon\", { \"aria-hidden\": \"true\", mode: mode, icon: this.cancelButtonIcon, lazy: false })) : (cancelButtonText))));\n        return (h(Host, { key: '074aa60e051bfb3225e87d44bbb6346c59c73574', role: \"search\", \"aria-disabled\": this.disabled ? 'true' : null, class: createColorClasses(this.color, {\n                [mode]: true,\n                'searchbar-animated': animated,\n                'searchbar-disabled': this.disabled,\n                'searchbar-no-animate': animated && this.noAnimate,\n                'searchbar-has-value': this.hasValue(),\n                'searchbar-left-aligned': this.shouldAlignLeft,\n                'searchbar-has-focus': this.focused,\n                'searchbar-should-show-clear': this.shouldShowClearButton(),\n                'searchbar-should-show-cancel': this.shouldShowCancelButton(),\n            }) }, h(\"div\", { key: '54f58a79fe36e85d9295157303f1be89c98bbdaf', class: \"searchbar-input-container\" }, h(\"input\", Object.assign({ key: 'f991a37fcf54d26b7ad10d89084764e03d97b9de', \"aria-label\": \"search text\", disabled: this.disabled, ref: (el) => (this.nativeInput = el), class: \"searchbar-input\", inputMode: this.inputmode, enterKeyHint: this.enterkeyhint, name: this.name, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, minLength: this.minlength, maxLength: this.maxlength, placeholder: this.placeholder, type: this.type, value: this.getValue(), autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, spellcheck: this.spellcheck }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", { key: '8b44dd90a3292c5cf872ef16a8520675f5673494', \"aria-hidden\": \"true\", mode: mode, icon: searchIcon, lazy: false, class: \"searchbar-search-icon\" }), h(\"button\", { key: '79d9cfed8f01268044f82811a35d323a12dca749', \"aria-label\": \"reset\", type: \"button\", \"no-blur\": true, class: \"searchbar-clear-button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: () => this.onClearInput(true) }, h(\"ion-icon\", { key: 'aa3b9fa8a61f853236783ac7bcd0b113ea65ece2', \"aria-hidden\": \"true\", mode: mode, icon: clearIcon, lazy: false, class: \"searchbar-clear-icon\" }))), mode === 'ios' && cancelButton));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"lang\": [\"onLangChanged\"],\n        \"dir\": [\"onDirChanged\"],\n        \"debounce\": [\"debounceChanged\"],\n        \"value\": [\"valueChanged\"],\n        \"showCancelButton\": [\"showCancelButtonChanged\"]\n    }; }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n    ios: searchbarIosCss,\n    md: searchbarMdCss\n};\n\nexport { Searchbar as ion_searchbar };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5J,SAASZ,CAAC,IAAIa,aAAa,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEnB,CAAC,IAAIoB,GAAG,QAAQ,uBAAuB;AACnH,SAASC,CAAC,IAAIC,KAAK,QAAQ,mBAAmB;AAC9C,SAASJ,CAAC,IAAIK,kBAAkB,QAAQ,qBAAqB;AAC7D,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,aAAa,EAAEnB,CAAC,IAAIoB,WAAW,EAAEZ,CAAC,IAAIa,WAAW,EAAE3B,CAAC,IAAI4B,UAAU,QAAQ,qBAAqB;AAElI,MAAMC,eAAe,GAAG,k2RAAk2R;AAE13R,MAAMC,cAAc,GAAG,46NAA46N;AAEn8N,MAAMC,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBnC,gBAAgB,CAAC,IAAI,EAAEkC,OAAO,CAAC;IAC/B,IAAI,CAACE,QAAQ,GAAGlC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmC,SAAS,GAAGnC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACoC,SAAS,GAAGpC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqC,QAAQ,GAAGrC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsC,OAAO,GAAGtC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACuC,QAAQ,GAAGvC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACwC,QAAQ,GAAGxC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,OAAO,GAAG,iBAAiBC,YAAY,EAAE,EAAE;IAChD,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAGlD,MAAM,CAACmD,GAAG,CAAC,gBAAgB,EAAE/B,cAAc,CAAC;IACpE;AACR;AACA;IACQ,IAAI,CAACgC,gBAAgB,GAAG,QAAQ;IAChC;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACb,OAAO;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACc,WAAW,GAAG,QAAQ;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,QAAQ;IAC/B;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;AACR;AACA;IACQ,IAAI,CAACC,YAAY;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,WAAW,EAAK;QACvCjC,KAAI,CAACI,QAAQ,CAAC8B,IAAI,CAAC,CAAC;QACpB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;UAC5B;UACA;UACAC,UAAU,CAAC,MAAM;YACb,MAAMR,KAAK,GAAG7B,KAAI,CAACsC,QAAQ,CAAC,CAAC;YAC7B,IAAIT,KAAK,KAAK,EAAE,EAAE;cACd7B,KAAI,CAAC6B,KAAK,GAAG,EAAE;cACf7B,KAAI,CAACuC,eAAe,CAAC,CAAC;cACtB;AACxB;AACA;AACA;AACA;AACA;cACwB,IAAIN,WAAW,IAAI,CAACjC,KAAI,CAACa,OAAO,EAAE;gBAC9Bb,KAAI,CAACwC,QAAQ,CAAC,CAAC;gBACf;AAC5B;AACA;AACA;AACA;AACA;gBAC4BxC,KAAI,CAACyC,YAAY,GAAGZ,KAAK;cAC7B;YACJ;YACAO,OAAO,CAAC,CAAC;UACb,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACd,CAAC,CAAC;MACN,CAAC;MAAA,iBAAAM,EAAA;QAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB;MAAA,IAAAC,KAAA,GAAAd,iBAAA,CAAG,WAAOe,EAAE,EAAK;QACnC,IAAIA,EAAE,EAAE;UACJA,EAAE,CAACC,cAAc,CAAC,CAAC;UACnBD,EAAE,CAACE,eAAe,CAAC,CAAC;QACxB;QACAjD,KAAI,CAACG,SAAS,CAAC+B,IAAI,CAAC,CAAC;QACrB;QACA,MAAML,KAAK,GAAG7B,KAAI,CAACsC,QAAQ,CAAC,CAAC;QAC7B,MAAMzB,OAAO,GAAGb,KAAI,CAACa,OAAO;QAC5B,MAAMb,KAAI,CAAC8B,YAAY,CAAC,CAAC;QACzB;AACZ;AACA;AACA;AACA;QACY,IAAID,KAAK,IAAI,CAAChB,OAAO,EAAE;UACnBb,KAAI,CAACkD,eAAe,CAACH,EAAE,CAAC;QAC5B;QACA,IAAI/C,KAAI,CAACmD,WAAW,EAAE;UAClBnD,KAAI,CAACmD,WAAW,CAACC,IAAI,CAAC,CAAC;QAC3B;MACJ,CAAC;MAAA,iBAAAC,GAAA;QAAA,OAAAP,KAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD;AACR;AACA;IACQ,IAAI,CAACU,OAAO,GAAIP,EAAE,IAAK;MACnB,MAAMQ,KAAK,GAAGR,EAAE,CAACS,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAAC1B,KAAK,GAAG0B,KAAK,CAAC1B,KAAK;MAC5B;MACA,IAAI,CAACU,eAAe,CAACQ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACU,QAAQ,GAAIV,EAAE,IAAK;MACpB,IAAI,CAACG,eAAe,CAACH,EAAE,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACW,MAAM,GAAIX,EAAE,IAAK;MAClB,IAAI,CAAClC,OAAO,GAAG,KAAK;MACpB,IAAI,CAACR,OAAO,CAAC6B,IAAI,CAAC,CAAC;MACnB,IAAI,CAACyB,gBAAgB,CAAC,CAAC;MACvB,IAAI,IAAI,CAAClB,YAAY,KAAK,IAAI,CAACZ,KAAK,EAAE;QAClC,IAAI,CAACqB,eAAe,CAACH,EAAE,CAAC;MAC5B;MACA,IAAI,CAACN,YAAY,GAAGmB,SAAS;IACjC,CAAC;IACD;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAChD,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC4B,YAAY,GAAG,IAAI,CAACZ,KAAK;MAC9B,IAAI,CAACvB,QAAQ,CAAC4B,IAAI,CAAC,CAAC;MACpB,IAAI,CAACyB,gBAAgB,CAAC,CAAC;IAC3B,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACnD,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrD,mBAAmB,CAAC,EAAE;MAAEsD,IAAI,EAAEH;IAAS,CAAC,CAAC;IACzG5F,WAAW,CAAC,IAAI,CAAC;EACrB;EACAgG,YAAYA,CAACJ,QAAQ,EAAE;IACnB,IAAI,CAACnD,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrD,mBAAmB,CAAC,EAAE;MAAEwD,GAAG,EAAEL;IAAS,CAAC,CAAC;IACxG5F,WAAW,CAAC,IAAI,CAAC;EACrB;EACAkG,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEpE,QAAQ;MAAEqE,QAAQ;MAAEC;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACtE,QAAQ,GAAGqE,QAAQ,KAAKV,SAAS,GAAGW,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGtE,QAAQ,GAAGtB,aAAa,CAACsB,QAAQ,EAAEqE,QAAQ,CAAC;EACvK;EACAE,YAAYA,CAAA,EAAG;IACX,MAAMC,OAAO,GAAG,IAAI,CAACtB,WAAW;IAChC,MAAMtB,KAAK,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC;IAC7B,IAAImC,OAAO,IAAIA,OAAO,CAAC5C,KAAK,KAAKA,KAAK,EAAE;MACpC4C,OAAO,CAAC5C,KAAK,GAAGA,KAAK;IACzB;EACJ;EACA6C,uBAAuBA,CAAA,EAAG;IACtBC,qBAAqB,CAAC,MAAM;MACxB,IAAI,CAAChB,gBAAgB,CAAC,CAAC;MACvBxF,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC,CAAC;EACN;EACAyG,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAClE,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpF,iBAAiB,CAAC,IAAI,CAACkG,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;EAC7F;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACT,gBAAgB,GAAG,IAAI,CAACtE,QAAQ;IACrC,IAAI,CAAC0D,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACU,eAAe,CAAC,CAAC;IACtBhC,UAAU,CAAC,MAAM;MACb,IAAI,CAACvB,SAAS,GAAG,KAAK;IAC1B,CAAC,EAAE,GAAG,CAAC;EACX;EACA+D,SAASA,CAAA,EAAG;IACR,IAAI,CAACtE,QAAQ,CAAC2B,IAAI,CAAC;MACf+C,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUzC,QAAQA,CAAA,EAAG;IAAA,IAAA0C,MAAA;IAAA,OAAAlD,iBAAA;MACb,IAAIkD,MAAI,CAAC/B,WAAW,EAAE;QAClB+B,MAAI,CAAC/B,WAAW,CAACgC,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACqD,MAAI,CAAClC,WAAW,EAAE;QACnB,MAAM,IAAIhB,OAAO,CAAEC,OAAO,IAAKrD,gBAAgB,CAACsG,MAAI,CAACN,EAAE,EAAE3C,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACiD,MAAI,CAAClC,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACID,eAAeA,CAACoC,KAAK,EAAE;IACnB,MAAM;MAAEzD;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMkC,QAAQ,GAAGlC,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAAC0D,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAAC9C,YAAY,GAAGsB,QAAQ;IAC5B,IAAI,CAAC7D,SAAS,CAACgC,IAAI,CAAC;MAAEL,KAAK,EAAEkC,QAAQ;MAAEuB;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACI/C,eAAeA,CAAC+C,KAAK,EAAE;IACnB,MAAM;MAAEzD;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC5B,QAAQ,CAACiC,IAAI,CAAC;MAAEL,KAAK;MAAEyD;IAAM,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACI3B,gBAAgBA,CAAA,EAAG;IACf,MAAM9B,KAAK,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC;IAC7B,MAAMkD,aAAa,GAAG,IAAI,CAAC/E,eAAe;IAC1C,MAAMgF,IAAI,GAAGpH,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoC,eAAe,GAAG,CAAC,IAAI,CAACM,QAAQ,IAAIc,KAAK,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC7E,OAAO;IAC/E,IAAI,CAACJ,eAAe,GAAGA,eAAe;IACtC,IAAIgF,IAAI,KAAK,KAAK,EAAE;MAChB;IACJ;IACA,IAAID,aAAa,KAAK/E,eAAe,EAAE;MACnC,IAAI,CAACkF,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAAC5E,QAAQ,EAAE;MACf,IAAI,CAAC6E,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;EACID,mBAAmBA,CAAA,EAAG;IAClB,MAAMlB,OAAO,GAAG,IAAI,CAACtB,WAAW;IAChC,IAAI,CAACsB,OAAO,EAAE;MACV;IACJ;IACA,MAAMoB,GAAG,GAAG3G,KAAK,CAAC,IAAI,CAAC6F,EAAE,CAAC;IAC1B,MAAMe,MAAM,GAAG,CAAC,IAAI,CAACf,EAAE,CAACgB,UAAU,IAAI,IAAI,CAAChB,EAAE,EAAEiB,aAAa,CAAC,wBAAwB,CAAC;IACtF,IAAI,IAAI,CAACvF,eAAe,EAAE;MACtBgE,OAAO,CAACwB,eAAe,CAAC,OAAO,CAAC;MAChCH,MAAM,CAACG,eAAe,CAAC,OAAO,CAAC;IACnC,CAAC,MACI;MACD;MACA,MAAMC,GAAG,GAAGC,QAAQ;MACpB,MAAMC,QAAQ,GAAGF,GAAG,CAACG,aAAa,CAAC,MAAM,CAAC;MAC1CD,QAAQ,CAACE,SAAS,GAAG,IAAI,CAAC9E,WAAW,IAAI,EAAE;MAC3C0E,GAAG,CAACK,IAAI,CAACC,WAAW,CAACJ,QAAQ,CAAC;MAC9B;MACApH,GAAG,CAAC,MAAM;QACN,MAAMyH,SAAS,GAAGL,QAAQ,CAACM,WAAW;QACtCN,QAAQ,CAACO,MAAM,CAAC,CAAC;QACjB;QACA,MAAMC,SAAS,GAAG,aAAa,GAAGH,SAAS,GAAG,CAAC,GAAG,KAAK;QACvD;QACA;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMI,QAAQ,GAAG,aAAa,IAAIJ,SAAS,GAAG,CAAC,GAAGX,MAAM,CAACgB,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;QACjF;QACA,IAAIjB,GAAG,EAAE;UACLpB,OAAO,CAACsC,KAAK,CAACC,YAAY,GAAGJ,SAAS;UACtCd,MAAM,CAACiB,KAAK,CAACE,WAAW,GAAGJ,QAAQ;QACvC,CAAC,MACI;UACDpC,OAAO,CAACsC,KAAK,CAACG,WAAW,GAAGN,SAAS;UACrCd,MAAM,CAACiB,KAAK,CAACI,UAAU,GAAGN,QAAQ;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIjB,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,GAAG,GAAG3G,KAAK,CAAC,IAAI,CAAC6F,EAAE,CAAC;IAC1B,MAAMqC,YAAY,GAAG,CAAC,IAAI,CAACrC,EAAE,CAACgB,UAAU,IAAI,IAAI,CAAChB,EAAE,EAAEiB,aAAa,CAAC,0BAA0B,CAAC;IAC9F,MAAMqB,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACtD,IAAIF,YAAY,KAAK,IAAI,IAAIC,gBAAgB,KAAK,IAAI,CAAC7G,eAAe,EAAE;MACpE,MAAM+G,WAAW,GAAGH,YAAY,CAACL,KAAK;MACtC,IAAI,CAACvG,eAAe,GAAG6G,gBAAgB;MACvC,IAAIA,gBAAgB,EAAE;QAClB,IAAIxB,GAAG,EAAE;UACL0B,WAAW,CAACJ,UAAU,GAAG,GAAG;QAChC,CAAC,MACI;UACDI,WAAW,CAACN,WAAW,GAAG,GAAG;QACjC;MACJ,CAAC,MACI;QACD,MAAMO,MAAM,GAAGJ,YAAY,CAACV,WAAW;QACvC,IAAIc,MAAM,GAAG,CAAC,EAAE;UACZ,IAAI3B,GAAG,EAAE;YACL0B,WAAW,CAACJ,UAAU,GAAG,CAACK,MAAM,GAAG,IAAI;UAC3C,CAAC,MACI;YACDD,WAAW,CAACN,WAAW,GAAG,CAACO,MAAM,GAAG,IAAI;UAC5C;QACJ;MACJ;IACJ;EACJ;EACAlF,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACT,KAAK,IAAI,EAAE;EAC3B;EACA4F,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnF,QAAQ,CAAC,CAAC,KAAK,EAAE;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgF,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC7F,gBAAgB,KAAK,OAAO,IAAK,IAAI,CAACA,gBAAgB,KAAK,OAAO,IAAI,CAAC,IAAI,CAACZ,OAAQ,EAAE;MAC3F,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6G,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAChG,eAAe,KAAK,OAAO,IAAK,IAAI,CAACA,eAAe,KAAK,OAAO,IAAI,CAAC,IAAI,CAACb,OAAQ,EAAE;MACzF,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA8G,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtG,gBAAgB;MAAEL;IAAe,CAAC,GAAG,IAAI;IACjD,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI9C,MAAM,CAAC2J,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;IACrE,MAAMnC,IAAI,GAAGpH,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwJ,SAAS,GAAG,IAAI,CAACA,SAAS,KAAKpC,IAAI,KAAK,KAAK,GAAGhG,WAAW,GAAGC,UAAU,CAAC;IAC/E,MAAMoI,UAAU,GAAG,IAAI,CAACA,UAAU,KAAKrC,IAAI,KAAK,KAAK,GAAGlG,aAAa,GAAGC,WAAW,CAAC;IACpF,MAAM8H,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC,CAAC;IAC5D,MAAMF,YAAY,GAAG,IAAI,CAAC3F,gBAAgB,KAAK,OAAO,IAAKnD,CAAC,CAAC,QAAQ,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE1G,gBAAgB;MAAE,aAAa,EAAEiG,sBAAsB,GAAG1D,SAAS,GAAG,MAAM;MAAEhC,IAAI,EAAE,QAAQ;MAAEoG,QAAQ,EAAEvC,IAAI,KAAK,KAAK,IAAI,CAAC6B,sBAAsB,GAAG,CAAC,CAAC,GAAG1D,SAAS;MAAEqE,WAAW,EAAE,IAAI,CAACpF,iBAAiB;MAAEqF,YAAY,EAAE,IAAI,CAACrF,iBAAiB;MAAEsF,KAAK,EAAE;IAA0B,CAAC,EAAE7J,CAAC,CAAC,KAAK,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE;IAAO,CAAC,EAAEtC,IAAI,KAAK,IAAI,GAAInH,CAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEmH,IAAI,EAAEA,IAAI;MAAE2C,IAAI,EAAE,IAAI,CAACjH,gBAAgB;MAAEkH,IAAI,EAAE;IAAM,CAAC,CAAC,GAAKhH,gBAAiB,CAAC,CAAE;IACxnB,OAAQ/C,CAAC,CAACE,IAAI,EAAE;MAAEuJ,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE,QAAQ;MAAE,eAAe,EAAE,IAAI,CAAChH,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE6G,KAAK,EAAEhJ,kBAAkB,CAAC,IAAI,CAACoJ,KAAK,EAAE;QACjK,CAAC9C,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE1E,QAAQ;QAC9B,oBAAoB,EAAE,IAAI,CAACO,QAAQ;QACnC,sBAAsB,EAAEP,QAAQ,IAAI,IAAI,CAACD,SAAS;QAClD,qBAAqB,EAAE,IAAI,CAAC2G,QAAQ,CAAC,CAAC;QACtC,wBAAwB,EAAE,IAAI,CAAChH,eAAe;QAC9C,qBAAqB,EAAE,IAAI,CAACI,OAAO;QACnC,6BAA6B,EAAE,IAAI,CAAC6G,qBAAqB,CAAC,CAAC;QAC3D,8BAA8B,EAAE,IAAI,CAACJ,sBAAsB,CAAC;MAChE,CAAC;IAAE,CAAC,EAAEhJ,CAAC,CAAC,KAAK,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA4B,CAAC,EAAE7J,CAAC,CAAC,OAAO,EAAE0F,MAAM,CAACC,MAAM,CAAC;MAAE8D,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,aAAa;MAAEzG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEkH,GAAG,EAAGzD,EAAE,IAAM,IAAI,CAAC5B,WAAW,GAAG4B,EAAG;MAAEoD,KAAK,EAAE,iBAAiB;MAAEM,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,YAAY,EAAE,IAAI,CAACC,YAAY;MAAErH,IAAI,EAAE,IAAI,CAACA,IAAI;MAAE+B,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEG,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEgF,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACC,SAAS;MAAExH,WAAW,EAAE,IAAI,CAACA,WAAW;MAAEI,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,KAAK,EAAE,IAAI,CAACS,QAAQ,CAAC,CAAC;MAAE2G,cAAc,EAAEjI,cAAc,KAAK,SAAS,GAAG4C,SAAS,GAAG5C,cAAc;MAAEkI,YAAY,EAAE,IAAI,CAACjI,YAAY;MAAEkI,WAAW,EAAE,IAAI,CAACjI,WAAW;MAAES,UAAU,EAAE,IAAI,CAACA;IAAW,CAAC,EAAE,IAAI,CAACf,mBAAmB,CAAC,CAAC,EAAE6E,IAAI,KAAK,IAAI,IAAI2B,YAAY,EAAE9I,CAAC,CAAC,UAAU,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEtC,IAAI,EAAEA,IAAI;MAAE2C,IAAI,EAAEN,UAAU;MAAEO,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE;IAAwB,CAAC,CAAC,EAAE7J,CAAC,CAAC,QAAQ,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,OAAO;MAAEnG,IAAI,EAAE,QAAQ;MAAE,SAAS,EAAE,IAAI;MAAEuG,KAAK,EAAE,wBAAwB;MAAEiB,aAAa,EAAGrG,EAAE,IAAK;QACvoC;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEqG,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACvH,YAAY,CAAC,IAAI;IAAE,CAAC,EAAExD,CAAC,CAAC,UAAU,EAAE;MAAEyJ,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEtC,IAAI,EAAEA,IAAI;MAAE2C,IAAI,EAAEP,SAAS;MAAEQ,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE;IAAuB,CAAC,CAAC,CAAC,CAAC,EAAE1C,IAAI,KAAK,KAAK,IAAI2B,YAAY,CAAC;EAC1P;EACA,IAAIrC,EAAEA,CAAA,EAAG;IAAE,OAAOrG,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4K,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;MACzB,KAAK,EAAE,CAAC,cAAc,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,kBAAkB,EAAE,CAAC,yBAAyB;IAClD,CAAC;EAAE;AACP,CAAC;AACD,IAAI3I,YAAY,GAAG,CAAC;AACpBd,SAAS,CAACkH,KAAK,GAAG;EACdwC,GAAG,EAAE5J,eAAe;EACpB6J,EAAE,EAAE5J;AACR,CAAC;AAED,SAASC,SAAS,IAAI4J,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}