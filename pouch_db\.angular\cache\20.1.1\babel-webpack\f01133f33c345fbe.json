{"ast": null, "code": "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\nfunction stringify(arr) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;", "map": {"version": 3, "names": ["validate", "byteToHex", "i", "push", "toString", "substr", "stringify", "arr", "offset", "arguments", "length", "undefined", "uuid", "toLowerCase", "TypeError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nvar byteToHex = [];\n\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\n\nfunction stringify(arr) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC;AACA;AACA;AACA;;AAEA,IAAIC,SAAS,GAAG,EAAE;AAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;EAC5BD,SAAS,CAACE,IAAI,CAAC,CAACD,CAAC,GAAG,KAAK,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpD;AAEA,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF;EACA;EACA,IAAIG,IAAI,GAAG,CAACX,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,EAAEK,WAAW,CAAC,CAAC,CAAC,CAAC;EACxgB;EACA;EACA;EACA;;EAEA,IAAI,CAACb,QAAQ,CAACY,IAAI,CAAC,EAAE;IACnB,MAAME,SAAS,CAAC,6BAA6B,CAAC;EAChD;EAEA,OAAOF,IAAI;AACb;AAEA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}