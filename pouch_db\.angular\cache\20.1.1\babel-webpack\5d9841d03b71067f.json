{"ast": null, "code": "/** @typedef {\"info\" | \"warning\" | \"error\"} LogLevel */\n\n/** @type {LogLevel} */\nvar logLevel = \"info\";\nfunction dummy() {}\n\n/**\n * @param {LogLevel} level log level\n * @returns {boolean} true, if should log\n */\nfunction shouldLog(level) {\n  var shouldLog = logLevel === \"info\" && level === \"info\" || [\"info\", \"warning\"].indexOf(logLevel) >= 0 && level === \"warning\" || [\"info\", \"warning\", \"error\"].indexOf(logLevel) >= 0 && level === \"error\";\n  return shouldLog;\n}\n\n/**\n * @param {(msg?: string) => void} logFn log function\n * @returns {(level: LogLevel, msg?: string) => void} function that logs when log level is sufficient\n */\nfunction logGroup(logFn) {\n  return function (level, msg) {\n    if (shouldLog(level)) {\n      logFn(msg);\n    }\n  };\n}\n\n/**\n * @param {LogLevel} level log level\n * @param {string|Error} msg message\n */\nmodule.exports = function (level, msg) {\n  if (shouldLog(level)) {\n    if (level === \"info\") {\n      console.log(msg);\n    } else if (level === \"warning\") {\n      console.warn(msg);\n    } else if (level === \"error\") {\n      console.error(msg);\n    }\n  }\n};\nvar group = console.group || dummy;\nvar groupCollapsed = console.groupCollapsed || dummy;\nvar groupEnd = console.groupEnd || dummy;\nmodule.exports.group = logGroup(group);\nmodule.exports.groupCollapsed = logGroup(groupCollapsed);\nmodule.exports.groupEnd = logGroup(groupEnd);\n\n/**\n * @param {LogLevel} level log level\n */\nmodule.exports.setLogLevel = function (level) {\n  logLevel = level;\n};\n\n/**\n * @param {Error} err error\n * @returns {string} formatted error\n */\nmodule.exports.formatError = function (err) {\n  var message = err.message;\n  var stack = err.stack;\n  if (!stack) {\n    return message;\n  } else if (stack.indexOf(message) < 0) {\n    return message + \"\\n\" + stack;\n  }\n  return stack;\n};", "map": {"version": 3, "names": ["logLevel", "dummy", "shouldLog", "level", "indexOf", "logGroup", "logFn", "msg", "module", "exports", "console", "log", "warn", "error", "group", "groupCollapsed", "groupEnd", "setLogLevel", "formatError", "err", "message", "stack"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/webpack/hot/log.js"], "sourcesContent": ["/** @typedef {\"info\" | \"warning\" | \"error\"} LogLevel */\n\n/** @type {LogLevel} */\nvar logLevel = \"info\";\n\nfunction dummy() {}\n\n/**\n * @param {LogLevel} level log level\n * @returns {boolean} true, if should log\n */\nfunction shouldLog(level) {\n\tvar shouldLog =\n\t\t(logLevel === \"info\" && level === \"info\") ||\n\t\t([\"info\", \"warning\"].indexOf(logLevel) >= 0 && level === \"warning\") ||\n\t\t([\"info\", \"warning\", \"error\"].indexOf(logLevel) >= 0 && level === \"error\");\n\treturn shouldLog;\n}\n\n/**\n * @param {(msg?: string) => void} logFn log function\n * @returns {(level: LogLevel, msg?: string) => void} function that logs when log level is sufficient\n */\nfunction logGroup(logFn) {\n\treturn function (level, msg) {\n\t\tif (shouldLog(level)) {\n\t\t\tlogFn(msg);\n\t\t}\n\t};\n}\n\n/**\n * @param {LogLevel} level log level\n * @param {string|Error} msg message\n */\nmodule.exports = function (level, msg) {\n\tif (shouldLog(level)) {\n\t\tif (level === \"info\") {\n\t\t\tconsole.log(msg);\n\t\t} else if (level === \"warning\") {\n\t\t\tconsole.warn(msg);\n\t\t} else if (level === \"error\") {\n\t\t\tconsole.error(msg);\n\t\t}\n\t}\n};\n\nvar group = console.group || dummy;\nvar groupCollapsed = console.groupCollapsed || dummy;\nvar groupEnd = console.groupEnd || dummy;\n\nmodule.exports.group = logGroup(group);\n\nmodule.exports.groupCollapsed = logGroup(groupCollapsed);\n\nmodule.exports.groupEnd = logGroup(groupEnd);\n\n/**\n * @param {LogLevel} level log level\n */\nmodule.exports.setLogLevel = function (level) {\n\tlogLevel = level;\n};\n\n/**\n * @param {Error} err error\n * @returns {string} formatted error\n */\nmodule.exports.formatError = function (err) {\n\tvar message = err.message;\n\tvar stack = err.stack;\n\tif (!stack) {\n\t\treturn message;\n\t} else if (stack.indexOf(message) < 0) {\n\t\treturn message + \"\\n\" + stack;\n\t}\n\treturn stack;\n};\n"], "mappings": "AAAA;;AAEA;AACA,IAAIA,QAAQ,GAAG,MAAM;AAErB,SAASC,KAAKA,CAAA,EAAG,CAAC;;AAElB;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACzB,IAAID,SAAS,GACXF,QAAQ,KAAK,MAAM,IAAIG,KAAK,KAAK,MAAM,IACvC,CAAC,MAAM,EAAE,SAAS,CAAC,CAACC,OAAO,CAACJ,QAAQ,CAAC,IAAI,CAAC,IAAIG,KAAK,KAAK,SAAU,IAClE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAACC,OAAO,CAACJ,QAAQ,CAAC,IAAI,CAAC,IAAIG,KAAK,KAAK,OAAQ;EAC3E,OAAOD,SAAS;AACjB;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACxB,OAAO,UAAUH,KAAK,EAAEI,GAAG,EAAE;IAC5B,IAAIL,SAAS,CAACC,KAAK,CAAC,EAAE;MACrBG,KAAK,CAACC,GAAG,CAAC;IACX;EACD,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUN,KAAK,EAAEI,GAAG,EAAE;EACtC,IAAIL,SAAS,CAACC,KAAK,CAAC,EAAE;IACrB,IAAIA,KAAK,KAAK,MAAM,EAAE;MACrBO,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;IACjB,CAAC,MAAM,IAAIJ,KAAK,KAAK,SAAS,EAAE;MAC/BO,OAAO,CAACE,IAAI,CAACL,GAAG,CAAC;IAClB,CAAC,MAAM,IAAIJ,KAAK,KAAK,OAAO,EAAE;MAC7BO,OAAO,CAACG,KAAK,CAACN,GAAG,CAAC;IACnB;EACD;AACD,CAAC;AAED,IAAIO,KAAK,GAAGJ,OAAO,CAACI,KAAK,IAAIb,KAAK;AAClC,IAAIc,cAAc,GAAGL,OAAO,CAACK,cAAc,IAAId,KAAK;AACpD,IAAIe,QAAQ,GAAGN,OAAO,CAACM,QAAQ,IAAIf,KAAK;AAExCO,MAAM,CAACC,OAAO,CAACK,KAAK,GAAGT,QAAQ,CAACS,KAAK,CAAC;AAEtCN,MAAM,CAACC,OAAO,CAACM,cAAc,GAAGV,QAAQ,CAACU,cAAc,CAAC;AAExDP,MAAM,CAACC,OAAO,CAACO,QAAQ,GAAGX,QAAQ,CAACW,QAAQ,CAAC;;AAE5C;AACA;AACA;AACAR,MAAM,CAACC,OAAO,CAACQ,WAAW,GAAG,UAAUd,KAAK,EAAE;EAC7CH,QAAQ,GAAGG,KAAK;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACAK,MAAM,CAACC,OAAO,CAACS,WAAW,GAAG,UAAUC,GAAG,EAAE;EAC3C,IAAIC,OAAO,GAAGD,GAAG,CAACC,OAAO;EACzB,IAAIC,KAAK,GAAGF,GAAG,CAACE,KAAK;EACrB,IAAI,CAACA,KAAK,EAAE;IACX,OAAOD,OAAO;EACf,CAAC,MAAM,IAAIC,KAAK,CAACjB,OAAO,CAACgB,OAAO,CAAC,GAAG,CAAC,EAAE;IACtC,OAAOA,OAAO,GAAG,IAAI,GAAGC,KAAK;EAC9B;EACA,OAAOA,KAAK;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}