{"ast": null, "code": "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return x & y ^ x & z ^ y & z;\n    case 3:\n      return x ^ y ^ z;\n  }\n}\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n    for (var i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n  for (var _i = 0; _i < N; ++_i) {\n    var arr = new Uint32Array(16);\n    for (var j = 0; j < 16; ++j) {\n      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n    M[_i] = arr;\n  }\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n  for (var _i2 = 0; _i2 < N; ++_i2) {\n    var W = new Uint32Array(80);\n    for (var t = 0; t < 16; ++t) {\n      W[t] = M[_i2][t];\n    }\n    for (var _t = 16; _t < 80; ++_t) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n    for (var _t2 = 0; _t2 < 80; ++_t2) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\nexport default sha1;", "map": {"version": 3, "names": ["f", "s", "x", "y", "z", "ROTL", "n", "sha1", "bytes", "K", "H", "msg", "unescape", "encodeURIComponent", "i", "length", "push", "charCodeAt", "Array", "isArray", "prototype", "slice", "call", "l", "N", "Math", "ceil", "M", "_i", "arr", "Uint32Array", "j", "pow", "floor", "_i2", "W", "t", "_t", "a", "b", "c", "d", "e", "_t2", "T"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/uuid/dist/esm-browser/sha1.js"], "sourcesContent": ["// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (var i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n\n  for (var _i = 0; _i < N; ++_i) {\n    var arr = new Uint32Array(16);\n\n    for (var j = 0; j < 16; ++j) {\n      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n\n    M[_i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (var _i2 = 0; _i2 < N; ++_i2) {\n    var W = new Uint32Array(80);\n\n    for (var t = 0; t < 16; ++t) {\n      W[t] = M[_i2][t];\n    }\n\n    for (var _t = 16; _t < 80; ++_t) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n\n    for (var _t2 = 0; _t2 < 80; ++_t2) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;"], "mappings": "AAAA;AACA;AACA,SAASA,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrB,QAAQH,CAAC;IACP,KAAK,CAAC;MACJ,OAAOC,CAAC,GAAGC,CAAC,GAAG,CAACD,CAAC,GAAGE,CAAC;IAEvB,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;IAElB,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGE,CAAC,GAAGD,CAAC,GAAGC,CAAC;IAE9B,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;EACpB;AACF;AAEA,SAASC,IAAIA,CAACH,CAAC,EAAEI,CAAC,EAAE;EAClB,OAAOJ,CAAC,IAAII,CAAC,GAAGJ,CAAC,KAAK,EAAE,GAAGI,CAAC;AAC9B;AAEA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACxD,IAAIC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAEpE,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIG,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE/CA,KAAK,GAAG,EAAE;IAEV,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAE,EAAED,CAAC,EAAE;MACnCN,KAAK,CAACQ,IAAI,CAACL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,MAAM,IAAI,CAACI,KAAK,CAACC,OAAO,CAACX,KAAK,CAAC,EAAE;IAChC;IACAA,KAAK,GAAGU,KAAK,CAACE,SAAS,CAACC,KAAK,CAACC,IAAI,CAACd,KAAK,CAAC;EAC3C;EAEAA,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;EAChB,IAAIO,CAAC,GAAGf,KAAK,CAACO,MAAM,GAAG,CAAC,GAAG,CAAC;EAC5B,IAAIS,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACH,CAAC,GAAG,EAAE,CAAC;EACzB,IAAII,CAAC,GAAG,IAAIT,KAAK,CAACM,CAAC,CAAC;EAEpB,KAAK,IAAII,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGJ,CAAC,EAAE,EAAEI,EAAE,EAAE;IAC7B,IAAIC,GAAG,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;IAE7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BF,GAAG,CAACE,CAAC,CAAC,GAAGvB,KAAK,CAACoB,EAAE,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAGvB,KAAK,CAACoB,EAAE,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAGvB,KAAK,CAACoB,EAAE,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGvB,KAAK,CAACoB,EAAE,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzI;IAEAJ,CAAC,CAACC,EAAE,CAAC,GAAGC,GAAG;EACb;EAEAF,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAChB,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAGU,IAAI,CAACO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EACvDL,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACQ,KAAK,CAACN,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvCG,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAChB,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU;EAElD,KAAK,IAAImB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGV,CAAC,EAAE,EAAEU,GAAG,EAAE;IAChC,IAAIC,CAAC,GAAG,IAAIL,WAAW,CAAC,EAAE,CAAC;IAE3B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BD,CAAC,CAACC,CAAC,CAAC,GAAGT,CAAC,CAACO,GAAG,CAAC,CAACE,CAAC,CAAC;IAClB;IAEA,KAAK,IAAIC,EAAE,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EAAE;MAC/BF,CAAC,CAACE,EAAE,CAAC,GAAGhC,IAAI,CAAC8B,CAAC,CAACE,EAAE,GAAG,CAAC,CAAC,GAAGF,CAAC,CAACE,EAAE,GAAG,CAAC,CAAC,GAAGF,CAAC,CAACE,EAAE,GAAG,EAAE,CAAC,GAAGF,CAAC,CAACE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAClE;IAEA,IAAIC,CAAC,GAAG5B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI6B,CAAC,GAAG7B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI8B,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI+B,CAAC,GAAG/B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIgC,CAAC,GAAGhC,CAAC,CAAC,CAAC,CAAC;IAEZ,KAAK,IAAIiC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAE,EAAEA,GAAG,EAAE;MACjC,IAAI1C,CAAC,GAAGwB,IAAI,CAACQ,KAAK,CAACU,GAAG,GAAG,EAAE,CAAC;MAC5B,IAAIC,CAAC,GAAGvC,IAAI,CAACiC,CAAC,EAAE,CAAC,CAAC,GAAGtC,CAAC,CAACC,CAAC,EAAEsC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGC,CAAC,GAAGjC,CAAC,CAACR,CAAC,CAAC,GAAGkC,CAAC,CAACQ,GAAG,CAAC,KAAK,CAAC;MAC5DD,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGnC,IAAI,CAACkC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MACrBA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGM,CAAC;IACP;IAEAlC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG4B,CAAC,KAAK,CAAC;IACrB5B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG6B,CAAC,KAAK,CAAC;IACrB7B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,KAAK,CAAC;IACrB9B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG+B,CAAC,KAAK,CAAC;IACrB/B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGgC,CAAC,KAAK,CAAC;EACvB;EAEA,OAAO,CAAChC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAClW;AAEA,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}