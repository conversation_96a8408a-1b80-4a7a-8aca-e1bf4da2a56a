{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { consumerMarkDirty, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from '../signal.mjs';\nexport { SIGNAL_NODE, createComputed, createSignal, defaultEquals, getActiveConsumer, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn } from '../signal.mjs';\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked } from '../untracked.mjs';\nexport { setAlternateWeakRefImpl } from '../weak_ref.mjs';\nfunction createWatch(fn, schedule, allowSignalWrites) {\n  const node = Object.create(WATCH_NODE);\n  if (allowSignalWrites) {\n    node.consumerAllowSignalWrites = true;\n  }\n  node.fn = fn;\n  node.schedule = schedule;\n  const registerOnCleanup = cleanupFn => {\n    node.cleanupFn = cleanupFn;\n  };\n  function isWatchNodeDestroyed(node) {\n    return node.fn === null && node.schedule === null;\n  }\n  function destroyWatchNode(node) {\n    if (!isWatchNodeDestroyed(node)) {\n      consumerDestroy(node); // disconnect watcher from the reactive graph\n      node.cleanupFn();\n      // nullify references to the integration functions to mark node as destroyed\n      node.fn = null;\n      node.schedule = null;\n      node.cleanupFn = NOOP_CLEANUP_FN;\n    }\n  }\n  const run = () => {\n    if (node.fn === null) {\n      // trying to run a destroyed watch is noop\n      return;\n    }\n    if (isInNotificationPhase()) {\n      throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Schedulers cannot synchronously execute watches while scheduling.' : '');\n    }\n    node.dirty = false;\n    if (node.hasRun && !consumerPollProducersForChange(node)) {\n      return;\n    }\n    node.hasRun = true;\n    const prevConsumer = consumerBeforeComputation(node);\n    try {\n      node.cleanupFn();\n      node.cleanupFn = NOOP_CLEANUP_FN;\n      node.fn(registerOnCleanup);\n    } finally {\n      consumerAfterComputation(node, prevConsumer);\n    }\n  };\n  node.ref = {\n    notify: () => consumerMarkDirty(node),\n    run,\n    cleanup: () => node.cleanupFn(),\n    destroy: () => destroyWatchNode(node),\n    [SIGNAL]: node\n  };\n  return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => {};\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: false,\n    consumerMarkedDirty: node => {\n      if (node.schedule !== null) {\n        node.schedule(node.ref);\n      }\n    },\n    hasRun: false,\n    cleanupFn: NOOP_CLEANUP_FN\n  };\n})();\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createWatch, isInNotificationPhase };", "map": {"version": 3, "names": ["consumerMarkDirty", "SIGNAL", "REACTIVE_NODE", "consumerDestroy", "isInNotificationPhase", "consumerPollProducersForChange", "consumerBeforeComputation", "consumerAfterComputation", "SIGNAL_NODE", "createComputed", "createSignal", "defaultEquals", "getActiveConsumer", "isReactive", "producerAccessed", "producerIncrementEpoch", "producerMark<PERSON><PERSON>", "producerNotifyConsumers", "producerUpdateValueVersion", "producer<PERSON><PERSON>datesAllowed", "runPostProducerCreatedFn", "runPostSignalSetFn", "setActiveConsumer", "setPostProducerCreatedFn", "setPostSignalSetFn", "setThrowInvalidWriteToSignalError", "signalGetFn", "signalSetFn", "signalUpdateFn", "createLinkedSignal", "linkedSignalSetFn", "linkedSignalUpdateFn", "untracked", "setAlternateWeakRefImpl", "createWatch", "fn", "schedule", "allowSignalWrites", "node", "Object", "create", "WATCH_NODE", "consumerAllowSignalWrites", "registerOnCleanup", "cleanupFn", "isWatchNodeDestroyed", "destroyWatchNode", "NOOP_CLEANUP_FN", "run", "Error", "ngDevMode", "dirty", "<PERSON><PERSON>un", "prevConsumer", "ref", "notify", "cleanup", "destroy", "consumerIsAlwaysLive", "consumerMarkedDirty"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/core/fesm2022/primitives/signals.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { consumerMarkDirty, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from '../signal.mjs';\nexport { SIGNAL_NODE, createComputed, createSignal, defaultEquals, getActiveConsumer, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn } from '../signal.mjs';\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked } from '../untracked.mjs';\nexport { setAlternateWeakRefImpl } from '../weak_ref.mjs';\n\nfunction createWatch(fn, schedule, allowSignalWrites) {\n    const node = Object.create(WATCH_NODE);\n    if (allowSignalWrites) {\n        node.consumerAllowSignalWrites = true;\n    }\n    node.fn = fn;\n    node.schedule = schedule;\n    const registerOnCleanup = (cleanupFn) => {\n        node.cleanupFn = cleanupFn;\n    };\n    function isWatchNodeDestroyed(node) {\n        return node.fn === null && node.schedule === null;\n    }\n    function destroyWatchNode(node) {\n        if (!isWatchNodeDestroyed(node)) {\n            consumerDestroy(node); // disconnect watcher from the reactive graph\n            node.cleanupFn();\n            // nullify references to the integration functions to mark node as destroyed\n            node.fn = null;\n            node.schedule = null;\n            node.cleanupFn = NOOP_CLEANUP_FN;\n        }\n    }\n    const run = () => {\n        if (node.fn === null) {\n            // trying to run a destroyed watch is noop\n            return;\n        }\n        if (isInNotificationPhase()) {\n            throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode\n                ? 'Schedulers cannot synchronously execute watches while scheduling.'\n                : '');\n        }\n        node.dirty = false;\n        if (node.hasRun && !consumerPollProducersForChange(node)) {\n            return;\n        }\n        node.hasRun = true;\n        const prevConsumer = consumerBeforeComputation(node);\n        try {\n            node.cleanupFn();\n            node.cleanupFn = NOOP_CLEANUP_FN;\n            node.fn(registerOnCleanup);\n        }\n        finally {\n            consumerAfterComputation(node, prevConsumer);\n        }\n    };\n    node.ref = {\n        notify: () => consumerMarkDirty(node),\n        run,\n        cleanup: () => node.cleanupFn(),\n        destroy: () => destroyWatchNode(node),\n        [SIGNAL]: node,\n    };\n    return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => { };\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        consumerIsAlwaysLive: true,\n        consumerAllowSignalWrites: false,\n        consumerMarkedDirty: (node) => {\n            if (node.schedule !== null) {\n                node.schedule(node.ref);\n            }\n        },\n        hasRun: false,\n        cleanupFn: NOOP_CLEANUP_FN,\n    };\n})();\n\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createWatch, isInNotificationPhase };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,iBAAiB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,8BAA8B,EAAEC,yBAAyB,EAAEC,wBAAwB,QAAQ,eAAe;AACrM,SAASC,WAAW,EAAEC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,iCAAiC,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,QAAQ,eAAe;AAC7b,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,kBAAkB;AACzG,SAASC,uBAAuB,QAAQ,iBAAiB;AAEzD,SAASC,WAAWA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EAClD,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAACC,UAAU,CAAC;EACtC,IAAIJ,iBAAiB,EAAE;IACnBC,IAAI,CAACI,yBAAyB,GAAG,IAAI;EACzC;EACAJ,IAAI,CAACH,EAAE,GAAGA,EAAE;EACZG,IAAI,CAACF,QAAQ,GAAGA,QAAQ;EACxB,MAAMO,iBAAiB,GAAIC,SAAS,IAAK;IACrCN,IAAI,CAACM,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD,SAASC,oBAAoBA,CAACP,IAAI,EAAE;IAChC,OAAOA,IAAI,CAACH,EAAE,KAAK,IAAI,IAAIG,IAAI,CAACF,QAAQ,KAAK,IAAI;EACrD;EACA,SAASU,gBAAgBA,CAACR,IAAI,EAAE;IAC5B,IAAI,CAACO,oBAAoB,CAACP,IAAI,CAAC,EAAE;MAC7BnC,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC;MACvBA,IAAI,CAACM,SAAS,CAAC,CAAC;MAChB;MACAN,IAAI,CAACH,EAAE,GAAG,IAAI;MACdG,IAAI,CAACF,QAAQ,GAAG,IAAI;MACpBE,IAAI,CAACM,SAAS,GAAGG,eAAe;IACpC;EACJ;EACA,MAAMC,GAAG,GAAGA,CAAA,KAAM;IACd,IAAIV,IAAI,CAACH,EAAE,KAAK,IAAI,EAAE;MAClB;MACA;IACJ;IACA,IAAI/B,qBAAqB,CAAC,CAAC,EAAE;MACzB,MAAM,IAAI6C,KAAK,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvD,mEAAmE,GACnE,EAAE,CAAC;IACb;IACAZ,IAAI,CAACa,KAAK,GAAG,KAAK;IAClB,IAAIb,IAAI,CAACc,MAAM,IAAI,CAAC/C,8BAA8B,CAACiC,IAAI,CAAC,EAAE;MACtD;IACJ;IACAA,IAAI,CAACc,MAAM,GAAG,IAAI;IAClB,MAAMC,YAAY,GAAG/C,yBAAyB,CAACgC,IAAI,CAAC;IACpD,IAAI;MACAA,IAAI,CAACM,SAAS,CAAC,CAAC;MAChBN,IAAI,CAACM,SAAS,GAAGG,eAAe;MAChCT,IAAI,CAACH,EAAE,CAACQ,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJpC,wBAAwB,CAAC+B,IAAI,EAAEe,YAAY,CAAC;IAChD;EACJ,CAAC;EACDf,IAAI,CAACgB,GAAG,GAAG;IACPC,MAAM,EAAEA,CAAA,KAAMvD,iBAAiB,CAACsC,IAAI,CAAC;IACrCU,GAAG;IACHQ,OAAO,EAAEA,CAAA,KAAMlB,IAAI,CAACM,SAAS,CAAC,CAAC;IAC/Ba,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACR,IAAI,CAAC;IACrC,CAACrC,MAAM,GAAGqC;EACd,CAAC;EACD,OAAOA,IAAI,CAACgB,GAAG;AACnB;AACA,MAAMP,eAAe,GAAGA,CAAA,KAAM,CAAE,CAAC;AACjC;AACA;AACA;AACA,MAAMN,UAAU,GAAG,eAAgB,CAAC,MAAM;EACtC,OAAO;IACH,GAAGvC,aAAa;IAChBwD,oBAAoB,EAAE,IAAI;IAC1BhB,yBAAyB,EAAE,KAAK;IAChCiB,mBAAmB,EAAGrB,IAAI,IAAK;MAC3B,IAAIA,IAAI,CAACF,QAAQ,KAAK,IAAI,EAAE;QACxBE,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACgB,GAAG,CAAC;MAC3B;IACJ,CAAC;IACDF,MAAM,EAAE,KAAK;IACbR,SAAS,EAAEG;EACf,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAAS7C,aAAa,EAAED,MAAM,EAAEM,wBAAwB,EAAED,yBAAyB,EAAEH,eAAe,EAAEH,iBAAiB,EAAEK,8BAA8B,EAAE6B,WAAW,EAAE9B,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}