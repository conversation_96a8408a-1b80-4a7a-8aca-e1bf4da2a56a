{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: RouteReuseStrategy,\n      useClass: IonicRouteStrategy\n    }],\n    imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, i1.IonicModule, AppRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "RouteReuseStrategy", "IonicModule", "IonicRouteStrategy", "AppComponent", "AppRoutingModule", "AppModule", "_", "_2", "bootstrap", "_3", "provide", "useClass", "imports", "forRoot", "declarations", "i1"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\n\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\n\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule],\n  providers: [{ provide: RouteReuseStrategy, useClass: IonicRouteStrategy }],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AAEpD,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,gBAAgB;AAEhE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;;;AAQvD,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qCAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAFRL,YAAY;EAAA;EAAA,QAAAM,EAAA,G;eADb,CAAC;MAAEC,OAAO,EAAEV,kBAAkB;MAAEW,QAAQ,EAAET;IAAkB,CAAE,CAAC;IAAAU,OAAA,GADhEb,aAAa,EAAEE,WAAW,CAACY,OAAO,EAAE,EAAET,gBAAgB;EAAA;;;2EAIrDC,SAAS;IAAAS,YAAA,GALLX,YAAY;IAAAS,OAAA,GACjBb,aAAa,EAAAgB,EAAA,CAAAd,WAAA,EAAyBG,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}