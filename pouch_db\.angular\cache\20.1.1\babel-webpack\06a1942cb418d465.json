{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { FormsModule } from '@angular/forms';\nimport { HomePage } from './home.page';\nimport { HomePageRoutingModule } from './home-routing.module';\nlet HomePageModule = class HomePageModule {};\nHomePageModule = __decorate([NgModule({\n  imports: [CommonModule, FormsModule, IonicModule, HomePageRoutingModule],\n  declarations: [HomePage]\n})], HomePageModule);\nexport { HomePageModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IonicModule", "FormsModule", "HomePage", "HomePageRoutingModule", "HomePageModule", "__decorate", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { FormsModule } from '@angular/forms';\nimport { HomePage } from './home.page';\n\nimport { HomePageRoutingModule } from './home-routing.module';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    IonicModule,\n    HomePageRoutingModule\n  ],\n  declarations: [HomePage]\n})\nexport class HomePageModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,aAAa;AAEtC,SAASC,qBAAqB,QAAQ,uBAAuB;AAYtD,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAAG;AAAjBA,cAAc,GAAAC,UAAA,EAT1BP,QAAQ,CAAC;EACRQ,OAAO,EAAE,CACPP,YAAY,EACZE,WAAW,EACXD,WAAW,EACXG,qBAAqB,CACtB;EACDI,YAAY,EAAE,CAACL,QAAQ;CACxB,CAAC,C,EACWE,cAAc,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}