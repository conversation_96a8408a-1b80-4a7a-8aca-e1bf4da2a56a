{"ast": null, "code": "/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction setAlternateWeakRefImpl(impl) {\n  // TODO: remove this function\n}\nexport { setAlternateWeakRefImpl };", "map": {"version": 3, "names": ["setAlternateWeakRefImpl", "impl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@angular/core/fesm2022/weak_ref.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction setAlternateWeakRefImpl(impl) {\n    // TODO: remove this function\n}\n\nexport { setAlternateWeakRefImpl };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EACnC;AAAA;AAGJ,SAASD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}