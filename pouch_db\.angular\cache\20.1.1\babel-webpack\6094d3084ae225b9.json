{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\nconst DURATION = 540;\n// TODO(FW-2832): types\nconst getClonedElement = tagName => {\n  return document.querySelector(`${tagName}.ion-cloned-element`);\n};\nconst shadow = el => {\n  return el.shadowRoot || el;\n};\nconst getLargeTitle = refEl => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  const query = 'ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large';\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    return activeTab != null ? activeTab.querySelector(query) : null;\n  }\n  return refEl.querySelector(query);\n};\nconst getBackButton = (refEl, backDirection) => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  let buttonsList = [];\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    if (activeTab != null) {\n      buttonsList = activeTab.querySelectorAll('ion-buttons');\n    }\n  } else {\n    buttonsList = refEl.querySelectorAll('ion-buttons');\n  }\n  for (const buttons of buttonsList) {\n    const parentHeader = buttons.closest('ion-header');\n    const activeHeader = parentHeader && !parentHeader.classList.contains('header-collapse-condense-inactive');\n    const backButton = buttons.querySelector('ion-back-button');\n    const buttonsCollapse = buttons.classList.contains('buttons-collapse');\n    const startSlot = buttons.slot === 'start' || buttons.slot === '';\n    if (backButton !== null && startSlot && (buttonsCollapse && activeHeader && backDirection || !buttonsCollapse)) {\n      return backButton;\n    }\n  }\n  return null;\n};\nconst createLargeTitleTransition = (rootAnimation, rtl, backDirection, enteringEl, leavingEl) => {\n  const enteringBackButton = getBackButton(enteringEl, backDirection);\n  const leavingLargeTitle = getLargeTitle(leavingEl);\n  const enteringLargeTitle = getLargeTitle(enteringEl);\n  const leavingBackButton = getBackButton(leavingEl, backDirection);\n  const shouldAnimationForward = enteringBackButton !== null && leavingLargeTitle !== null && !backDirection;\n  const shouldAnimationBackward = enteringLargeTitle !== null && leavingBackButton !== null && backDirection;\n  if (shouldAnimationForward) {\n    const leavingLargeTitleBox = leavingLargeTitle.getBoundingClientRect();\n    const enteringBackButtonBox = enteringBackButton.getBoundingClientRect();\n    const enteringBackButtonTextEl = shadow(enteringBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const enteringBackButtonTextBox = enteringBackButtonTextEl === null || enteringBackButtonTextEl === void 0 ? void 0 : enteringBackButtonTextEl.getBoundingClientRect();\n    const leavingLargeTitleTextEl = shadow(leavingLargeTitle).querySelector('.toolbar-title');\n    const leavingLargeTitleTextBox = leavingLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, leavingLargeTitle, leavingLargeTitleBox, leavingLargeTitleTextBox, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, enteringBackButton, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox, leavingLargeTitle, leavingLargeTitleTextBox);\n  } else if (shouldAnimationBackward) {\n    const enteringLargeTitleBox = enteringLargeTitle.getBoundingClientRect();\n    const leavingBackButtonBox = leavingBackButton.getBoundingClientRect();\n    const leavingBackButtonTextEl = shadow(leavingBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const leavingBackButtonTextBox = leavingBackButtonTextEl === null || leavingBackButtonTextEl === void 0 ? void 0 : leavingBackButtonTextEl.getBoundingClientRect();\n    const enteringLargeTitleTextEl = shadow(enteringLargeTitle).querySelector('.toolbar-title');\n    const enteringLargeTitleTextBox = enteringLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, enteringLargeTitle, enteringLargeTitleBox, enteringLargeTitleTextBox, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, leavingBackButton, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox, enteringLargeTitle, enteringLargeTitleTextBox);\n  }\n  return {\n    forward: shouldAnimationForward,\n    backward: shouldAnimationBackward\n  };\n};\nconst animateBackButton = (rootAnimation, rtl, backDirection, backButtonEl, backButtonBox, backButtonTextEl, backButtonTextBox, largeTitleEl, largeTitleTextBox) => {\n  var _a, _b;\n  const BACK_BUTTON_START_OFFSET = rtl ? `calc(100% - ${backButtonBox.right + 4}px)` : `${backButtonBox.left - 4}px`;\n  const TEXT_ORIGIN_X = rtl ? 'right' : 'left';\n  const ICON_ORIGIN_X = rtl ? 'left' : 'right';\n  const CONTAINER_ORIGIN_X = rtl ? 'right' : 'left';\n  let WIDTH_SCALE = 1;\n  let HEIGHT_SCALE = 1;\n  let TEXT_START_SCALE = `scale(${HEIGHT_SCALE})`;\n  const TEXT_END_SCALE = 'scale(1)';\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the back button text scale\n     * adjusts to not perfectly match the large title text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    WIDTH_SCALE = largeTitleTextBox.width / backButtonTextBox.width;\n    /**\n     * Subtract an offset to account for slight sizing/padding differences between the\n     * title and the back button.\n     */\n    HEIGHT_SCALE = (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET) / backButtonTextBox.height;\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    TEXT_START_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  const backButtonIconEl = shadow(backButtonEl).querySelector('ion-icon');\n  const backButtonIconBox = backButtonIconEl.getBoundingClientRect();\n  /**\n   * We need to offset the container by the icon dimensions\n   * so that the back button text aligns with the large title\n   * text. Otherwise, the back button icon will align with the\n   * large title text but the back button text will not.\n   */\n  const CONTAINER_START_TRANSLATE_X = rtl ? `${backButtonIconBox.width / 2 - (backButtonIconBox.right - backButtonBox.right)}px` : `${backButtonBox.left - backButtonIconBox.width / 2}px`;\n  const CONTAINER_END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right}px` : `${backButtonBox.left}px`;\n  /**\n   * Back button container should be\n   * aligned to the top of the title container\n   * so the texts overlap as the back button\n   * text begins to fade in.\n   */\n  const CONTAINER_START_TRANSLATE_Y = `${largeTitleTextBox.top}px`;\n  /**\n   * The cloned back button should align exactly with the\n   * real back button on the entering page otherwise there will\n   * be a layout shift.\n   */\n  const CONTAINER_END_TRANSLATE_Y = `${backButtonBox.top}px`;\n  /**\n   * In the forward direction, the cloned back button\n   * container should translate from over the large title\n   * to over the back button. In the backward direction,\n   * it should translate from over the back button to over\n   * the large title.\n   */\n  const FORWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }];\n  const BACKWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }];\n  const CONTAINER_KEYFRAMES = backDirection ? BACKWARD_CONTAINER_KEYFRAMES : FORWARD_CONTAINER_KEYFRAMES;\n  /**\n   * In the forward direction, the text in the cloned back button\n   * should start to be (roughly) the size of the large title\n   * and then scale down to be the size of the actual back button.\n   * The text should also translate, but that translate is handled\n   * by the container keyframes.\n   */\n  const FORWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }];\n  const BACKWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }];\n  const TEXT_KEYFRAMES = backDirection ? BACKWARD_TEXT_KEYFRAMES : FORWARD_TEXT_KEYFRAMES;\n  /**\n   * The icon should scale in/out in the second\n   * half of the animation. The icon should also\n   * translate, but that translate is handled by the\n   * container keyframes.\n   */\n  const FORWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 0.6,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }];\n  const BACKWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: 'scale(1)'\n  }, {\n    offset: 0.2,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }];\n  const ICON_KEYFRAMES = backDirection ? BACKWARD_ICON_KEYFRAMES : FORWARD_ICON_KEYFRAMES;\n  const enteringBackButtonTextAnimation = createAnimation();\n  const enteringBackButtonIconAnimation = createAnimation();\n  const enteringBackButtonAnimation = createAnimation();\n  const clonedBackButtonEl = getClonedElement('ion-back-button');\n  const clonedBackButtonTextEl = shadow(clonedBackButtonEl).querySelector('.button-text');\n  const clonedBackButtonIconEl = shadow(clonedBackButtonEl).querySelector('ion-icon');\n  clonedBackButtonEl.text = backButtonEl.text;\n  clonedBackButtonEl.mode = backButtonEl.mode;\n  clonedBackButtonEl.icon = backButtonEl.icon;\n  clonedBackButtonEl.color = backButtonEl.color;\n  clonedBackButtonEl.disabled = backButtonEl.disabled;\n  clonedBackButtonEl.style.setProperty('display', 'block');\n  clonedBackButtonEl.style.setProperty('position', 'fixed');\n  enteringBackButtonIconAnimation.addElement(clonedBackButtonIconEl);\n  enteringBackButtonTextAnimation.addElement(clonedBackButtonTextEl);\n  enteringBackButtonAnimation.addElement(clonedBackButtonEl);\n  enteringBackButtonAnimation.beforeStyles({\n    position: 'absolute',\n    top: '0px',\n    [CONTAINER_ORIGIN_X]: '0px'\n  })\n  /**\n   * The write hooks must be set on this animation as it is guaranteed to run. Other\n   * animations such as the back button text animation will not run if the back button\n   * has no visible text.\n   */.beforeAddWrite(() => {\n    backButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.setProperty(TEXT_ORIGIN_X, BACK_BUTTON_START_OFFSET);\n  }).afterAddWrite(() => {\n    backButtonEl.style.setProperty('display', '');\n    clonedBackButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.removeProperty(TEXT_ORIGIN_X);\n  }).keyframes(CONTAINER_KEYFRAMES);\n  enteringBackButtonTextAnimation.beforeStyles({\n    'transform-origin': `${TEXT_ORIGIN_X} top`\n  }).keyframes(TEXT_KEYFRAMES);\n  enteringBackButtonIconAnimation.beforeStyles({\n    'transform-origin': `${ICON_ORIGIN_X} center`\n  }).keyframes(ICON_KEYFRAMES);\n  rootAnimation.addAnimation([enteringBackButtonTextAnimation, enteringBackButtonIconAnimation, enteringBackButtonAnimation]);\n};\nconst animateLargeTitle = (rootAnimation, rtl, backDirection, largeTitleEl, largeTitleBox, largeTitleTextBox, backButtonBox, backButtonTextEl, backButtonTextBox) => {\n  var _a, _b;\n  /**\n   * The horizontal transform origin for the large title\n   */\n  const ORIGIN_X = rtl ? 'right' : 'left';\n  const TITLE_START_OFFSET = rtl ? `calc(100% - ${largeTitleBox.right}px)` : `${largeTitleBox.left}px`;\n  /**\n   * The cloned large should align exactly with the\n   * real large title on the leaving page otherwise there will\n   * be a layout shift.\n   */\n  const START_TRANSLATE_X = '0px';\n  const START_TRANSLATE_Y = `${largeTitleBox.top}px`;\n  /**\n   * How much to offset the large title translation by.\n   * This accounts for differences in sizing between the large\n   * title and the back button due to padding and font weight.\n   */\n  const LARGE_TITLE_TRANSLATION_OFFSET = 8;\n  let END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonBox.x + LARGE_TITLE_TRANSLATION_OFFSET}px`;\n  /**\n   * How much to scale the large title up/down by.\n   */\n  let HEIGHT_SCALE = 0.5;\n  /**\n   * The large title always starts full size.\n   */\n  const START_SCALE = 'scale(1)';\n  /**\n   * By default, we don't worry about having the large title scaled to perfectly\n   * match the back button because we don't know if the back button's text matches\n   * the large title's text.\n   */\n  let END_SCALE = `scale(${HEIGHT_SCALE})`;\n  // Text element not rendered if developers pass text=\"\" to the back button\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * The scaled title should (roughly) overlap the back button. This ensures that\n     * the back button and title overlap during the animation. Note that since both\n     * elements either fade in or fade out over the course of the animation, neither\n     * element will be fully visible on top of the other. As a result, the overlap\n     * does not need to be perfect, so approximate values are acceptable here.\n     */\n    END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonTextBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonTextBox.x - LARGE_TITLE_TRANSLATION_OFFSET}px`;\n    /**\n     * In the forward direction, the large title should start at its normal size and\n     * then scale down to be (roughly) the size of the back button on the other view.\n     * In the backward direction, the large title should start at (roughly) the size\n     * of the back button and then scale up to its original size.\n     * Note that since both elements either fade in or fade out over the course of the\n     * animation, neither element will be fully visible on top of the other. As a result,\n     * the overlap  does not need to be perfect, so approximate values are acceptable here.\n     */\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the large title text scale\n     * adjusts to not perfectly match the back button text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    const WIDTH_SCALE = backButtonTextBox.width / largeTitleTextBox.width;\n    HEIGHT_SCALE = backButtonTextBox.height / (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET);\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    END_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  /**\n   * The midpoints of the back button and the title should align such that the back\n   * button and title appear to be centered with each other.\n   */\n  const backButtonMidPoint = backButtonBox.top + backButtonBox.height / 2;\n  const titleMidPoint = largeTitleBox.height * HEIGHT_SCALE / 2;\n  const END_TRANSLATE_Y = `${backButtonMidPoint - titleMidPoint}px`;\n  const BACKWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }, {\n    offset: 0.1,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }];\n  const FORWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0.99,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }, {\n    offset: 0.6,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }];\n  const KEYFRAMES = backDirection ? BACKWARDS_KEYFRAMES : FORWARDS_KEYFRAMES;\n  const clonedTitleEl = getClonedElement('ion-title');\n  const clonedLargeTitleAnimation = createAnimation();\n  clonedTitleEl.innerText = largeTitleEl.innerText;\n  clonedTitleEl.size = largeTitleEl.size;\n  clonedTitleEl.color = largeTitleEl.color;\n  clonedLargeTitleAnimation.addElement(clonedTitleEl);\n  clonedLargeTitleAnimation.beforeStyles({\n    'transform-origin': `${ORIGIN_X} top`,\n    /**\n     * Since font size changes will cause\n     * the dimension of the large title to change\n     * we need to set the cloned title height\n     * equal to that of the original large title height.\n     */\n    height: `${largeTitleBox.height}px`,\n    display: '',\n    position: 'relative',\n    [ORIGIN_X]: TITLE_START_OFFSET\n  }).beforeAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '0');\n  }).afterAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '');\n    clonedTitleEl.style.setProperty('display', 'none');\n  }).keyframes(KEYFRAMES);\n  rootAnimation.addAnimation(clonedLargeTitleAnimation);\n};\nconst iosTransitionAnimation = (navEl, opts) => {\n  var _a;\n  try {\n    const EASING = 'cubic-bezier(0.32,0.72,0,1)';\n    const OPACITY = 'opacity';\n    const TRANSFORM = 'transform';\n    const CENTER = '0%';\n    const OFF_OPACITY = 0.8;\n    const isRTL = navEl.ownerDocument.dir === 'rtl';\n    const OFF_RIGHT = isRTL ? '-99.5%' : '99.5%';\n    const OFF_LEFT = isRTL ? '33%' : '-33%';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const backDirection = opts.direction === 'back';\n    const contentEl = enteringEl.querySelector(':scope > ion-content');\n    const headerEls = enteringEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n    const enteringToolBarEls = enteringEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n    const rootAnimation = createAnimation();\n    const enteringContentAnimation = createAnimation();\n    rootAnimation.addElement(enteringEl).duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || DURATION).easing(opts.easing || EASING).fill('both').beforeRemoveClass('ion-page-invisible');\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (leavingEl && navEl !== null && navEl !== undefined) {\n      const navDecorAnimation = createAnimation();\n      navDecorAnimation.addElement(navEl);\n      rootAnimation.addAnimation(navDecorAnimation);\n    }\n    if (!contentEl && enteringToolBarEls.length === 0 && headerEls.length === 0) {\n      enteringContentAnimation.addElement(enteringEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n    } else {\n      enteringContentAnimation.addElement(contentEl); // REVIEW\n      enteringContentAnimation.addElement(headerEls);\n    }\n    rootAnimation.addAnimation(enteringContentAnimation);\n    if (backDirection) {\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, OFF_OPACITY, 1);\n    } else {\n      // entering content, forward direction\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n    }\n    if (contentEl) {\n      const enteringTransitionEffectEl = shadow(contentEl).querySelector('.transition-effect');\n      if (enteringTransitionEffectEl) {\n        const enteringTransitionCoverEl = enteringTransitionEffectEl.querySelector('.transition-cover');\n        const enteringTransitionShadowEl = enteringTransitionEffectEl.querySelector('.transition-shadow');\n        const enteringTransitionEffect = createAnimation();\n        const enteringTransitionCover = createAnimation();\n        const enteringTransitionShadow = createAnimation();\n        enteringTransitionEffect.addElement(enteringTransitionEffectEl).beforeStyles({\n          opacity: '1',\n          display: 'block'\n        }).afterStyles({\n          opacity: '',\n          display: ''\n        });\n        enteringTransitionCover.addElement(enteringTransitionCoverEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0, 0.1);\n        enteringTransitionShadow.addElement(enteringTransitionShadowEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.03, 0.7);\n        enteringTransitionEffect.addAnimation([enteringTransitionCover, enteringTransitionShadow]);\n        enteringContentAnimation.addAnimation([enteringTransitionEffect]);\n      }\n    }\n    const enteringContentHasLargeTitle = enteringEl.querySelector('ion-header.header-collapse-condense');\n    const {\n      forward,\n      backward\n    } = createLargeTitleTransition(rootAnimation, isRTL, backDirection, enteringEl, leavingEl);\n    enteringToolBarEls.forEach(enteringToolBarEl => {\n      const enteringToolBar = createAnimation();\n      enteringToolBar.addElement(enteringToolBarEl);\n      rootAnimation.addAnimation(enteringToolBar);\n      const enteringTitle = createAnimation();\n      enteringTitle.addElement(enteringToolBarEl.querySelector('ion-title')); // REVIEW\n      const enteringToolBarButtons = createAnimation();\n      const buttons = Array.from(enteringToolBarEl.querySelectorAll('ion-buttons,[menuToggle]'));\n      const parentHeader = enteringToolBarEl.closest('ion-header');\n      const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n      let buttonsToAnimate;\n      if (backDirection) {\n        buttonsToAnimate = buttons.filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n      } else {\n        buttonsToAnimate = buttons.filter(button => !button.classList.contains('buttons-collapse'));\n      }\n      enteringToolBarButtons.addElement(buttonsToAnimate);\n      const enteringToolBarItems = createAnimation();\n      enteringToolBarItems.addElement(enteringToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])'));\n      const enteringToolBarBg = createAnimation();\n      enteringToolBarBg.addElement(shadow(enteringToolBarEl).querySelector('.toolbar-background')); // REVIEW\n      const enteringBackButton = createAnimation();\n      const backButtonEl = enteringToolBarEl.querySelector('ion-back-button');\n      if (backButtonEl) {\n        enteringBackButton.addElement(backButtonEl);\n      }\n      enteringToolBar.addAnimation([enteringTitle, enteringToolBarButtons, enteringToolBarItems, enteringToolBarBg, enteringBackButton]);\n      enteringToolBarButtons.fromTo(OPACITY, 0.01, 1);\n      enteringToolBarItems.fromTo(OPACITY, 0.01, 1);\n      if (backDirection) {\n        if (!inactiveHeader) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`);\n        // back direction, entering page has a back button\n        enteringBackButton.fromTo(OPACITY, 0.01, 1);\n      } else {\n        // entering toolbar, forward direction\n        if (!enteringContentHasLargeTitle) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n        enteringToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n        const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n        if (!translucentHeader) {\n          enteringToolBarBg.fromTo(OPACITY, 0.01, 'var(--opacity)');\n        } else {\n          enteringToolBarBg.fromTo('transform', isRTL ? 'translateX(-100%)' : 'translateX(100%)', 'translateX(0px)');\n        }\n        // forward direction, entering page has a back button\n        if (!forward) {\n          enteringBackButton.fromTo(OPACITY, 0.01, 1);\n        }\n        if (backButtonEl && !forward) {\n          const enteringBackBtnText = createAnimation();\n          enteringBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n          .fromTo(`transform`, isRTL ? 'translateX(-100px)' : 'translateX(100px)', 'translateX(0px)');\n          enteringToolBar.addAnimation(enteringBackBtnText);\n        }\n      }\n    });\n    // setup leaving view\n    if (leavingEl) {\n      const leavingContent = createAnimation();\n      const leavingContentEl = leavingEl.querySelector(':scope > ion-content');\n      const leavingToolBarEls = leavingEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n      const leavingHeaderEls = leavingEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n      if (!leavingContentEl && leavingToolBarEls.length === 0 && leavingHeaderEls.length === 0) {\n        leavingContent.addElement(leavingEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n      } else {\n        leavingContent.addElement(leavingContentEl); // REVIEW\n        leavingContent.addElement(leavingHeaderEls);\n      }\n      rootAnimation.addAnimation(leavingContent);\n      if (backDirection) {\n        // leaving content, back direction\n        leavingContent.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n        const leavingPage = getIonPageElement(leavingEl);\n        rootAnimation.afterAddWrite(() => {\n          if (rootAnimation.getDirection() === 'normal') {\n            leavingPage.style.setProperty('display', 'none');\n          }\n        });\n      } else {\n        // leaving content, forward direction\n        leavingContent.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 1, OFF_OPACITY);\n      }\n      if (leavingContentEl) {\n        const leavingTransitionEffectEl = shadow(leavingContentEl).querySelector('.transition-effect');\n        if (leavingTransitionEffectEl) {\n          const leavingTransitionCoverEl = leavingTransitionEffectEl.querySelector('.transition-cover');\n          const leavingTransitionShadowEl = leavingTransitionEffectEl.querySelector('.transition-shadow');\n          const leavingTransitionEffect = createAnimation();\n          const leavingTransitionCover = createAnimation();\n          const leavingTransitionShadow = createAnimation();\n          leavingTransitionEffect.addElement(leavingTransitionEffectEl).beforeStyles({\n            opacity: '1',\n            display: 'block'\n          }).afterStyles({\n            opacity: '',\n            display: ''\n          });\n          leavingTransitionCover.addElement(leavingTransitionCoverEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.1, 0);\n          leavingTransitionShadow.addElement(leavingTransitionShadowEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.7, 0.03);\n          leavingTransitionEffect.addAnimation([leavingTransitionCover, leavingTransitionShadow]);\n          leavingContent.addAnimation([leavingTransitionEffect]);\n        }\n      }\n      leavingToolBarEls.forEach(leavingToolBarEl => {\n        const leavingToolBar = createAnimation();\n        leavingToolBar.addElement(leavingToolBarEl);\n        const leavingTitle = createAnimation();\n        leavingTitle.addElement(leavingToolBarEl.querySelector('ion-title')); // REVIEW\n        const leavingToolBarButtons = createAnimation();\n        const buttons = leavingToolBarEl.querySelectorAll('ion-buttons,[menuToggle]');\n        const parentHeader = leavingToolBarEl.closest('ion-header');\n        const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n        const buttonsToAnimate = Array.from(buttons).filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n        leavingToolBarButtons.addElement(buttonsToAnimate);\n        const leavingToolBarItems = createAnimation();\n        const leavingToolBarItemEls = leavingToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])');\n        if (leavingToolBarItemEls.length > 0) {\n          leavingToolBarItems.addElement(leavingToolBarItemEls);\n        }\n        const leavingToolBarBg = createAnimation();\n        leavingToolBarBg.addElement(shadow(leavingToolBarEl).querySelector('.toolbar-background')); // REVIEW\n        const leavingBackButton = createAnimation();\n        const backButtonEl = leavingToolBarEl.querySelector('ion-back-button');\n        if (backButtonEl) {\n          leavingBackButton.addElement(backButtonEl);\n        }\n        leavingToolBar.addAnimation([leavingTitle, leavingToolBarButtons, leavingToolBarItems, leavingBackButton, leavingToolBarBg]);\n        rootAnimation.addAnimation(leavingToolBar);\n        // fade out leaving toolbar items\n        leavingBackButton.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarButtons.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarItems.fromTo(OPACITY, 0.99, 0);\n        if (backDirection) {\n          if (!inactiveHeader) {\n            // leaving toolbar, back direction\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)').fromTo(OPACITY, 0.99, 0);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          leavingToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n          // leaving toolbar, back direction, and there's no entering toolbar\n          // should just slide out, no fading out\n          const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n          if (!translucentHeader) {\n            leavingToolBarBg.fromTo(OPACITY, 'var(--opacity)', 0);\n          } else {\n            leavingToolBarBg.fromTo('transform', 'translateX(0px)', isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          }\n          if (backButtonEl && !backward) {\n            const leavingBackBtnText = createAnimation();\n            leavingBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n            .fromTo('transform', `translateX(${CENTER})`, `translateX(${(isRTL ? -124 : 124) + 'px'})`);\n            leavingToolBar.addAnimation(leavingBackBtnText);\n          }\n        } else {\n          // leaving toolbar, forward direction\n          if (!inactiveHeader) {\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 0.99, 0).afterClearStyles([TRANSFORM, OPACITY]);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).afterClearStyles([TRANSFORM, OPACITY]);\n          leavingBackButton.afterClearStyles([OPACITY]);\n          leavingTitle.afterClearStyles([OPACITY]);\n          leavingToolBarButtons.afterClearStyles([OPACITY]);\n        }\n      });\n    }\n    return rootAnimation;\n  } catch (err) {\n    throw err;\n  }\n};\n/**\n * The scale of the back button during the animation\n * is computed based on the scale of the large title\n * and vice versa. However, we need to account for slight\n * variations in the size of the large title due to\n * padding and font weight. This value should be used to subtract\n * a small amount from the large title height when computing scales\n * to get more accurate scale results.\n */\nconst LARGE_TITLE_SIZE_OFFSET = 10;\nexport { iosTransitionAnimation, shadow };", "map": {"version": 3, "names": ["c", "createAnimation", "g", "getIonPageElement", "DURATION", "getClonedElement", "tagName", "document", "querySelector", "shadow", "el", "shadowRoot", "getLargeTitle", "refEl", "tabs", "query", "activeTab", "getBackButton", "backDirection", "buttonsList", "querySelectorAll", "buttons", "parentHeader", "closest", "activeHeader", "classList", "contains", "backButton", "buttonsCollapse", "startSlot", "slot", "createLargeTitleTransition", "rootAnimation", "rtl", "enteringEl", "leavingEl", "enteringBackButton", "leavingLargeTitle", "enteringLargeTitle", "leavingBackButton", "shouldAnimationForward", "shouldAnimationBackward", "leavingLargeTitleBox", "getBoundingClientRect", "enteringBackButtonBox", "enteringBackButtonTextEl", "enteringBackButtonTextBox", "leavingLargeTitleTextEl", "leavingLargeTitleTextBox", "animate<PERSON>arge<PERSON>it<PERSON>", "animateBackButton", "enteringLargeTitleBox", "leavingBackButtonBox", "leavingBackButtonTextEl", "leavingBackButtonTextBox", "enteringLargeTitleTextEl", "enteringLargeTitleTextBox", "forward", "backward", "backButtonEl", "backButtonBox", "backButtonTextEl", "backButtonTextBox", "largeTitleEl", "largeTitleTextBox", "_a", "_b", "BACK_BUTTON_START_OFFSET", "right", "left", "TEXT_ORIGIN_X", "ICON_ORIGIN_X", "CONTAINER_ORIGIN_X", "WIDTH_SCALE", "HEIGHT_SCALE", "TEXT_START_SCALE", "TEXT_END_SCALE", "doTitleAndButtonTextsMatch", "textContent", "trim", "width", "height", "LARGE_TITLE_SIZE_OFFSET", "backButtonIconEl", "backButtonIconBox", "CONTAINER_START_TRANSLATE_X", "CONTAINER_END_TRANSLATE_X", "window", "innerWidth", "CONTAINER_START_TRANSLATE_Y", "top", "CONTAINER_END_TRANSLATE_Y", "FORWARD_CONTAINER_KEYFRAMES", "offset", "transform", "BACKWARD_CONTAINER_KEYFRAMES", "CONTAINER_KEYFRAMES", "FORWARD_TEXT_KEYFRAMES", "opacity", "BACKWARD_TEXT_KEYFRAMES", "TEXT_KEYFRAMES", "FORWARD_ICON_KEYFRAMES", "BACKWARD_ICON_KEYFRAMES", "ICON_KEYFRAMES", "enteringBackButtonTextAnimation", "enteringBackButtonIconAnimation", "enteringBackButtonAnimation", "clonedBackButtonEl", "clonedBackButtonTextEl", "clonedBackButtonIconEl", "text", "mode", "icon", "color", "disabled", "style", "setProperty", "addElement", "beforeStyles", "position", "beforeAddWrite", "afterAddWrite", "removeProperty", "keyframes", "addAnimation", "largeTitleBox", "ORIGIN_X", "TITLE_START_OFFSET", "START_TRANSLATE_X", "START_TRANSLATE_Y", "LARGE_TITLE_TRANSLATION_OFFSET", "END_TRANSLATE_X", "x", "START_SCALE", "END_SCALE", "backButtonMidPoint", "titleMidPoint", "END_TRANSLATE_Y", "BACKWARDS_KEYFRAMES", "FORWARDS_KEYFRAMES", "KEYFRAMES", "clonedTitleEl", "clonedLargeTitleAnimation", "innerText", "size", "display", "iosTransitionAnimation", "navEl", "opts", "EASING", "OPACITY", "TRANSFORM", "CENTER", "OFF_OPACITY", "isRTL", "ownerDocument", "dir", "OFF_RIGHT", "OFF_LEFT", "direction", "contentEl", "headerEls", "enteringToolBarEls", "enteringContentAnimation", "duration", "easing", "fill", "beforeRemoveClass", "undefined", "navDecorAnimation", "length", "beforeClearStyles", "fromTo", "enteringTransitionEffectEl", "enteringTransitionCoverEl", "enteringTransitionShadowEl", "enteringTransitionEffect", "enteringTransitionCover", "enteringTransitionShadow", "afterStyles", "enteringContentHasLargeTitle", "for<PERSON>ach", "enteringToolBarEl", "enteringToolBar", "enteringTitle", "enteringToolBarButtons", "Array", "from", "inactiveH<PERSON>er", "buttonsToAnimate", "filter", "button", "isCollapseButton", "enteringToolBarItems", "enteringToolBarBg", "translucentHeader", "translucent", "enteringBackBtnText", "leavingContent", "leavingContentEl", "leavingToolBarEls", "leavingHeaderEls", "leavingPage", "getDirection", "leavingTransitionEffectEl", "leavingTransitionCoverEl", "leavingTransitionShadowEl", "leavingTransitionEffect", "leavingTransitionCover", "leavingTransitionShadow", "leavingToolBarEl", "leavingToolBar", "leavingTitle", "leavingToolBarButtons", "leavingToolBarItems", "leavingToolBarItemEls", "leavingToolBarBg", "leavingBackBtnText", "afterClearStyles", "err"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/components/ios.transition.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\n\nconst DURATION = 540;\n// TODO(FW-2832): types\nconst getClonedElement = (tagName) => {\n    return document.querySelector(`${tagName}.ion-cloned-element`);\n};\nconst shadow = (el) => {\n    return el.shadowRoot || el;\n};\nconst getLargeTitle = (refEl) => {\n    const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n    const query = 'ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large';\n    if (tabs != null) {\n        const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n        return activeTab != null ? activeTab.querySelector(query) : null;\n    }\n    return refEl.querySelector(query);\n};\nconst getBackButton = (refEl, backDirection) => {\n    const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n    let buttonsList = [];\n    if (tabs != null) {\n        const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n        if (activeTab != null) {\n            buttonsList = activeTab.querySelectorAll('ion-buttons');\n        }\n    }\n    else {\n        buttonsList = refEl.querySelectorAll('ion-buttons');\n    }\n    for (const buttons of buttonsList) {\n        const parentHeader = buttons.closest('ion-header');\n        const activeHeader = parentHeader && !parentHeader.classList.contains('header-collapse-condense-inactive');\n        const backButton = buttons.querySelector('ion-back-button');\n        const buttonsCollapse = buttons.classList.contains('buttons-collapse');\n        const startSlot = buttons.slot === 'start' || buttons.slot === '';\n        if (backButton !== null && startSlot && ((buttonsCollapse && activeHeader && backDirection) || !buttonsCollapse)) {\n            return backButton;\n        }\n    }\n    return null;\n};\nconst createLargeTitleTransition = (rootAnimation, rtl, backDirection, enteringEl, leavingEl) => {\n    const enteringBackButton = getBackButton(enteringEl, backDirection);\n    const leavingLargeTitle = getLargeTitle(leavingEl);\n    const enteringLargeTitle = getLargeTitle(enteringEl);\n    const leavingBackButton = getBackButton(leavingEl, backDirection);\n    const shouldAnimationForward = enteringBackButton !== null && leavingLargeTitle !== null && !backDirection;\n    const shouldAnimationBackward = enteringLargeTitle !== null && leavingBackButton !== null && backDirection;\n    if (shouldAnimationForward) {\n        const leavingLargeTitleBox = leavingLargeTitle.getBoundingClientRect();\n        const enteringBackButtonBox = enteringBackButton.getBoundingClientRect();\n        const enteringBackButtonTextEl = shadow(enteringBackButton).querySelector('.button-text');\n        // Text element not rendered if developers pass text=\"\" to the back button\n        const enteringBackButtonTextBox = enteringBackButtonTextEl === null || enteringBackButtonTextEl === void 0 ? void 0 : enteringBackButtonTextEl.getBoundingClientRect();\n        const leavingLargeTitleTextEl = shadow(leavingLargeTitle).querySelector('.toolbar-title');\n        const leavingLargeTitleTextBox = leavingLargeTitleTextEl.getBoundingClientRect();\n        animateLargeTitle(rootAnimation, rtl, backDirection, leavingLargeTitle, leavingLargeTitleBox, leavingLargeTitleTextBox, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox);\n        animateBackButton(rootAnimation, rtl, backDirection, enteringBackButton, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox, leavingLargeTitle, leavingLargeTitleTextBox);\n    }\n    else if (shouldAnimationBackward) {\n        const enteringLargeTitleBox = enteringLargeTitle.getBoundingClientRect();\n        const leavingBackButtonBox = leavingBackButton.getBoundingClientRect();\n        const leavingBackButtonTextEl = shadow(leavingBackButton).querySelector('.button-text');\n        // Text element not rendered if developers pass text=\"\" to the back button\n        const leavingBackButtonTextBox = leavingBackButtonTextEl === null || leavingBackButtonTextEl === void 0 ? void 0 : leavingBackButtonTextEl.getBoundingClientRect();\n        const enteringLargeTitleTextEl = shadow(enteringLargeTitle).querySelector('.toolbar-title');\n        const enteringLargeTitleTextBox = enteringLargeTitleTextEl.getBoundingClientRect();\n        animateLargeTitle(rootAnimation, rtl, backDirection, enteringLargeTitle, enteringLargeTitleBox, enteringLargeTitleTextBox, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox);\n        animateBackButton(rootAnimation, rtl, backDirection, leavingBackButton, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox, enteringLargeTitle, enteringLargeTitleTextBox);\n    }\n    return {\n        forward: shouldAnimationForward,\n        backward: shouldAnimationBackward,\n    };\n};\nconst animateBackButton = (rootAnimation, rtl, backDirection, backButtonEl, backButtonBox, backButtonTextEl, backButtonTextBox, largeTitleEl, largeTitleTextBox) => {\n    var _a, _b;\n    const BACK_BUTTON_START_OFFSET = rtl ? `calc(100% - ${backButtonBox.right + 4}px)` : `${backButtonBox.left - 4}px`;\n    const TEXT_ORIGIN_X = rtl ? 'right' : 'left';\n    const ICON_ORIGIN_X = rtl ? 'left' : 'right';\n    const CONTAINER_ORIGIN_X = rtl ? 'right' : 'left';\n    let WIDTH_SCALE = 1;\n    let HEIGHT_SCALE = 1;\n    let TEXT_START_SCALE = `scale(${HEIGHT_SCALE})`;\n    const TEXT_END_SCALE = 'scale(1)';\n    if (backButtonTextEl && backButtonTextBox) {\n        /**\n         * When the title and back button texts match then they should overlap during the\n         * page transition. If the texts do not match up then the back button text scale\n         * adjusts to not perfectly match the large title text otherwise the proportions\n         * will be incorrect. When the texts match we scale both the width and height to\n         * account for font weight differences between the title and back button.\n         */\n        const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n        WIDTH_SCALE = largeTitleTextBox.width / backButtonTextBox.width;\n        /**\n         * Subtract an offset to account for slight sizing/padding differences between the\n         * title and the back button.\n         */\n        HEIGHT_SCALE = (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET) / backButtonTextBox.height;\n        /**\n         * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n         * to re-compute this here since the HEIGHT_SCALE may have changed.\n         */\n        TEXT_START_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n    }\n    const backButtonIconEl = shadow(backButtonEl).querySelector('ion-icon');\n    const backButtonIconBox = backButtonIconEl.getBoundingClientRect();\n    /**\n     * We need to offset the container by the icon dimensions\n     * so that the back button text aligns with the large title\n     * text. Otherwise, the back button icon will align with the\n     * large title text but the back button text will not.\n     */\n    const CONTAINER_START_TRANSLATE_X = rtl\n        ? `${backButtonIconBox.width / 2 - (backButtonIconBox.right - backButtonBox.right)}px`\n        : `${backButtonBox.left - backButtonIconBox.width / 2}px`;\n    const CONTAINER_END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right}px` : `${backButtonBox.left}px`;\n    /**\n     * Back button container should be\n     * aligned to the top of the title container\n     * so the texts overlap as the back button\n     * text begins to fade in.\n     */\n    const CONTAINER_START_TRANSLATE_Y = `${largeTitleTextBox.top}px`;\n    /**\n     * The cloned back button should align exactly with the\n     * real back button on the entering page otherwise there will\n     * be a layout shift.\n     */\n    const CONTAINER_END_TRANSLATE_Y = `${backButtonBox.top}px`;\n    /**\n     * In the forward direction, the cloned back button\n     * container should translate from over the large title\n     * to over the back button. In the backward direction,\n     * it should translate from over the back button to over\n     * the large title.\n     */\n    const FORWARD_CONTAINER_KEYFRAMES = [\n        { offset: 0, transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)` },\n        { offset: 1, transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)` },\n    ];\n    const BACKWARD_CONTAINER_KEYFRAMES = [\n        { offset: 0, transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)` },\n        { offset: 1, transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)` },\n    ];\n    const CONTAINER_KEYFRAMES = backDirection ? BACKWARD_CONTAINER_KEYFRAMES : FORWARD_CONTAINER_KEYFRAMES;\n    /**\n     * In the forward direction, the text in the cloned back button\n     * should start to be (roughly) the size of the large title\n     * and then scale down to be the size of the actual back button.\n     * The text should also translate, but that translate is handled\n     * by the container keyframes.\n     */\n    const FORWARD_TEXT_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: TEXT_START_SCALE },\n        { offset: 1, opacity: 1, transform: TEXT_END_SCALE },\n    ];\n    const BACKWARD_TEXT_KEYFRAMES = [\n        { offset: 0, opacity: 1, transform: TEXT_END_SCALE },\n        { offset: 1, opacity: 0, transform: TEXT_START_SCALE },\n    ];\n    const TEXT_KEYFRAMES = backDirection ? BACKWARD_TEXT_KEYFRAMES : FORWARD_TEXT_KEYFRAMES;\n    /**\n     * The icon should scale in/out in the second\n     * half of the animation. The icon should also\n     * translate, but that translate is handled by the\n     * container keyframes.\n     */\n    const FORWARD_ICON_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 0.6, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ];\n    const BACKWARD_ICON_KEYFRAMES = [\n        { offset: 0, opacity: 1, transform: 'scale(1)' },\n        { offset: 0.2, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.6)' },\n    ];\n    const ICON_KEYFRAMES = backDirection ? BACKWARD_ICON_KEYFRAMES : FORWARD_ICON_KEYFRAMES;\n    const enteringBackButtonTextAnimation = createAnimation();\n    const enteringBackButtonIconAnimation = createAnimation();\n    const enteringBackButtonAnimation = createAnimation();\n    const clonedBackButtonEl = getClonedElement('ion-back-button');\n    const clonedBackButtonTextEl = shadow(clonedBackButtonEl).querySelector('.button-text');\n    const clonedBackButtonIconEl = shadow(clonedBackButtonEl).querySelector('ion-icon');\n    clonedBackButtonEl.text = backButtonEl.text;\n    clonedBackButtonEl.mode = backButtonEl.mode;\n    clonedBackButtonEl.icon = backButtonEl.icon;\n    clonedBackButtonEl.color = backButtonEl.color;\n    clonedBackButtonEl.disabled = backButtonEl.disabled;\n    clonedBackButtonEl.style.setProperty('display', 'block');\n    clonedBackButtonEl.style.setProperty('position', 'fixed');\n    enteringBackButtonIconAnimation.addElement(clonedBackButtonIconEl);\n    enteringBackButtonTextAnimation.addElement(clonedBackButtonTextEl);\n    enteringBackButtonAnimation.addElement(clonedBackButtonEl);\n    enteringBackButtonAnimation\n        .beforeStyles({\n        position: 'absolute',\n        top: '0px',\n        [CONTAINER_ORIGIN_X]: '0px',\n    })\n        /**\n         * The write hooks must be set on this animation as it is guaranteed to run. Other\n         * animations such as the back button text animation will not run if the back button\n         * has no visible text.\n         */\n        .beforeAddWrite(() => {\n        backButtonEl.style.setProperty('display', 'none');\n        clonedBackButtonEl.style.setProperty(TEXT_ORIGIN_X, BACK_BUTTON_START_OFFSET);\n    })\n        .afterAddWrite(() => {\n        backButtonEl.style.setProperty('display', '');\n        clonedBackButtonEl.style.setProperty('display', 'none');\n        clonedBackButtonEl.style.removeProperty(TEXT_ORIGIN_X);\n    })\n        .keyframes(CONTAINER_KEYFRAMES);\n    enteringBackButtonTextAnimation\n        .beforeStyles({\n        'transform-origin': `${TEXT_ORIGIN_X} top`,\n    })\n        .keyframes(TEXT_KEYFRAMES);\n    enteringBackButtonIconAnimation\n        .beforeStyles({\n        'transform-origin': `${ICON_ORIGIN_X} center`,\n    })\n        .keyframes(ICON_KEYFRAMES);\n    rootAnimation.addAnimation([\n        enteringBackButtonTextAnimation,\n        enteringBackButtonIconAnimation,\n        enteringBackButtonAnimation,\n    ]);\n};\nconst animateLargeTitle = (rootAnimation, rtl, backDirection, largeTitleEl, largeTitleBox, largeTitleTextBox, backButtonBox, backButtonTextEl, backButtonTextBox) => {\n    var _a, _b;\n    /**\n     * The horizontal transform origin for the large title\n     */\n    const ORIGIN_X = rtl ? 'right' : 'left';\n    const TITLE_START_OFFSET = rtl ? `calc(100% - ${largeTitleBox.right}px)` : `${largeTitleBox.left}px`;\n    /**\n     * The cloned large should align exactly with the\n     * real large title on the leaving page otherwise there will\n     * be a layout shift.\n     */\n    const START_TRANSLATE_X = '0px';\n    const START_TRANSLATE_Y = `${largeTitleBox.top}px`;\n    /**\n     * How much to offset the large title translation by.\n     * This accounts for differences in sizing between the large\n     * title and the back button due to padding and font weight.\n     */\n    const LARGE_TITLE_TRANSLATION_OFFSET = 8;\n    let END_TRANSLATE_X = rtl\n        ? `-${window.innerWidth - backButtonBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px`\n        : `${backButtonBox.x + LARGE_TITLE_TRANSLATION_OFFSET}px`;\n    /**\n     * How much to scale the large title up/down by.\n     */\n    let HEIGHT_SCALE = 0.5;\n    /**\n     * The large title always starts full size.\n     */\n    const START_SCALE = 'scale(1)';\n    /**\n     * By default, we don't worry about having the large title scaled to perfectly\n     * match the back button because we don't know if the back button's text matches\n     * the large title's text.\n     */\n    let END_SCALE = `scale(${HEIGHT_SCALE})`;\n    // Text element not rendered if developers pass text=\"\" to the back button\n    if (backButtonTextEl && backButtonTextBox) {\n        /**\n         * The scaled title should (roughly) overlap the back button. This ensures that\n         * the back button and title overlap during the animation. Note that since both\n         * elements either fade in or fade out over the course of the animation, neither\n         * element will be fully visible on top of the other. As a result, the overlap\n         * does not need to be perfect, so approximate values are acceptable here.\n         */\n        END_TRANSLATE_X = rtl\n            ? `-${window.innerWidth - backButtonTextBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px`\n            : `${backButtonTextBox.x - LARGE_TITLE_TRANSLATION_OFFSET}px`;\n        /**\n         * In the forward direction, the large title should start at its normal size and\n         * then scale down to be (roughly) the size of the back button on the other view.\n         * In the backward direction, the large title should start at (roughly) the size\n         * of the back button and then scale up to its original size.\n         * Note that since both elements either fade in or fade out over the course of the\n         * animation, neither element will be fully visible on top of the other. As a result,\n         * the overlap  does not need to be perfect, so approximate values are acceptable here.\n         */\n        /**\n         * When the title and back button texts match then they should overlap during the\n         * page transition. If the texts do not match up then the large title text scale\n         * adjusts to not perfectly match the back button text otherwise the proportions\n         * will be incorrect. When the texts match we scale both the width and height to\n         * account for font weight differences between the title and back button.\n         */\n        const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n        const WIDTH_SCALE = backButtonTextBox.width / largeTitleTextBox.width;\n        HEIGHT_SCALE = backButtonTextBox.height / (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET);\n        /**\n         * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n         * to re-compute this here since the HEIGHT_SCALE may have changed.\n         */\n        END_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n    }\n    /**\n     * The midpoints of the back button and the title should align such that the back\n     * button and title appear to be centered with each other.\n     */\n    const backButtonMidPoint = backButtonBox.top + backButtonBox.height / 2;\n    const titleMidPoint = (largeTitleBox.height * HEIGHT_SCALE) / 2;\n    const END_TRANSLATE_Y = `${backButtonMidPoint - titleMidPoint}px`;\n    const BACKWARDS_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}` },\n        { offset: 0.1, opacity: 0 },\n        { offset: 1, opacity: 1, transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}` },\n    ];\n    const FORWARDS_KEYFRAMES = [\n        {\n            offset: 0,\n            opacity: 0.99,\n            transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`,\n        },\n        { offset: 0.6, opacity: 0 },\n        { offset: 1, opacity: 0, transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}` },\n    ];\n    const KEYFRAMES = backDirection ? BACKWARDS_KEYFRAMES : FORWARDS_KEYFRAMES;\n    const clonedTitleEl = getClonedElement('ion-title');\n    const clonedLargeTitleAnimation = createAnimation();\n    clonedTitleEl.innerText = largeTitleEl.innerText;\n    clonedTitleEl.size = largeTitleEl.size;\n    clonedTitleEl.color = largeTitleEl.color;\n    clonedLargeTitleAnimation.addElement(clonedTitleEl);\n    clonedLargeTitleAnimation\n        .beforeStyles({\n        'transform-origin': `${ORIGIN_X} top`,\n        /**\n         * Since font size changes will cause\n         * the dimension of the large title to change\n         * we need to set the cloned title height\n         * equal to that of the original large title height.\n         */\n        height: `${largeTitleBox.height}px`,\n        display: '',\n        position: 'relative',\n        [ORIGIN_X]: TITLE_START_OFFSET,\n    })\n        .beforeAddWrite(() => {\n        largeTitleEl.style.setProperty('opacity', '0');\n    })\n        .afterAddWrite(() => {\n        largeTitleEl.style.setProperty('opacity', '');\n        clonedTitleEl.style.setProperty('display', 'none');\n    })\n        .keyframes(KEYFRAMES);\n    rootAnimation.addAnimation(clonedLargeTitleAnimation);\n};\nconst iosTransitionAnimation = (navEl, opts) => {\n    var _a;\n    try {\n        const EASING = 'cubic-bezier(0.32,0.72,0,1)';\n        const OPACITY = 'opacity';\n        const TRANSFORM = 'transform';\n        const CENTER = '0%';\n        const OFF_OPACITY = 0.8;\n        const isRTL = navEl.ownerDocument.dir === 'rtl';\n        const OFF_RIGHT = isRTL ? '-99.5%' : '99.5%';\n        const OFF_LEFT = isRTL ? '33%' : '-33%';\n        const enteringEl = opts.enteringEl;\n        const leavingEl = opts.leavingEl;\n        const backDirection = opts.direction === 'back';\n        const contentEl = enteringEl.querySelector(':scope > ion-content');\n        const headerEls = enteringEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n        const enteringToolBarEls = enteringEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n        const rootAnimation = createAnimation();\n        const enteringContentAnimation = createAnimation();\n        rootAnimation\n            .addElement(enteringEl)\n            .duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || DURATION)\n            .easing(opts.easing || EASING)\n            .fill('both')\n            .beforeRemoveClass('ion-page-invisible');\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        if (leavingEl && navEl !== null && navEl !== undefined) {\n            const navDecorAnimation = createAnimation();\n            navDecorAnimation.addElement(navEl);\n            rootAnimation.addAnimation(navDecorAnimation);\n        }\n        if (!contentEl && enteringToolBarEls.length === 0 && headerEls.length === 0) {\n            enteringContentAnimation.addElement(enteringEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n        }\n        else {\n            enteringContentAnimation.addElement(contentEl); // REVIEW\n            enteringContentAnimation.addElement(headerEls);\n        }\n        rootAnimation.addAnimation(enteringContentAnimation);\n        if (backDirection) {\n            enteringContentAnimation\n                .beforeClearStyles([OPACITY])\n                .fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`)\n                .fromTo(OPACITY, OFF_OPACITY, 1);\n        }\n        else {\n            // entering content, forward direction\n            enteringContentAnimation\n                .beforeClearStyles([OPACITY])\n                .fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n        }\n        if (contentEl) {\n            const enteringTransitionEffectEl = shadow(contentEl).querySelector('.transition-effect');\n            if (enteringTransitionEffectEl) {\n                const enteringTransitionCoverEl = enteringTransitionEffectEl.querySelector('.transition-cover');\n                const enteringTransitionShadowEl = enteringTransitionEffectEl.querySelector('.transition-shadow');\n                const enteringTransitionEffect = createAnimation();\n                const enteringTransitionCover = createAnimation();\n                const enteringTransitionShadow = createAnimation();\n                enteringTransitionEffect\n                    .addElement(enteringTransitionEffectEl)\n                    .beforeStyles({ opacity: '1', display: 'block' })\n                    .afterStyles({ opacity: '', display: '' });\n                enteringTransitionCover\n                    .addElement(enteringTransitionCoverEl) // REVIEW\n                    .beforeClearStyles([OPACITY])\n                    .fromTo(OPACITY, 0, 0.1);\n                enteringTransitionShadow\n                    .addElement(enteringTransitionShadowEl) // REVIEW\n                    .beforeClearStyles([OPACITY])\n                    .fromTo(OPACITY, 0.03, 0.7);\n                enteringTransitionEffect.addAnimation([enteringTransitionCover, enteringTransitionShadow]);\n                enteringContentAnimation.addAnimation([enteringTransitionEffect]);\n            }\n        }\n        const enteringContentHasLargeTitle = enteringEl.querySelector('ion-header.header-collapse-condense');\n        const { forward, backward } = createLargeTitleTransition(rootAnimation, isRTL, backDirection, enteringEl, leavingEl);\n        enteringToolBarEls.forEach((enteringToolBarEl) => {\n            const enteringToolBar = createAnimation();\n            enteringToolBar.addElement(enteringToolBarEl);\n            rootAnimation.addAnimation(enteringToolBar);\n            const enteringTitle = createAnimation();\n            enteringTitle.addElement(enteringToolBarEl.querySelector('ion-title')); // REVIEW\n            const enteringToolBarButtons = createAnimation();\n            const buttons = Array.from(enteringToolBarEl.querySelectorAll('ion-buttons,[menuToggle]'));\n            const parentHeader = enteringToolBarEl.closest('ion-header');\n            const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n            let buttonsToAnimate;\n            if (backDirection) {\n                buttonsToAnimate = buttons.filter((button) => {\n                    const isCollapseButton = button.classList.contains('buttons-collapse');\n                    return (isCollapseButton && !inactiveHeader) || !isCollapseButton;\n                });\n            }\n            else {\n                buttonsToAnimate = buttons.filter((button) => !button.classList.contains('buttons-collapse'));\n            }\n            enteringToolBarButtons.addElement(buttonsToAnimate);\n            const enteringToolBarItems = createAnimation();\n            enteringToolBarItems.addElement(enteringToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])'));\n            const enteringToolBarBg = createAnimation();\n            enteringToolBarBg.addElement(shadow(enteringToolBarEl).querySelector('.toolbar-background')); // REVIEW\n            const enteringBackButton = createAnimation();\n            const backButtonEl = enteringToolBarEl.querySelector('ion-back-button');\n            if (backButtonEl) {\n                enteringBackButton.addElement(backButtonEl);\n            }\n            enteringToolBar.addAnimation([\n                enteringTitle,\n                enteringToolBarButtons,\n                enteringToolBarItems,\n                enteringToolBarBg,\n                enteringBackButton,\n            ]);\n            enteringToolBarButtons.fromTo(OPACITY, 0.01, 1);\n            enteringToolBarItems.fromTo(OPACITY, 0.01, 1);\n            if (backDirection) {\n                if (!inactiveHeader) {\n                    enteringTitle\n                        .fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`)\n                        .fromTo(OPACITY, 0.01, 1);\n                }\n                enteringToolBarItems.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`);\n                // back direction, entering page has a back button\n                enteringBackButton.fromTo(OPACITY, 0.01, 1);\n            }\n            else {\n                // entering toolbar, forward direction\n                if (!enteringContentHasLargeTitle) {\n                    enteringTitle\n                        .fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`)\n                        .fromTo(OPACITY, 0.01, 1);\n                }\n                enteringToolBarItems.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n                enteringToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n                const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n                if (!translucentHeader) {\n                    enteringToolBarBg.fromTo(OPACITY, 0.01, 'var(--opacity)');\n                }\n                else {\n                    enteringToolBarBg.fromTo('transform', isRTL ? 'translateX(-100%)' : 'translateX(100%)', 'translateX(0px)');\n                }\n                // forward direction, entering page has a back button\n                if (!forward) {\n                    enteringBackButton.fromTo(OPACITY, 0.01, 1);\n                }\n                if (backButtonEl && !forward) {\n                    const enteringBackBtnText = createAnimation();\n                    enteringBackBtnText\n                        .addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n                        .fromTo(`transform`, isRTL ? 'translateX(-100px)' : 'translateX(100px)', 'translateX(0px)');\n                    enteringToolBar.addAnimation(enteringBackBtnText);\n                }\n            }\n        });\n        // setup leaving view\n        if (leavingEl) {\n            const leavingContent = createAnimation();\n            const leavingContentEl = leavingEl.querySelector(':scope > ion-content');\n            const leavingToolBarEls = leavingEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n            const leavingHeaderEls = leavingEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n            if (!leavingContentEl && leavingToolBarEls.length === 0 && leavingHeaderEls.length === 0) {\n                leavingContent.addElement(leavingEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n            }\n            else {\n                leavingContent.addElement(leavingContentEl); // REVIEW\n                leavingContent.addElement(leavingHeaderEls);\n            }\n            rootAnimation.addAnimation(leavingContent);\n            if (backDirection) {\n                // leaving content, back direction\n                leavingContent\n                    .beforeClearStyles([OPACITY])\n                    .fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                const leavingPage = getIonPageElement(leavingEl);\n                rootAnimation.afterAddWrite(() => {\n                    if (rootAnimation.getDirection() === 'normal') {\n                        leavingPage.style.setProperty('display', 'none');\n                    }\n                });\n            }\n            else {\n                // leaving content, forward direction\n                leavingContent\n                    .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                    .fromTo(OPACITY, 1, OFF_OPACITY);\n            }\n            if (leavingContentEl) {\n                const leavingTransitionEffectEl = shadow(leavingContentEl).querySelector('.transition-effect');\n                if (leavingTransitionEffectEl) {\n                    const leavingTransitionCoverEl = leavingTransitionEffectEl.querySelector('.transition-cover');\n                    const leavingTransitionShadowEl = leavingTransitionEffectEl.querySelector('.transition-shadow');\n                    const leavingTransitionEffect = createAnimation();\n                    const leavingTransitionCover = createAnimation();\n                    const leavingTransitionShadow = createAnimation();\n                    leavingTransitionEffect\n                        .addElement(leavingTransitionEffectEl)\n                        .beforeStyles({ opacity: '1', display: 'block' })\n                        .afterStyles({ opacity: '', display: '' });\n                    leavingTransitionCover\n                        .addElement(leavingTransitionCoverEl) // REVIEW\n                        .beforeClearStyles([OPACITY])\n                        .fromTo(OPACITY, 0.1, 0);\n                    leavingTransitionShadow\n                        .addElement(leavingTransitionShadowEl) // REVIEW\n                        .beforeClearStyles([OPACITY])\n                        .fromTo(OPACITY, 0.7, 0.03);\n                    leavingTransitionEffect.addAnimation([leavingTransitionCover, leavingTransitionShadow]);\n                    leavingContent.addAnimation([leavingTransitionEffect]);\n                }\n            }\n            leavingToolBarEls.forEach((leavingToolBarEl) => {\n                const leavingToolBar = createAnimation();\n                leavingToolBar.addElement(leavingToolBarEl);\n                const leavingTitle = createAnimation();\n                leavingTitle.addElement(leavingToolBarEl.querySelector('ion-title')); // REVIEW\n                const leavingToolBarButtons = createAnimation();\n                const buttons = leavingToolBarEl.querySelectorAll('ion-buttons,[menuToggle]');\n                const parentHeader = leavingToolBarEl.closest('ion-header');\n                const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n                const buttonsToAnimate = Array.from(buttons).filter((button) => {\n                    const isCollapseButton = button.classList.contains('buttons-collapse');\n                    return (isCollapseButton && !inactiveHeader) || !isCollapseButton;\n                });\n                leavingToolBarButtons.addElement(buttonsToAnimate);\n                const leavingToolBarItems = createAnimation();\n                const leavingToolBarItemEls = leavingToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])');\n                if (leavingToolBarItemEls.length > 0) {\n                    leavingToolBarItems.addElement(leavingToolBarItemEls);\n                }\n                const leavingToolBarBg = createAnimation();\n                leavingToolBarBg.addElement(shadow(leavingToolBarEl).querySelector('.toolbar-background')); // REVIEW\n                const leavingBackButton = createAnimation();\n                const backButtonEl = leavingToolBarEl.querySelector('ion-back-button');\n                if (backButtonEl) {\n                    leavingBackButton.addElement(backButtonEl);\n                }\n                leavingToolBar.addAnimation([\n                    leavingTitle,\n                    leavingToolBarButtons,\n                    leavingToolBarItems,\n                    leavingBackButton,\n                    leavingToolBarBg,\n                ]);\n                rootAnimation.addAnimation(leavingToolBar);\n                // fade out leaving toolbar items\n                leavingBackButton.fromTo(OPACITY, 0.99, 0);\n                leavingToolBarButtons.fromTo(OPACITY, 0.99, 0);\n                leavingToolBarItems.fromTo(OPACITY, 0.99, 0);\n                if (backDirection) {\n                    if (!inactiveHeader) {\n                        // leaving toolbar, back direction\n                        leavingTitle\n                            .fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)')\n                            .fromTo(OPACITY, 0.99, 0);\n                    }\n                    leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                    leavingToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n                    // leaving toolbar, back direction, and there's no entering toolbar\n                    // should just slide out, no fading out\n                    const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n                    if (!translucentHeader) {\n                        leavingToolBarBg.fromTo(OPACITY, 'var(--opacity)', 0);\n                    }\n                    else {\n                        leavingToolBarBg.fromTo('transform', 'translateX(0px)', isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                    }\n                    if (backButtonEl && !backward) {\n                        const leavingBackBtnText = createAnimation();\n                        leavingBackBtnText\n                            .addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n                            .fromTo('transform', `translateX(${CENTER})`, `translateX(${(isRTL ? -124 : 124) + 'px'})`);\n                        leavingToolBar.addAnimation(leavingBackBtnText);\n                    }\n                }\n                else {\n                    // leaving toolbar, forward direction\n                    if (!inactiveHeader) {\n                        leavingTitle\n                            .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                            .fromTo(OPACITY, 0.99, 0)\n                            .afterClearStyles([TRANSFORM, OPACITY]);\n                    }\n                    leavingToolBarItems\n                        .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                        .afterClearStyles([TRANSFORM, OPACITY]);\n                    leavingBackButton.afterClearStyles([OPACITY]);\n                    leavingTitle.afterClearStyles([OPACITY]);\n                    leavingToolBarButtons.afterClearStyles([OPACITY]);\n                }\n            });\n        }\n        return rootAnimation;\n    }\n    catch (err) {\n        throw err;\n    }\n};\n/**\n * The scale of the back button during the animation\n * is computed based on the scale of the large title\n * and vice versa. However, we need to account for slight\n * variations in the size of the large title due to\n * padding and font weight. This value should be used to subtract\n * a small amount from the large title height when computing scales\n * to get more accurate scale results.\n */\nconst LARGE_TITLE_SIZE_OFFSET = 10;\n\nexport { iosTransitionAnimation, shadow };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,eAAe,QAAQ,gBAAgB;AACrD,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,aAAa;AAEpD,MAAMC,QAAQ,GAAG,GAAG;AACpB;AACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;EAClC,OAAOC,QAAQ,CAACC,aAAa,CAAC,GAAGF,OAAO,qBAAqB,CAAC;AAClE,CAAC;AACD,MAAMG,MAAM,GAAIC,EAAE,IAAK;EACnB,OAAOA,EAAE,CAACC,UAAU,IAAID,EAAE;AAC9B,CAAC;AACD,MAAME,aAAa,GAAIC,KAAK,IAAK;EAC7B,MAAMC,IAAI,GAAGD,KAAK,CAACP,OAAO,KAAK,UAAU,GAAGO,KAAK,GAAGA,KAAK,CAACL,aAAa,CAAC,UAAU,CAAC;EACnF,MAAMO,KAAK,GAAG,sFAAsF;EACpG,IAAID,IAAI,IAAI,IAAI,EAAE;IACd,MAAME,SAAS,GAAGF,IAAI,CAACN,aAAa,CAAC,2DAA2D,CAAC;IACjG,OAAOQ,SAAS,IAAI,IAAI,GAAGA,SAAS,CAACR,aAAa,CAACO,KAAK,CAAC,GAAG,IAAI;EACpE;EACA,OAAOF,KAAK,CAACL,aAAa,CAACO,KAAK,CAAC;AACrC,CAAC;AACD,MAAME,aAAa,GAAGA,CAACJ,KAAK,EAAEK,aAAa,KAAK;EAC5C,MAAMJ,IAAI,GAAGD,KAAK,CAACP,OAAO,KAAK,UAAU,GAAGO,KAAK,GAAGA,KAAK,CAACL,aAAa,CAAC,UAAU,CAAC;EACnF,IAAIW,WAAW,GAAG,EAAE;EACpB,IAAIL,IAAI,IAAI,IAAI,EAAE;IACd,MAAME,SAAS,GAAGF,IAAI,CAACN,aAAa,CAAC,2DAA2D,CAAC;IACjG,IAAIQ,SAAS,IAAI,IAAI,EAAE;MACnBG,WAAW,GAAGH,SAAS,CAACI,gBAAgB,CAAC,aAAa,CAAC;IAC3D;EACJ,CAAC,MACI;IACDD,WAAW,GAAGN,KAAK,CAACO,gBAAgB,CAAC,aAAa,CAAC;EACvD;EACA,KAAK,MAAMC,OAAO,IAAIF,WAAW,EAAE;IAC/B,MAAMG,YAAY,GAAGD,OAAO,CAACE,OAAO,CAAC,YAAY,CAAC;IAClD,MAAMC,YAAY,GAAGF,YAAY,IAAI,CAACA,YAAY,CAACG,SAAS,CAACC,QAAQ,CAAC,mCAAmC,CAAC;IAC1G,MAAMC,UAAU,GAAGN,OAAO,CAACb,aAAa,CAAC,iBAAiB,CAAC;IAC3D,MAAMoB,eAAe,GAAGP,OAAO,CAACI,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC;IACtE,MAAMG,SAAS,GAAGR,OAAO,CAACS,IAAI,KAAK,OAAO,IAAIT,OAAO,CAACS,IAAI,KAAK,EAAE;IACjE,IAAIH,UAAU,KAAK,IAAI,IAAIE,SAAS,KAAMD,eAAe,IAAIJ,YAAY,IAAIN,aAAa,IAAK,CAACU,eAAe,CAAC,EAAE;MAC9G,OAAOD,UAAU;IACrB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMI,0BAA0B,GAAGA,CAACC,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEgB,UAAU,EAAEC,SAAS,KAAK;EAC7F,MAAMC,kBAAkB,GAAGnB,aAAa,CAACiB,UAAU,EAAEhB,aAAa,CAAC;EACnE,MAAMmB,iBAAiB,GAAGzB,aAAa,CAACuB,SAAS,CAAC;EAClD,MAAMG,kBAAkB,GAAG1B,aAAa,CAACsB,UAAU,CAAC;EACpD,MAAMK,iBAAiB,GAAGtB,aAAa,CAACkB,SAAS,EAAEjB,aAAa,CAAC;EACjE,MAAMsB,sBAAsB,GAAGJ,kBAAkB,KAAK,IAAI,IAAIC,iBAAiB,KAAK,IAAI,IAAI,CAACnB,aAAa;EAC1G,MAAMuB,uBAAuB,GAAGH,kBAAkB,KAAK,IAAI,IAAIC,iBAAiB,KAAK,IAAI,IAAIrB,aAAa;EAC1G,IAAIsB,sBAAsB,EAAE;IACxB,MAAME,oBAAoB,GAAGL,iBAAiB,CAACM,qBAAqB,CAAC,CAAC;IACtE,MAAMC,qBAAqB,GAAGR,kBAAkB,CAACO,qBAAqB,CAAC,CAAC;IACxE,MAAME,wBAAwB,GAAGpC,MAAM,CAAC2B,kBAAkB,CAAC,CAAC5B,aAAa,CAAC,cAAc,CAAC;IACzF;IACA,MAAMsC,yBAAyB,GAAGD,wBAAwB,KAAK,IAAI,IAAIA,wBAAwB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,wBAAwB,CAACF,qBAAqB,CAAC,CAAC;IACtK,MAAMI,uBAAuB,GAAGtC,MAAM,CAAC4B,iBAAiB,CAAC,CAAC7B,aAAa,CAAC,gBAAgB,CAAC;IACzF,MAAMwC,wBAAwB,GAAGD,uBAAuB,CAACJ,qBAAqB,CAAC,CAAC;IAChFM,iBAAiB,CAACjB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEmB,iBAAiB,EAAEK,oBAAoB,EAAEM,wBAAwB,EAAEJ,qBAAqB,EAAEC,wBAAwB,EAAEC,yBAAyB,CAAC;IACnMI,iBAAiB,CAAClB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEkB,kBAAkB,EAAEQ,qBAAqB,EAAEC,wBAAwB,EAAEC,yBAAyB,EAAET,iBAAiB,EAAEW,wBAAwB,CAAC;EACrM,CAAC,MACI,IAAIP,uBAAuB,EAAE;IAC9B,MAAMU,qBAAqB,GAAGb,kBAAkB,CAACK,qBAAqB,CAAC,CAAC;IACxE,MAAMS,oBAAoB,GAAGb,iBAAiB,CAACI,qBAAqB,CAAC,CAAC;IACtE,MAAMU,uBAAuB,GAAG5C,MAAM,CAAC8B,iBAAiB,CAAC,CAAC/B,aAAa,CAAC,cAAc,CAAC;IACvF;IACA,MAAM8C,wBAAwB,GAAGD,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACV,qBAAqB,CAAC,CAAC;IAClK,MAAMY,wBAAwB,GAAG9C,MAAM,CAAC6B,kBAAkB,CAAC,CAAC9B,aAAa,CAAC,gBAAgB,CAAC;IAC3F,MAAMgD,yBAAyB,GAAGD,wBAAwB,CAACZ,qBAAqB,CAAC,CAAC;IAClFM,iBAAiB,CAACjB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEoB,kBAAkB,EAAEa,qBAAqB,EAAEK,yBAAyB,EAAEJ,oBAAoB,EAAEC,uBAAuB,EAAEC,wBAAwB,CAAC;IACnMJ,iBAAiB,CAAClB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEqB,iBAAiB,EAAEa,oBAAoB,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEhB,kBAAkB,EAAEkB,yBAAyB,CAAC;EACnM;EACA,OAAO;IACHC,OAAO,EAAEjB,sBAAsB;IAC/BkB,QAAQ,EAAEjB;EACd,CAAC;AACL,CAAC;AACD,MAAMS,iBAAiB,GAAGA,CAAClB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAEyC,YAAY,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,iBAAiB,KAAK;EAChK,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,wBAAwB,GAAGlC,GAAG,GAAG,eAAe2B,aAAa,CAACQ,KAAK,GAAG,CAAC,KAAK,GAAG,GAAGR,aAAa,CAACS,IAAI,GAAG,CAAC,IAAI;EAClH,MAAMC,aAAa,GAAGrC,GAAG,GAAG,OAAO,GAAG,MAAM;EAC5C,MAAMsC,aAAa,GAAGtC,GAAG,GAAG,MAAM,GAAG,OAAO;EAC5C,MAAMuC,kBAAkB,GAAGvC,GAAG,GAAG,OAAO,GAAG,MAAM;EACjD,IAAIwC,WAAW,GAAG,CAAC;EACnB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,gBAAgB,GAAG,SAASD,YAAY,GAAG;EAC/C,MAAME,cAAc,GAAG,UAAU;EACjC,IAAIf,gBAAgB,IAAIC,iBAAiB,EAAE;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMe,0BAA0B,GAAG,CAAC,CAACZ,EAAE,GAAGJ,gBAAgB,CAACiB,WAAW,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAAC,CAAC,OAAO,CAACb,EAAE,GAAGH,YAAY,CAACe,WAAW,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC,CAAC,CAAC;IAC5MN,WAAW,GAAGT,iBAAiB,CAACgB,KAAK,GAAGlB,iBAAiB,CAACkB,KAAK;IAC/D;AACR;AACA;AACA;IACQN,YAAY,GAAG,CAACV,iBAAiB,CAACiB,MAAM,GAAGC,uBAAuB,IAAIpB,iBAAiB,CAACmB,MAAM;IAC9F;AACR;AACA;AACA;IACQN,gBAAgB,GAAGE,0BAA0B,GAAG,SAASJ,WAAW,KAAKC,YAAY,GAAG,GAAG,SAASA,YAAY,GAAG;EACvH;EACA,MAAMS,gBAAgB,GAAG1E,MAAM,CAACkD,YAAY,CAAC,CAACnD,aAAa,CAAC,UAAU,CAAC;EACvE,MAAM4E,iBAAiB,GAAGD,gBAAgB,CAACxC,qBAAqB,CAAC,CAAC;EAClE;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM0C,2BAA2B,GAAGpD,GAAG,GACjC,GAAGmD,iBAAiB,CAACJ,KAAK,GAAG,CAAC,IAAII,iBAAiB,CAAChB,KAAK,GAAGR,aAAa,CAACQ,KAAK,CAAC,IAAI,GACpF,GAAGR,aAAa,CAACS,IAAI,GAAGe,iBAAiB,CAACJ,KAAK,GAAG,CAAC,IAAI;EAC7D,MAAMM,yBAAyB,GAAGrD,GAAG,GAAG,IAAIsD,MAAM,CAACC,UAAU,GAAG5B,aAAa,CAACQ,KAAK,IAAI,GAAG,GAAGR,aAAa,CAACS,IAAI,IAAI;EACnH;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMoB,2BAA2B,GAAG,GAAGzB,iBAAiB,CAAC0B,GAAG,IAAI;EAChE;AACJ;AACA;AACA;AACA;EACI,MAAMC,yBAAyB,GAAG,GAAG/B,aAAa,CAAC8B,GAAG,IAAI;EAC1D;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAME,2BAA2B,GAAG,CAChC;IAAEC,MAAM,EAAE,CAAC;IAAEC,SAAS,EAAE,eAAeT,2BAA2B,KAAKI,2BAA2B;EAAO,CAAC,EAC1G;IAAEI,MAAM,EAAE,CAAC;IAAEC,SAAS,EAAE,eAAeR,yBAAyB,KAAKK,yBAAyB;EAAO,CAAC,CACzG;EACD,MAAMI,4BAA4B,GAAG,CACjC;IAAEF,MAAM,EAAE,CAAC;IAAEC,SAAS,EAAE,eAAeR,yBAAyB,KAAKK,yBAAyB;EAAO,CAAC,EACtG;IAAEE,MAAM,EAAE,CAAC;IAAEC,SAAS,EAAE,eAAeT,2BAA2B,KAAKI,2BAA2B;EAAO,CAAC,CAC7G;EACD,MAAMO,mBAAmB,GAAG9E,aAAa,GAAG6E,4BAA4B,GAAGH,2BAA2B;EACtG;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMK,sBAAsB,GAAG,CAC3B;IAAEJ,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAEnB;EAAiB,CAAC,EACtD;IAAEkB,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAElB;EAAe,CAAC,CACvD;EACD,MAAMuB,uBAAuB,GAAG,CAC5B;IAAEN,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAElB;EAAe,CAAC,EACpD;IAAEiB,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAEnB;EAAiB,CAAC,CACzD;EACD,MAAMyB,cAAc,GAAGlF,aAAa,GAAGiF,uBAAuB,GAAGF,sBAAsB;EACvF;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMI,sBAAsB,GAAG,CAC3B;IAAER,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAa,CAAC,EAClD;IAAED,MAAM,EAAE,GAAG;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAa,CAAC,EACpD;IAAED,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAW,CAAC,CACnD;EACD,MAAMQ,uBAAuB,GAAG,CAC5B;IAAET,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAW,CAAC,EAChD;IAAED,MAAM,EAAE,GAAG;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAa,CAAC,EACpD;IAAED,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE;EAAa,CAAC,CACrD;EACD,MAAMS,cAAc,GAAGrF,aAAa,GAAGoF,uBAAuB,GAAGD,sBAAsB;EACvF,MAAMG,+BAA+B,GAAGvG,eAAe,CAAC,CAAC;EACzD,MAAMwG,+BAA+B,GAAGxG,eAAe,CAAC,CAAC;EACzD,MAAMyG,2BAA2B,GAAGzG,eAAe,CAAC,CAAC;EACrD,MAAM0G,kBAAkB,GAAGtG,gBAAgB,CAAC,iBAAiB,CAAC;EAC9D,MAAMuG,sBAAsB,GAAGnG,MAAM,CAACkG,kBAAkB,CAAC,CAACnG,aAAa,CAAC,cAAc,CAAC;EACvF,MAAMqG,sBAAsB,GAAGpG,MAAM,CAACkG,kBAAkB,CAAC,CAACnG,aAAa,CAAC,UAAU,CAAC;EACnFmG,kBAAkB,CAACG,IAAI,GAAGnD,YAAY,CAACmD,IAAI;EAC3CH,kBAAkB,CAACI,IAAI,GAAGpD,YAAY,CAACoD,IAAI;EAC3CJ,kBAAkB,CAACK,IAAI,GAAGrD,YAAY,CAACqD,IAAI;EAC3CL,kBAAkB,CAACM,KAAK,GAAGtD,YAAY,CAACsD,KAAK;EAC7CN,kBAAkB,CAACO,QAAQ,GAAGvD,YAAY,CAACuD,QAAQ;EACnDP,kBAAkB,CAACQ,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC;EACxDT,kBAAkB,CAACQ,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC;EACzDX,+BAA+B,CAACY,UAAU,CAACR,sBAAsB,CAAC;EAClEL,+BAA+B,CAACa,UAAU,CAACT,sBAAsB,CAAC;EAClEF,2BAA2B,CAACW,UAAU,CAACV,kBAAkB,CAAC;EAC1DD,2BAA2B,CACtBY,YAAY,CAAC;IACdC,QAAQ,EAAE,UAAU;IACpB7B,GAAG,EAAE,KAAK;IACV,CAAClB,kBAAkB,GAAG;EAC1B,CAAC;EACG;AACR;AACA;AACA;AACA,KAJQ,CAKCgD,cAAc,CAAC,MAAM;IACtB7D,YAAY,CAACwD,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;IACjDT,kBAAkB,CAACQ,KAAK,CAACC,WAAW,CAAC9C,aAAa,EAAEH,wBAAwB,CAAC;EACjF,CAAC,CAAC,CACGsD,aAAa,CAAC,MAAM;IACrB9D,YAAY,CAACwD,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7CT,kBAAkB,CAACQ,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;IACvDT,kBAAkB,CAACQ,KAAK,CAACO,cAAc,CAACpD,aAAa,CAAC;EAC1D,CAAC,CAAC,CACGqD,SAAS,CAAC3B,mBAAmB,CAAC;EACnCQ,+BAA+B,CAC1Bc,YAAY,CAAC;IACd,kBAAkB,EAAE,GAAGhD,aAAa;EACxC,CAAC,CAAC,CACGqD,SAAS,CAACvB,cAAc,CAAC;EAC9BK,+BAA+B,CAC1Ba,YAAY,CAAC;IACd,kBAAkB,EAAE,GAAG/C,aAAa;EACxC,CAAC,CAAC,CACGoD,SAAS,CAACpB,cAAc,CAAC;EAC9BvE,aAAa,CAAC4F,YAAY,CAAC,CACvBpB,+BAA+B,EAC/BC,+BAA+B,EAC/BC,2BAA2B,CAC9B,CAAC;AACN,CAAC;AACD,MAAMzD,iBAAiB,GAAGA,CAACjB,aAAa,EAAEC,GAAG,EAAEf,aAAa,EAAE6C,YAAY,EAAE8D,aAAa,EAAE7D,iBAAiB,EAAEJ,aAAa,EAAEC,gBAAgB,EAAEC,iBAAiB,KAAK;EACjK,IAAIG,EAAE,EAAEC,EAAE;EACV;AACJ;AACA;EACI,MAAM4D,QAAQ,GAAG7F,GAAG,GAAG,OAAO,GAAG,MAAM;EACvC,MAAM8F,kBAAkB,GAAG9F,GAAG,GAAG,eAAe4F,aAAa,CAACzD,KAAK,KAAK,GAAG,GAAGyD,aAAa,CAACxD,IAAI,IAAI;EACpG;AACJ;AACA;AACA;AACA;EACI,MAAM2D,iBAAiB,GAAG,KAAK;EAC/B,MAAMC,iBAAiB,GAAG,GAAGJ,aAAa,CAACnC,GAAG,IAAI;EAClD;AACJ;AACA;AACA;AACA;EACI,MAAMwC,8BAA8B,GAAG,CAAC;EACxC,IAAIC,eAAe,GAAGlG,GAAG,GACnB,IAAIsD,MAAM,CAACC,UAAU,GAAG5B,aAAa,CAACQ,KAAK,GAAG8D,8BAA8B,IAAI,GAChF,GAAGtE,aAAa,CAACwE,CAAC,GAAGF,8BAA8B,IAAI;EAC7D;AACJ;AACA;EACI,IAAIxD,YAAY,GAAG,GAAG;EACtB;AACJ;AACA;EACI,MAAM2D,WAAW,GAAG,UAAU;EAC9B;AACJ;AACA;AACA;AACA;EACI,IAAIC,SAAS,GAAG,SAAS5D,YAAY,GAAG;EACxC;EACA,IAAIb,gBAAgB,IAAIC,iBAAiB,EAAE;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;IACQqE,eAAe,GAAGlG,GAAG,GACf,IAAIsD,MAAM,CAACC,UAAU,GAAG1B,iBAAiB,CAACM,KAAK,GAAG8D,8BAA8B,IAAI,GACpF,GAAGpE,iBAAiB,CAACsE,CAAC,GAAGF,8BAA8B,IAAI;IACjE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMrD,0BAA0B,GAAG,CAAC,CAACZ,EAAE,GAAGJ,gBAAgB,CAACiB,WAAW,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAAC,CAAC,OAAO,CAACb,EAAE,GAAGH,YAAY,CAACe,WAAW,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC,CAAC,CAAC;IAC5M,MAAMN,WAAW,GAAGX,iBAAiB,CAACkB,KAAK,GAAGhB,iBAAiB,CAACgB,KAAK;IACrEN,YAAY,GAAGZ,iBAAiB,CAACmB,MAAM,IAAIjB,iBAAiB,CAACiB,MAAM,GAAGC,uBAAuB,CAAC;IAC9F;AACR;AACA;AACA;IACQoD,SAAS,GAAGzD,0BAA0B,GAAG,SAASJ,WAAW,KAAKC,YAAY,GAAG,GAAG,SAASA,YAAY,GAAG;EAChH;EACA;AACJ;AACA;AACA;EACI,MAAM6D,kBAAkB,GAAG3E,aAAa,CAAC8B,GAAG,GAAG9B,aAAa,CAACqB,MAAM,GAAG,CAAC;EACvE,MAAMuD,aAAa,GAAIX,aAAa,CAAC5C,MAAM,GAAGP,YAAY,GAAI,CAAC;EAC/D,MAAM+D,eAAe,GAAG,GAAGF,kBAAkB,GAAGC,aAAa,IAAI;EACjE,MAAME,mBAAmB,GAAG,CACxB;IAAE7C,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE,eAAeqC,eAAe,KAAKM,eAAe,QAAQH,SAAS;EAAG,CAAC,EAC3G;IAAEzC,MAAM,EAAE,GAAG;IAAEK,OAAO,EAAE;EAAE,CAAC,EAC3B;IAAEL,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE,eAAekC,iBAAiB,KAAKC,iBAAiB,QAAQI,WAAW;EAAG,CAAC,CACpH;EACD,MAAMM,kBAAkB,GAAG,CACvB;IACI9C,MAAM,EAAE,CAAC;IACTK,OAAO,EAAE,IAAI;IACbJ,SAAS,EAAE,eAAekC,iBAAiB,KAAKC,iBAAiB,QAAQI,WAAW;EACxF,CAAC,EACD;IAAExC,MAAM,EAAE,GAAG;IAAEK,OAAO,EAAE;EAAE,CAAC,EAC3B;IAAEL,MAAM,EAAE,CAAC;IAAEK,OAAO,EAAE,CAAC;IAAEJ,SAAS,EAAE,eAAeqC,eAAe,KAAKM,eAAe,QAAQH,SAAS;EAAG,CAAC,CAC9G;EACD,MAAMM,SAAS,GAAG1H,aAAa,GAAGwH,mBAAmB,GAAGC,kBAAkB;EAC1E,MAAME,aAAa,GAAGxI,gBAAgB,CAAC,WAAW,CAAC;EACnD,MAAMyI,yBAAyB,GAAG7I,eAAe,CAAC,CAAC;EACnD4I,aAAa,CAACE,SAAS,GAAGhF,YAAY,CAACgF,SAAS;EAChDF,aAAa,CAACG,IAAI,GAAGjF,YAAY,CAACiF,IAAI;EACtCH,aAAa,CAAC5B,KAAK,GAAGlD,YAAY,CAACkD,KAAK;EACxC6B,yBAAyB,CAACzB,UAAU,CAACwB,aAAa,CAAC;EACnDC,yBAAyB,CACpBxB,YAAY,CAAC;IACd,kBAAkB,EAAE,GAAGQ,QAAQ,MAAM;IACrC;AACR;AACA;AACA;AACA;AACA;IACQ7C,MAAM,EAAE,GAAG4C,aAAa,CAAC5C,MAAM,IAAI;IACnCgE,OAAO,EAAE,EAAE;IACX1B,QAAQ,EAAE,UAAU;IACpB,CAACO,QAAQ,GAAGC;EAChB,CAAC,CAAC,CACGP,cAAc,CAAC,MAAM;IACtBzD,YAAY,CAACoD,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC;EAClD,CAAC,CAAC,CACGK,aAAa,CAAC,MAAM;IACrB1D,YAAY,CAACoD,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7CyB,aAAa,CAAC1B,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;EACtD,CAAC,CAAC,CACGO,SAAS,CAACiB,SAAS,CAAC;EACzB5G,aAAa,CAAC4F,YAAY,CAACkB,yBAAyB,CAAC;AACzD,CAAC;AACD,MAAMI,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC5C,IAAInF,EAAE;EACN,IAAI;IACA,MAAMoF,MAAM,GAAG,6BAA6B;IAC5C,MAAMC,OAAO,GAAG,SAAS;IACzB,MAAMC,SAAS,GAAG,WAAW;IAC7B,MAAMC,MAAM,GAAG,IAAI;IACnB,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAMC,KAAK,GAAGP,KAAK,CAACQ,aAAa,CAACC,GAAG,KAAK,KAAK;IAC/C,MAAMC,SAAS,GAAGH,KAAK,GAAG,QAAQ,GAAG,OAAO;IAC5C,MAAMI,QAAQ,GAAGJ,KAAK,GAAG,KAAK,GAAG,MAAM;IACvC,MAAMxH,UAAU,GAAGkH,IAAI,CAAClH,UAAU;IAClC,MAAMC,SAAS,GAAGiH,IAAI,CAACjH,SAAS;IAChC,MAAMjB,aAAa,GAAGkI,IAAI,CAACW,SAAS,KAAK,MAAM;IAC/C,MAAMC,SAAS,GAAG9H,UAAU,CAAC1B,aAAa,CAAC,sBAAsB,CAAC;IAClE,MAAMyJ,SAAS,GAAG/H,UAAU,CAACd,gBAAgB,CAAC,mEAAmE,CAAC;IAClH,MAAM8I,kBAAkB,GAAGhI,UAAU,CAACd,gBAAgB,CAAC,mCAAmC,CAAC;IAC3F,MAAMY,aAAa,GAAG/B,eAAe,CAAC,CAAC;IACvC,MAAMkK,wBAAwB,GAAGlK,eAAe,CAAC,CAAC;IAClD+B,aAAa,CACRqF,UAAU,CAACnF,UAAU,CAAC,CACtBkI,QAAQ,CAAC,CAAC,CAACnG,EAAE,GAAGmF,IAAI,CAACgB,QAAQ,MAAM,IAAI,IAAInG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK7D,QAAQ,CAAC,CAC/EiK,MAAM,CAACjB,IAAI,CAACiB,MAAM,IAAIhB,MAAM,CAAC,CAC7BiB,IAAI,CAAC,MAAM,CAAC,CACZC,iBAAiB,CAAC,oBAAoB,CAAC;IAC5C;IACA,IAAIpI,SAAS,IAAIgH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKqB,SAAS,EAAE;MACpD,MAAMC,iBAAiB,GAAGxK,eAAe,CAAC,CAAC;MAC3CwK,iBAAiB,CAACpD,UAAU,CAAC8B,KAAK,CAAC;MACnCnH,aAAa,CAAC4F,YAAY,CAAC6C,iBAAiB,CAAC;IACjD;IACA,IAAI,CAACT,SAAS,IAAIE,kBAAkB,CAACQ,MAAM,KAAK,CAAC,IAAIT,SAAS,CAACS,MAAM,KAAK,CAAC,EAAE;MACzEP,wBAAwB,CAAC9C,UAAU,CAACnF,UAAU,CAAC1B,aAAa,CAAC,yDAAyD,CAAC,CAAC,CAAC,CAAC;IAC9H,CAAC,MACI;MACD2J,wBAAwB,CAAC9C,UAAU,CAAC2C,SAAS,CAAC,CAAC,CAAC;MAChDG,wBAAwB,CAAC9C,UAAU,CAAC4C,SAAS,CAAC;IAClD;IACAjI,aAAa,CAAC4F,YAAY,CAACuC,wBAAwB,CAAC;IACpD,IAAIjJ,aAAa,EAAE;MACfiJ,wBAAwB,CACnBQ,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAAC,WAAW,EAAE,cAAcd,QAAQ,GAAG,EAAE,cAAcN,MAAM,GAAG,CAAC,CACvEoB,MAAM,CAACtB,OAAO,EAAEG,WAAW,EAAE,CAAC,CAAC;IACxC,CAAC,MACI;MACD;MACAU,wBAAwB,CACnBQ,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAAC,WAAW,EAAE,cAAcf,SAAS,GAAG,EAAE,cAAcL,MAAM,GAAG,CAAC;IACjF;IACA,IAAIQ,SAAS,EAAE;MACX,MAAMa,0BAA0B,GAAGpK,MAAM,CAACuJ,SAAS,CAAC,CAACxJ,aAAa,CAAC,oBAAoB,CAAC;MACxF,IAAIqK,0BAA0B,EAAE;QAC5B,MAAMC,yBAAyB,GAAGD,0BAA0B,CAACrK,aAAa,CAAC,mBAAmB,CAAC;QAC/F,MAAMuK,0BAA0B,GAAGF,0BAA0B,CAACrK,aAAa,CAAC,oBAAoB,CAAC;QACjG,MAAMwK,wBAAwB,GAAG/K,eAAe,CAAC,CAAC;QAClD,MAAMgL,uBAAuB,GAAGhL,eAAe,CAAC,CAAC;QACjD,MAAMiL,wBAAwB,GAAGjL,eAAe,CAAC,CAAC;QAClD+K,wBAAwB,CACnB3D,UAAU,CAACwD,0BAA0B,CAAC,CACtCvD,YAAY,CAAC;UAAEpB,OAAO,EAAE,GAAG;UAAE+C,OAAO,EAAE;QAAQ,CAAC,CAAC,CAChDkC,WAAW,CAAC;UAAEjF,OAAO,EAAE,EAAE;UAAE+C,OAAO,EAAE;QAAG,CAAC,CAAC;QAC9CgC,uBAAuB,CAClB5D,UAAU,CAACyD,yBAAyB,CAAC,CAAC;QAAA,CACtCH,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAACtB,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC;QAC5B4B,wBAAwB,CACnB7D,UAAU,CAAC0D,0BAA0B,CAAC,CAAC;QAAA,CACvCJ,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC;QAC/B0B,wBAAwB,CAACpD,YAAY,CAAC,CAACqD,uBAAuB,EAAEC,wBAAwB,CAAC,CAAC;QAC1Ff,wBAAwB,CAACvC,YAAY,CAAC,CAACoD,wBAAwB,CAAC,CAAC;MACrE;IACJ;IACA,MAAMI,4BAA4B,GAAGlJ,UAAU,CAAC1B,aAAa,CAAC,qCAAqC,CAAC;IACpG,MAAM;MAAEiD,OAAO;MAAEC;IAAS,CAAC,GAAG3B,0BAA0B,CAACC,aAAa,EAAE0H,KAAK,EAAExI,aAAa,EAAEgB,UAAU,EAAEC,SAAS,CAAC;IACpH+H,kBAAkB,CAACmB,OAAO,CAAEC,iBAAiB,IAAK;MAC9C,MAAMC,eAAe,GAAGtL,eAAe,CAAC,CAAC;MACzCsL,eAAe,CAAClE,UAAU,CAACiE,iBAAiB,CAAC;MAC7CtJ,aAAa,CAAC4F,YAAY,CAAC2D,eAAe,CAAC;MAC3C,MAAMC,aAAa,GAAGvL,eAAe,CAAC,CAAC;MACvCuL,aAAa,CAACnE,UAAU,CAACiE,iBAAiB,CAAC9K,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MACxE,MAAMiL,sBAAsB,GAAGxL,eAAe,CAAC,CAAC;MAChD,MAAMoB,OAAO,GAAGqK,KAAK,CAACC,IAAI,CAACL,iBAAiB,CAAClK,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;MAC1F,MAAME,YAAY,GAAGgK,iBAAiB,CAAC/J,OAAO,CAAC,YAAY,CAAC;MAC5D,MAAMqK,cAAc,GAAGtK,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACG,SAAS,CAACC,QAAQ,CAAC,mCAAmC,CAAC;MACvJ,IAAImK,gBAAgB;MACpB,IAAI3K,aAAa,EAAE;QACf2K,gBAAgB,GAAGxK,OAAO,CAACyK,MAAM,CAAEC,MAAM,IAAK;UAC1C,MAAMC,gBAAgB,GAAGD,MAAM,CAACtK,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC;UACtE,OAAQsK,gBAAgB,IAAI,CAACJ,cAAc,IAAK,CAACI,gBAAgB;QACrE,CAAC,CAAC;MACN,CAAC,MACI;QACDH,gBAAgB,GAAGxK,OAAO,CAACyK,MAAM,CAAEC,MAAM,IAAK,CAACA,MAAM,CAACtK,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;MACjG;MACA+J,sBAAsB,CAACpE,UAAU,CAACwE,gBAAgB,CAAC;MACnD,MAAMI,oBAAoB,GAAGhM,eAAe,CAAC,CAAC;MAC9CgM,oBAAoB,CAAC5E,UAAU,CAACiE,iBAAiB,CAAClK,gBAAgB,CAAC,8DAA8D,CAAC,CAAC;MACnI,MAAM8K,iBAAiB,GAAGjM,eAAe,CAAC,CAAC;MAC3CiM,iBAAiB,CAAC7E,UAAU,CAAC5G,MAAM,CAAC6K,iBAAiB,CAAC,CAAC9K,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;MAC9F,MAAM4B,kBAAkB,GAAGnC,eAAe,CAAC,CAAC;MAC5C,MAAM0D,YAAY,GAAG2H,iBAAiB,CAAC9K,aAAa,CAAC,iBAAiB,CAAC;MACvE,IAAImD,YAAY,EAAE;QACdvB,kBAAkB,CAACiF,UAAU,CAAC1D,YAAY,CAAC;MAC/C;MACA4H,eAAe,CAAC3D,YAAY,CAAC,CACzB4D,aAAa,EACbC,sBAAsB,EACtBQ,oBAAoB,EACpBC,iBAAiB,EACjB9J,kBAAkB,CACrB,CAAC;MACFqJ,sBAAsB,CAACb,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;MAC/C2C,oBAAoB,CAACrB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;MAC7C,IAAIpI,aAAa,EAAE;QACf,IAAI,CAAC0K,cAAc,EAAE;UACjBJ,aAAa,CACRZ,MAAM,CAAC,WAAW,EAAE,cAAcd,QAAQ,GAAG,EAAE,cAAcN,MAAM,GAAG,CAAC,CACvEoB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACjC;QACA2C,oBAAoB,CAACrB,MAAM,CAAC,WAAW,EAAE,cAAcd,QAAQ,GAAG,EAAE,cAAcN,MAAM,GAAG,CAAC;QAC5F;QACApH,kBAAkB,CAACwI,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;MAC/C,CAAC,MACI;QACD;QACA,IAAI,CAAC8B,4BAA4B,EAAE;UAC/BI,aAAa,CACRZ,MAAM,CAAC,WAAW,EAAE,cAAcf,SAAS,GAAG,EAAE,cAAcL,MAAM,GAAG,CAAC,CACxEoB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACjC;QACA2C,oBAAoB,CAACrB,MAAM,CAAC,WAAW,EAAE,cAAcf,SAAS,GAAG,EAAE,cAAcL,MAAM,GAAG,CAAC;QAC7F0C,iBAAiB,CAACvB,iBAAiB,CAAC,CAACrB,OAAO,EAAE,WAAW,CAAC,CAAC;QAC3D,MAAM6C,iBAAiB,GAAG7K,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC8K,WAAW;QAC9G,IAAI,CAACD,iBAAiB,EAAE;UACpBD,iBAAiB,CAACtB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC;QAC7D,CAAC,MACI;UACD4C,iBAAiB,CAACtB,MAAM,CAAC,WAAW,EAAElB,KAAK,GAAG,mBAAmB,GAAG,kBAAkB,EAAE,iBAAiB,CAAC;QAC9G;QACA;QACA,IAAI,CAACjG,OAAO,EAAE;UACVrB,kBAAkB,CAACwI,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C;QACA,IAAI3F,YAAY,IAAI,CAACF,OAAO,EAAE;UAC1B,MAAM4I,mBAAmB,GAAGpM,eAAe,CAAC,CAAC;UAC7CoM,mBAAmB,CACdhF,UAAU,CAAC5G,MAAM,CAACkD,YAAY,CAAC,CAACnD,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;UAAA,CAC/DoK,MAAM,CAAC,WAAW,EAAElB,KAAK,GAAG,oBAAoB,GAAG,mBAAmB,EAAE,iBAAiB,CAAC;UAC/F6B,eAAe,CAAC3D,YAAY,CAACyE,mBAAmB,CAAC;QACrD;MACJ;IACJ,CAAC,CAAC;IACF;IACA,IAAIlK,SAAS,EAAE;MACX,MAAMmK,cAAc,GAAGrM,eAAe,CAAC,CAAC;MACxC,MAAMsM,gBAAgB,GAAGpK,SAAS,CAAC3B,aAAa,CAAC,sBAAsB,CAAC;MACxE,MAAMgM,iBAAiB,GAAGrK,SAAS,CAACf,gBAAgB,CAAC,mCAAmC,CAAC;MACzF,MAAMqL,gBAAgB,GAAGtK,SAAS,CAACf,gBAAgB,CAAC,mEAAmE,CAAC;MACxH,IAAI,CAACmL,gBAAgB,IAAIC,iBAAiB,CAAC9B,MAAM,KAAK,CAAC,IAAI+B,gBAAgB,CAAC/B,MAAM,KAAK,CAAC,EAAE;QACtF4B,cAAc,CAACjF,UAAU,CAAClF,SAAS,CAAC3B,aAAa,CAAC,yDAAyD,CAAC,CAAC,CAAC,CAAC;MACnH,CAAC,MACI;QACD8L,cAAc,CAACjF,UAAU,CAACkF,gBAAgB,CAAC,CAAC,CAAC;QAC7CD,cAAc,CAACjF,UAAU,CAACoF,gBAAgB,CAAC;MAC/C;MACAzK,aAAa,CAAC4F,YAAY,CAAC0E,cAAc,CAAC;MAC1C,IAAIpL,aAAa,EAAE;QACf;QACAoL,cAAc,CACT3B,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAEE,KAAK,GAAG,mBAAmB,GAAG,kBAAkB,CAAC;QACnG,MAAMgD,WAAW,GAAGvM,iBAAiB,CAACgC,SAAS,CAAC;QAChDH,aAAa,CAACyF,aAAa,CAAC,MAAM;UAC9B,IAAIzF,aAAa,CAAC2K,YAAY,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC3CD,WAAW,CAACvF,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;UACpD;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD;QACAkF,cAAc,CACT1B,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAE,cAAcM,QAAQ,GAAG,CAAC,CACvEc,MAAM,CAACtB,OAAO,EAAE,CAAC,EAAEG,WAAW,CAAC;MACxC;MACA,IAAI8C,gBAAgB,EAAE;QAClB,MAAMK,yBAAyB,GAAGnM,MAAM,CAAC8L,gBAAgB,CAAC,CAAC/L,aAAa,CAAC,oBAAoB,CAAC;QAC9F,IAAIoM,yBAAyB,EAAE;UAC3B,MAAMC,wBAAwB,GAAGD,yBAAyB,CAACpM,aAAa,CAAC,mBAAmB,CAAC;UAC7F,MAAMsM,yBAAyB,GAAGF,yBAAyB,CAACpM,aAAa,CAAC,oBAAoB,CAAC;UAC/F,MAAMuM,uBAAuB,GAAG9M,eAAe,CAAC,CAAC;UACjD,MAAM+M,sBAAsB,GAAG/M,eAAe,CAAC,CAAC;UAChD,MAAMgN,uBAAuB,GAAGhN,eAAe,CAAC,CAAC;UACjD8M,uBAAuB,CAClB1F,UAAU,CAACuF,yBAAyB,CAAC,CACrCtF,YAAY,CAAC;YAAEpB,OAAO,EAAE,GAAG;YAAE+C,OAAO,EAAE;UAAQ,CAAC,CAAC,CAChDkC,WAAW,CAAC;YAAEjF,OAAO,EAAE,EAAE;YAAE+C,OAAO,EAAE;UAAG,CAAC,CAAC;UAC9C+D,sBAAsB,CACjB3F,UAAU,CAACwF,wBAAwB,CAAC,CAAC;UAAA,CACrClC,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAACtB,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;UAC5B2D,uBAAuB,CAClB5F,UAAU,CAACyF,yBAAyB,CAAC,CAAC;UAAA,CACtCnC,iBAAiB,CAAC,CAACrB,OAAO,CAAC,CAAC,CAC5BsB,MAAM,CAACtB,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;UAC/ByD,uBAAuB,CAACnF,YAAY,CAAC,CAACoF,sBAAsB,EAAEC,uBAAuB,CAAC,CAAC;UACvFX,cAAc,CAAC1E,YAAY,CAAC,CAACmF,uBAAuB,CAAC,CAAC;QAC1D;MACJ;MACAP,iBAAiB,CAACnB,OAAO,CAAE6B,gBAAgB,IAAK;QAC5C,MAAMC,cAAc,GAAGlN,eAAe,CAAC,CAAC;QACxCkN,cAAc,CAAC9F,UAAU,CAAC6F,gBAAgB,CAAC;QAC3C,MAAME,YAAY,GAAGnN,eAAe,CAAC,CAAC;QACtCmN,YAAY,CAAC/F,UAAU,CAAC6F,gBAAgB,CAAC1M,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM6M,qBAAqB,GAAGpN,eAAe,CAAC,CAAC;QAC/C,MAAMoB,OAAO,GAAG6L,gBAAgB,CAAC9L,gBAAgB,CAAC,0BAA0B,CAAC;QAC7E,MAAME,YAAY,GAAG4L,gBAAgB,CAAC3L,OAAO,CAAC,YAAY,CAAC;QAC3D,MAAMqK,cAAc,GAAGtK,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACG,SAAS,CAACC,QAAQ,CAAC,mCAAmC,CAAC;QACvJ,MAAMmK,gBAAgB,GAAGH,KAAK,CAACC,IAAI,CAACtK,OAAO,CAAC,CAACyK,MAAM,CAAEC,MAAM,IAAK;UAC5D,MAAMC,gBAAgB,GAAGD,MAAM,CAACtK,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC;UACtE,OAAQsK,gBAAgB,IAAI,CAACJ,cAAc,IAAK,CAACI,gBAAgB;QACrE,CAAC,CAAC;QACFqB,qBAAqB,CAAChG,UAAU,CAACwE,gBAAgB,CAAC;QAClD,MAAMyB,mBAAmB,GAAGrN,eAAe,CAAC,CAAC;QAC7C,MAAMsN,qBAAqB,GAAGL,gBAAgB,CAAC9L,gBAAgB,CAAC,8DAA8D,CAAC;QAC/H,IAAImM,qBAAqB,CAAC7C,MAAM,GAAG,CAAC,EAAE;UAClC4C,mBAAmB,CAACjG,UAAU,CAACkG,qBAAqB,CAAC;QACzD;QACA,MAAMC,gBAAgB,GAAGvN,eAAe,CAAC,CAAC;QAC1CuN,gBAAgB,CAACnG,UAAU,CAAC5G,MAAM,CAACyM,gBAAgB,CAAC,CAAC1M,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC5F,MAAM+B,iBAAiB,GAAGtC,eAAe,CAAC,CAAC;QAC3C,MAAM0D,YAAY,GAAGuJ,gBAAgB,CAAC1M,aAAa,CAAC,iBAAiB,CAAC;QACtE,IAAImD,YAAY,EAAE;UACdpB,iBAAiB,CAAC8E,UAAU,CAAC1D,YAAY,CAAC;QAC9C;QACAwJ,cAAc,CAACvF,YAAY,CAAC,CACxBwF,YAAY,EACZC,qBAAqB,EACrBC,mBAAmB,EACnB/K,iBAAiB,EACjBiL,gBAAgB,CACnB,CAAC;QACFxL,aAAa,CAAC4F,YAAY,CAACuF,cAAc,CAAC;QAC1C;QACA5K,iBAAiB,CAACqI,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1C+D,qBAAqB,CAACzC,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9CgE,mBAAmB,CAAC1C,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5C,IAAIpI,aAAa,EAAE;UACf,IAAI,CAAC0K,cAAc,EAAE;YACjB;YACAwB,YAAY,CACPxC,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAEE,KAAK,GAAG,mBAAmB,GAAG,kBAAkB,CAAC,CAC9FkB,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;UACjC;UACAgE,mBAAmB,CAAC1C,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAEE,KAAK,GAAG,mBAAmB,GAAG,kBAAkB,CAAC;UAClH8D,gBAAgB,CAAC7C,iBAAiB,CAAC,CAACrB,OAAO,EAAE,WAAW,CAAC,CAAC;UAC1D;UACA;UACA,MAAM6C,iBAAiB,GAAG7K,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC8K,WAAW;UAC9G,IAAI,CAACD,iBAAiB,EAAE;YACpBqB,gBAAgB,CAAC5C,MAAM,CAACtB,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;UACzD,CAAC,MACI;YACDkE,gBAAgB,CAAC5C,MAAM,CAAC,WAAW,EAAE,iBAAiB,EAAElB,KAAK,GAAG,mBAAmB,GAAG,kBAAkB,CAAC;UAC7G;UACA,IAAI/F,YAAY,IAAI,CAACD,QAAQ,EAAE;YAC3B,MAAM+J,kBAAkB,GAAGxN,eAAe,CAAC,CAAC;YAC5CwN,kBAAkB,CACbpG,UAAU,CAAC5G,MAAM,CAACkD,YAAY,CAAC,CAACnD,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;YAAA,CAC/DoK,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAE,cAAc,CAACE,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;YAC/FyD,cAAc,CAACvF,YAAY,CAAC6F,kBAAkB,CAAC;UACnD;QACJ,CAAC,MACI;UACD;UACA,IAAI,CAAC7B,cAAc,EAAE;YACjBwB,YAAY,CACPxC,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAE,cAAcM,QAAQ,GAAG,CAAC,CACvEc,MAAM,CAACtB,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CACxBoE,gBAAgB,CAAC,CAACnE,SAAS,EAAED,OAAO,CAAC,CAAC;UAC/C;UACAgE,mBAAmB,CACd1C,MAAM,CAAC,WAAW,EAAE,cAAcpB,MAAM,GAAG,EAAE,cAAcM,QAAQ,GAAG,CAAC,CACvE4D,gBAAgB,CAAC,CAACnE,SAAS,EAAED,OAAO,CAAC,CAAC;UAC3C/G,iBAAiB,CAACmL,gBAAgB,CAAC,CAACpE,OAAO,CAAC,CAAC;UAC7C8D,YAAY,CAACM,gBAAgB,CAAC,CAACpE,OAAO,CAAC,CAAC;UACxC+D,qBAAqB,CAACK,gBAAgB,CAAC,CAACpE,OAAO,CAAC,CAAC;QACrD;MACJ,CAAC,CAAC;IACN;IACA,OAAOtH,aAAa;EACxB,CAAC,CACD,OAAO2L,GAAG,EAAE;IACR,MAAMA,GAAG;EACb;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMzI,uBAAuB,GAAG,EAAE;AAElC,SAASgE,sBAAsB,EAAEzI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}