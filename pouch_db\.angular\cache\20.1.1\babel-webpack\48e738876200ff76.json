{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n(function () {\n  if (\"undefined\" !== typeof window && void 0 !== window.Reflect && void 0 !== window.customElements) {\n    var a = HTMLElement;\n    window.HTMLElement = function () {\n      return Reflect.construct(a, [], this.constructor);\n    };\n    HTMLElement.prototype = a.prototype;\n    HTMLElement.prototype.constructor = HTMLElement;\n    Object.setPrototypeOf(HTMLElement, a);\n  }\n})();\nexport * from '../dist/esm/loader.js';", "map": {"version": 3, "names": ["window", "Reflect", "customElements", "a", "HTMLElement", "construct", "constructor", "prototype", "Object", "setPrototypeOf"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/loader/index.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n(function(){if(\"undefined\"!==typeof window&&void 0!==window.Reflect&&void 0!==window.customElements){var a=HTMLElement;window.HTMLElement=function(){return Reflect.construct(a,[],this.constructor)};HTMLElement.prototype=a.prototype;HTMLElement.prototype.constructor=HTMLElement;Object.setPrototypeOf(HTMLElement,a)}})();\nexport * from '../dist/esm/loader.js';"], "mappings": "AAAA;AACA;AACA;AACA,CAAC,YAAU;EAAC,IAAG,WAAW,KAAG,OAAOA,MAAM,IAAE,KAAK,CAAC,KAAGA,MAAM,CAACC,OAAO,IAAE,KAAK,CAAC,KAAGD,MAAM,CAACE,cAAc,EAAC;IAAC,IAAIC,CAAC,GAACC,WAAW;IAACJ,MAAM,CAACI,WAAW,GAAC,YAAU;MAAC,OAAOH,OAAO,CAACI,SAAS,CAACF,CAAC,EAAC,EAAE,EAAC,IAAI,CAACG,WAAW,CAAC;IAAA,CAAC;IAACF,WAAW,CAACG,SAAS,GAACJ,CAAC,CAACI,SAAS;IAACH,WAAW,CAACG,SAAS,CAACD,WAAW,GAACF,WAAW;IAACI,MAAM,CAACC,cAAc,CAACL,WAAW,EAACD,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AAC/T,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}