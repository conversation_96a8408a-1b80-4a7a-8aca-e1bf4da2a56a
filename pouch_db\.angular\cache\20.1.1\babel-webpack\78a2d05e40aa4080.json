{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.log(err));", "map": {"version": 3, "names": ["AppModule", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err", "console", "log"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\main.ts"], "sourcesContent": ["import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.log(err));\n"], "mappings": ";AAEA,SAASA,SAAS,QAAQ,kBAAkB;AAE5CC,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACH,SAAS,CAAC,CAChDI,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}