{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Creates a lock controller.\n *\n * Claiming a lock means that nothing else can acquire the lock until it is released.\n * This can momentarily prevent execution of code that needs to wait for the earlier code to finish.\n * For example, this can be used to prevent multiple transitions from occurring at the same time.\n */\nconst createLockController = () => {\n  let waitPromise;\n  /**\n   * When lock() is called, the lock is claimed.\n   * Once a lock has been claimed, it cannot be claimed again until it is released.\n   * When this function gets resolved, the lock is released, allowing it to be claimed again.\n   *\n   * @example ```tsx\n   * const unlock = await this.lockController.lock();\n   * // do other stuff\n   * unlock();\n   * ```\n   */\n  const lock = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* () {\n      const p = waitPromise;\n      let resolve;\n      waitPromise = new Promise(r => resolve = r);\n      if (p !== undefined) {\n        yield p;\n      }\n      return resolve;\n    });\n    return function lock() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  return {\n    lock\n  };\n};\nexport { createLockController as c };", "map": {"version": 3, "names": ["createLockController", "waitPromise", "lock", "_ref", "_asyncToGenerator", "p", "resolve", "Promise", "r", "undefined", "apply", "arguments", "c"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/lock-controller-B-hirT0v.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Creates a lock controller.\n *\n * Claiming a lock means that nothing else can acquire the lock until it is released.\n * This can momentarily prevent execution of code that needs to wait for the earlier code to finish.\n * For example, this can be used to prevent multiple transitions from occurring at the same time.\n */\nconst createLockController = () => {\n    let waitPromise;\n    /**\n     * When lock() is called, the lock is claimed.\n     * Once a lock has been claimed, it cannot be claimed again until it is released.\n     * When this function gets resolved, the lock is released, allowing it to be claimed again.\n     *\n     * @example ```tsx\n     * const unlock = await this.lockController.lock();\n     * // do other stuff\n     * unlock();\n     * ```\n     */\n    const lock = async () => {\n        const p = waitPromise;\n        let resolve;\n        waitPromise = new Promise((r) => (resolve = r));\n        if (p !== undefined) {\n            await p;\n        }\n        return resolve;\n    };\n    return {\n        lock,\n    };\n};\n\nexport { createLockController as c };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,IAAIC,WAAW;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,IAAI;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;MACrB,MAAMC,CAAC,GAAGJ,WAAW;MACrB,IAAIK,OAAO;MACXL,WAAW,GAAG,IAAIM,OAAO,CAAEC,CAAC,IAAMF,OAAO,GAAGE,CAAE,CAAC;MAC/C,IAAIH,CAAC,KAAKI,SAAS,EAAE;QACjB,MAAMJ,CAAC;MACX;MACA,OAAOC,OAAO;IAClB,CAAC;IAAA,gBARKJ,IAAIA,CAAA;MAAA,OAAAC,IAAA,CAAAO,KAAA,OAAAC,SAAA;IAAA;EAAA,GAQT;EACD,OAAO;IACHT;EACJ,CAAC;AACL,CAAC;AAED,SAASF,oBAAoB,IAAIY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}