{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n  let first;\n  let each;\n  let _with;\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n  if (isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n  if (withObservable) {\n    _with = () => withObservable;\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return timeout({\n    first,\n    each,\n    scheduler,\n    with: _with\n  });\n}", "map": {"version": 3, "names": ["async", "isValidDate", "timeout", "timeoutWith", "due", "withObservable", "scheduler", "first", "each", "_with", "TypeError", "with"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/rxjs/dist/esm/internal/operators/timeoutWith.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n    let first;\n    let each;\n    let _with;\n    scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n    if (isValidDate(due)) {\n        first = due;\n    }\n    else if (typeof due === 'number') {\n        each = due;\n    }\n    if (withObservable) {\n        _with = () => withObservable;\n    }\n    else {\n        throw new TypeError('No observable provided to switch to');\n    }\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return timeout({\n        first,\n        each,\n        scheduler,\n        with: _with,\n    });\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACxD,IAAIC,KAAK;EACT,IAAIC,IAAI;EACR,IAAIC,KAAK;EACTH,SAAS,GAAGA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGN,KAAK;EAC1E,IAAIC,WAAW,CAACG,GAAG,CAAC,EAAE;IAClBG,KAAK,GAAGH,GAAG;EACf,CAAC,MACI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC9BI,IAAI,GAAGJ,GAAG;EACd;EACA,IAAIC,cAAc,EAAE;IAChBI,KAAK,GAAGA,CAAA,KAAMJ,cAAc;EAChC,CAAC,MACI;IACD,MAAM,IAAIK,SAAS,CAAC,qCAAqC,CAAC;EAC9D;EACA,IAAIH,KAAK,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAC/B,MAAM,IAAIE,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAOR,OAAO,CAAC;IACXK,KAAK;IACLC,IAAI;IACJF,SAAS;IACTK,IAAI,EAAEF;EACV,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}