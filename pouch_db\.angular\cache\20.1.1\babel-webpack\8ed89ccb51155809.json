{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}", "map": {"version": 3, "names": ["not", "filter", "innerFrom", "partition", "source", "predicate", "thisArg"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/rxjs/dist/esm/internal/observable/partition.js"], "sourcesContent": ["import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n    return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAClD,OAAO,CAACL,MAAM,CAACI,SAAS,EAAEC,OAAO,CAAC,CAACJ,SAAS,CAACE,MAAM,CAAC,CAAC,EAAEH,MAAM,CAACD,GAAG,CAACK,SAAS,EAAEC,OAAO,CAAC,CAAC,CAACJ,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}