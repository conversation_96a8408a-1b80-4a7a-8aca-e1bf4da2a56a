{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n  let notchVisibilityIO;\n  const needsExplicitNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (\n    /**\n     * If the notch is not being used\n     * then we do not need to set the notch width.\n     */\n    notchSpacerEl === undefined ||\n    /**\n     * If either the label property is being\n     * used or the label slot is not defined,\n     * then we do not need to estimate the notch width.\n     */\n    el.label !== undefined || getLabelSlot() === null) {\n      return false;\n    }\n    return true;\n  };\n  const calculateNotchWidth = () => {\n    if (needsExplicitNotchWidth()) {\n      /**\n       * Run this the frame after\n       * the browser has re-painted the host element.\n       * Otherwise, the label element may have a width\n       * of 0 and the IntersectionObserver will be used.\n       */\n      raf(() => {\n        setNotchWidth();\n      });\n    }\n  };\n  /**\n   * When using a label prop we can render\n   * the label value inside of the notch and\n   * let the browser calculate the size of the notch.\n   * However, we cannot render the label slot in multiple\n   * places so we need to manually calculate the notch dimension\n   * based on the size of the slotted content.\n   *\n   * This function should only be used to set the notch width\n   * on slotted label content. The notch width for label prop\n   * content is automatically calculated based on the\n   * intrinsic size of the label text.\n   */\n  const setNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (notchSpacerEl === undefined) {\n      return;\n    }\n    if (!needsExplicitNotchWidth()) {\n      notchSpacerEl.style.removeProperty('width');\n      return;\n    }\n    const width = getLabelSlot().scrollWidth;\n    if (\n    /**\n     * If the computed width of the label is 0\n     * and notchSpacerEl's offsetParent is null\n     * then that means the element is hidden.\n     * As a result, we need to wait for the element\n     * to become visible before setting the notch width.\n     *\n     * We do not check el.offsetParent because\n     * that can be null if the host element has\n     * position: fixed applied to it.\n     * notchSpacerEl does not have position: fixed.\n     */\n    width === 0 && notchSpacerEl.offsetParent === null && win !== undefined && 'IntersectionObserver' in win) {\n      /**\n       * If there is an IO already attached\n       * then that will update the notch\n       * once the element becomes visible.\n       * As a result, there is no need to create\n       * another one.\n       */\n      if (notchVisibilityIO !== undefined) {\n        return;\n      }\n      const io = notchVisibilityIO = new IntersectionObserver(ev => {\n        /**\n         * If the element is visible then we\n         * can try setting the notch width again.\n         */\n        if (ev[0].intersectionRatio === 1) {\n          setNotchWidth();\n          io.disconnect();\n          notchVisibilityIO = undefined;\n        }\n      },\n      /**\n       * Set the root to be the host element\n       * This causes the IO callback\n       * to be fired in WebKit as soon as the element\n       * is visible. If we used the default root value\n       * then WebKit would only fire the IO callback\n       * after any animations (such as a modal transition)\n       * finished, and there would potentially be a flicker.\n       */\n      {\n        threshold: 0.01,\n        root: el\n      });\n      io.observe(notchSpacerEl);\n      return;\n    }\n    /**\n     * If the element is visible then we can set the notch width.\n     * The notch is only visible when the label is scaled,\n     * which is why we multiply the width by 0.75 as this is\n     * the same amount the label element is scaled by in the host CSS.\n     * (See $form-control-label-stacked-scale in ionic.globals.scss).\n     */\n    notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n  };\n  const destroy = () => {\n    if (notchVisibilityIO) {\n      notchVisibilityIO.disconnect();\n      notchVisibilityIO = undefined;\n    }\n  };\n  return {\n    calculateNotchWidth,\n    destroy\n  };\n};\nexport { createNotchController as c };", "map": {"version": 3, "names": ["w", "win", "r", "raf", "createNotchController", "el", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "notchSpacerEl", "undefined", "label", "calculateNotchWidth", "set<PERSON><PERSON>chWidth", "style", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "ev", "intersectionRatio", "disconnect", "threshold", "root", "observe", "setProperty", "destroy", "c"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/notch-controller-C5LPspO8.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n    let notchVisibilityIO;\n    const needsExplicitNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (\n        /**\n         * If the notch is not being used\n         * then we do not need to set the notch width.\n         */\n        notchSpacerEl === undefined ||\n            /**\n             * If either the label property is being\n             * used or the label slot is not defined,\n             * then we do not need to estimate the notch width.\n             */\n            el.label !== undefined ||\n            getLabelSlot() === null) {\n            return false;\n        }\n        return true;\n    };\n    const calculateNotchWidth = () => {\n        if (needsExplicitNotchWidth()) {\n            /**\n             * Run this the frame after\n             * the browser has re-painted the host element.\n             * Otherwise, the label element may have a width\n             * of 0 and the IntersectionObserver will be used.\n             */\n            raf(() => {\n                setNotchWidth();\n            });\n        }\n    };\n    /**\n     * When using a label prop we can render\n     * the label value inside of the notch and\n     * let the browser calculate the size of the notch.\n     * However, we cannot render the label slot in multiple\n     * places so we need to manually calculate the notch dimension\n     * based on the size of the slotted content.\n     *\n     * This function should only be used to set the notch width\n     * on slotted label content. The notch width for label prop\n     * content is automatically calculated based on the\n     * intrinsic size of the label text.\n     */\n    const setNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (notchSpacerEl === undefined) {\n            return;\n        }\n        if (!needsExplicitNotchWidth()) {\n            notchSpacerEl.style.removeProperty('width');\n            return;\n        }\n        const width = getLabelSlot().scrollWidth;\n        if (\n        /**\n         * If the computed width of the label is 0\n         * and notchSpacerEl's offsetParent is null\n         * then that means the element is hidden.\n         * As a result, we need to wait for the element\n         * to become visible before setting the notch width.\n         *\n         * We do not check el.offsetParent because\n         * that can be null if the host element has\n         * position: fixed applied to it.\n         * notchSpacerEl does not have position: fixed.\n         */\n        width === 0 &&\n            notchSpacerEl.offsetParent === null &&\n            win !== undefined &&\n            'IntersectionObserver' in win) {\n            /**\n             * If there is an IO already attached\n             * then that will update the notch\n             * once the element becomes visible.\n             * As a result, there is no need to create\n             * another one.\n             */\n            if (notchVisibilityIO !== undefined) {\n                return;\n            }\n            const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n                /**\n                 * If the element is visible then we\n                 * can try setting the notch width again.\n                 */\n                if (ev[0].intersectionRatio === 1) {\n                    setNotchWidth();\n                    io.disconnect();\n                    notchVisibilityIO = undefined;\n                }\n            }, \n            /**\n             * Set the root to be the host element\n             * This causes the IO callback\n             * to be fired in WebKit as soon as the element\n             * is visible. If we used the default root value\n             * then WebKit would only fire the IO callback\n             * after any animations (such as a modal transition)\n             * finished, and there would potentially be a flicker.\n             */\n            { threshold: 0.01, root: el }));\n            io.observe(notchSpacerEl);\n            return;\n        }\n        /**\n         * If the element is visible then we can set the notch width.\n         * The notch is only visible when the label is scaled,\n         * which is why we multiply the width by 0.75 as this is\n         * the same amount the label element is scaled by in the host CSS.\n         * (See $form-control-label-stacked-scale in ionic.globals.scss).\n         */\n        notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n    };\n    const destroy = () => {\n        if (notchVisibilityIO) {\n            notchVisibilityIO.disconnect();\n            notchVisibilityIO = undefined;\n        }\n    };\n    return {\n        calculateNotchWidth,\n        destroy,\n    };\n};\n\nexport { createNotchController as c };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,GAAG,QAAQ,uBAAuB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACC,EAAE,EAAEC,gBAAgB,EAAEC,YAAY,KAAK;EAClE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;IACQI,aAAa,KAAKC,SAAS;IACvB;AACZ;AACA;AACA;AACA;IACYN,EAAE,CAACO,KAAK,KAAKD,SAAS,IACtBJ,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIJ,uBAAuB,CAAC,CAAC,EAAE;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACYN,GAAG,CAAC,MAAM;QACNW,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMA,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMJ,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC,IAAII,aAAa,KAAKC,SAAS,EAAE;MAC7B;IACJ;IACA,IAAI,CAACF,uBAAuB,CAAC,CAAC,EAAE;MAC5BC,aAAa,CAACK,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC;MAC3C;IACJ;IACA,MAAMC,KAAK,GAAGV,YAAY,CAAC,CAAC,CAACW,WAAW;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,KAAK,KAAK,CAAC,IACPP,aAAa,CAACS,YAAY,KAAK,IAAI,IACnClB,GAAG,KAAKU,SAAS,IACjB,sBAAsB,IAAIV,GAAG,EAAE;MAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIO,iBAAiB,KAAKG,SAAS,EAAE;QACjC;MACJ;MACA,MAAMS,EAAE,GAAIZ,iBAAiB,GAAG,IAAIa,oBAAoB,CAAEC,EAAE,IAAK;QAC7D;AAChB;AACA;AACA;QACgB,IAAIA,EAAE,CAAC,CAAC,CAAC,CAACC,iBAAiB,KAAK,CAAC,EAAE;UAC/BT,aAAa,CAAC,CAAC;UACfM,EAAE,CAACI,UAAU,CAAC,CAAC;UACfhB,iBAAiB,GAAGG,SAAS;QACjC;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;QAAEc,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAErB;MAAG,CAAC,CAAE;MAC/Be,EAAE,CAACO,OAAO,CAACjB,aAAa,CAAC;MACzB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,aAAa,CAACK,KAAK,CAACa,WAAW,CAAC,OAAO,EAAE,GAAGX,KAAK,GAAG,IAAI,IAAI,CAAC;EACjE,CAAC;EACD,MAAMY,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIrB,iBAAiB,EAAE;MACnBA,iBAAiB,CAACgB,UAAU,CAAC,CAAC;MAC9BhB,iBAAiB,GAAGG,SAAS;IACjC;EACJ,CAAC;EACD,OAAO;IACHE,mBAAmB;IACnBgB;EACJ,CAAC;AACL,CAAC;AAED,SAASzB,qBAAqB,IAAI0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}