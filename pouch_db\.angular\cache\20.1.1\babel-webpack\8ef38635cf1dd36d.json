{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\nconst _c0 = [\"tabsInner\"];\nclass MenuController {\n  menuController;\n  constructor(menuController) {\n    this.menuController = menuController;\n  }\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId) {\n    return this.menuController.open(menuId);\n  }\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId) {\n    return this.menuController.close(menuId);\n  }\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId) {\n    return this.menuController.toggle(menuId);\n  }\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable, menuId) {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable, menuId) {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId) {\n    return this.menuController.isOpen(menuId);\n  }\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId) {\n    return this.menuController.isEnabled(menuId);\n  }\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId) {\n    return this.menuController.get(menuId);\n  }\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen() {\n    return this.menuController.getOpen();\n  }\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus() {\n    return this.menuController.getMenus();\n  }\n  registerAnimation(name, animation) {\n    return this.menuController.registerAnimation(name, animation);\n  }\n  isAnimating() {\n    return this.menuController.isAnimating();\n  }\n  _getOpenSync() {\n    return this.menuController._getOpenSync();\n  }\n  _createAnimation(type, menuCmp) {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n  _register(menu) {\n    return this.menuController._register(menu);\n  }\n  _unregister(menu) {\n    return this.menuController._unregister(menu);\n  }\n  _setOpen(menu, shouldOpen, animated) {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\nclass DomController {\n  /**\n   * Schedules a task to run during the READ phase of the next frame.\n   * This task should only read the DOM, but never modify it.\n   */\n  read(cb) {\n    getQueue().read(cb);\n  }\n  /**\n   * Schedules a task to run during the WRITE phase of the next frame.\n   * This task should write the DOM, but never READ it.\n   */\n  write(cb) {\n    getQueue().write(cb);\n  }\n  /** @nocollapse */\n  static ɵfac = function DomController_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomController)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomController,\n    factory: DomController.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst getQueue = () => {\n  const win = typeof window !== 'undefined' ? window : null;\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic?.queue) {\n      return Ionic.queue;\n    }\n    return {\n      read: cb => win.requestAnimationFrame(cb),\n      write: cb => win.requestAnimationFrame(cb)\n    };\n  }\n  return {\n    read: cb => cb(),\n    write: cb => cb()\n  };\n};\nclass Platform {\n  doc;\n  _readyPromise;\n  win;\n  /**\n   * @hidden\n   */\n  backButton = new Subject();\n  /**\n   * The keyboardDidShow event emits when the\n   * on-screen keyboard is presented.\n   */\n  keyboardDidShow = new Subject();\n  /**\n   * The keyboardDidHide event emits when the\n   * on-screen keyboard is hidden.\n   */\n  keyboardDidHide = new Subject();\n  /**\n   * The pause event emits when the native platform puts the application\n   * into the background, typically when the user switches to a different\n   * application. This event would emit when a Cordova app is put into\n   * the background, however, it would not fire on a standard web browser.\n   */\n  pause = new Subject();\n  /**\n   * The resume event emits when the native platform pulls the application\n   * out from the background. This event would emit when a Cordova app comes\n   * out from the background, however, it would not fire on a standard web browser.\n   */\n  resume = new Subject();\n  /**\n   * The resize event emits when the browser window has changed dimensions. This\n   * could be from a browser window being physically resized, or from a device\n   * changing orientation.\n   */\n  resize = new Subject();\n  constructor(doc, zone) {\n    this.doc = doc;\n    zone.run(() => {\n      this.win = doc.defaultView;\n      this.backButton.subscribeWithPriority = function (priority, callback) {\n        return this.subscribe(ev => {\n          return ev.register(priority, processNextHandler => zone.run(() => callback(processNextHandler)));\n        });\n      };\n      proxyEvent(this.pause, doc, 'pause', zone);\n      proxyEvent(this.resume, doc, 'resume', zone);\n      proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n      proxyEvent(this.resize, this.win, 'resize', zone);\n      proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n      proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n      let readyResolve;\n      this._readyPromise = new Promise(res => {\n        readyResolve = res;\n      });\n      if (this.win?.['cordova']) {\n        doc.addEventListener('deviceready', () => {\n          readyResolve('cordova');\n        }, {\n          once: true\n        });\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        readyResolve('dom');\n      }\n    });\n  }\n  /**\n   * @returns returns true/false based on platform.\n   * @description\n   * Depending on the platform the user is on, `is(platformName)` will\n   * return `true` or `false`. Note that the same app can return `true`\n   * for more than one platform name. For example, an app running from\n   * an iPad would return `true` for the platform names: `mobile`,\n   * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n   * from Cordova then `cordova` would be true, and if it was running\n   * from a web browser on the iPad then `mobileweb` would be `true`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     if (this.platform.is('ios')) {\n   *       // This will only print when on iOS\n   *       console.log('I am an iOS device!');\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * | Platform Name   | Description                        |\n   * |-----------------|------------------------------------|\n   * | android         | on a device running Android.       |\n   * | capacitor       | on a device running Capacitor.     |\n   * | cordova         | on a device running Cordova.       |\n   * | ios             | on a device running iOS.           |\n   * | ipad            | on an iPad device.                 |\n   * | iphone          | on an iPhone device.               |\n   * | phablet         | on a phablet device.               |\n   * | tablet          | on a tablet device.                |\n   * | electron        | in Electron on a desktop device.   |\n   * | pwa             | as a PWA app.                      |\n   * | mobile          | on a mobile device.                |\n   * | mobileweb       | on a mobile device in a browser.   |\n   * | desktop         | on a desktop device.               |\n   * | hybrid          | is a cordova or capacitor app.     |\n   *\n   */\n  is(platformName) {\n    return isPlatform(this.win, platformName);\n  }\n  /**\n   * @returns the array of platforms\n   * @description\n   * Depending on what device you are on, `platforms` can return multiple values.\n   * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n   * it would return `mobile`, `ios`, and `iphone`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     // This will print an array of the current platforms\n   *     console.log(this.platform.platforms());\n   *   }\n   * }\n   * ```\n   */\n  platforms() {\n    return getPlatforms(this.win);\n  }\n  /**\n   * Returns a promise when the platform is ready and native functionality\n   * can be called. If the app is running from within a web browser, then\n   * the promise will resolve when the DOM is ready. When the app is running\n   * from an application engine such as Cordova, then the promise will\n   * resolve when Cordova triggers the `deviceready` event.\n   *\n   * The resolved value is the `readySource`, which states which platform\n   * ready was used. For example, when Cordova is ready, the resolved ready\n   * source is `cordova`. The default ready source value will be `dom`. The\n   * `readySource` is useful if different logic should run depending on the\n   * platform the app is running from. For example, only Cordova can execute\n   * the status bar plugin, so the web should not run status bar plugin logic.\n   *\n   * ```\n   * import { Component } from '@angular/core';\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyApp {\n   *   constructor(public platform: Platform) {\n   *     this.platform.ready().then((readySource) => {\n   *       console.log('Platform ready from', readySource);\n   *       // Platform now ready, execute any required native code\n   *     });\n   *   }\n   * }\n   * ```\n   */\n  ready() {\n    return this._readyPromise;\n  }\n  /**\n   * Returns if this app is using right-to-left language direction or not.\n   * We recommend the app's `index.html` file already has the correct `dir`\n   * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n   * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n   */\n  get isRTL() {\n    return this.doc.dir === 'rtl';\n  }\n  /**\n   * Get the query string parameter\n   */\n  getQueryParam(key) {\n    return readQueryParam(this.win.location.href, key);\n  }\n  /**\n   * Returns `true` if the app is in landscape mode.\n   */\n  isLandscape() {\n    return !this.isPortrait();\n  }\n  /**\n   * Returns `true` if the app is in portrait mode.\n   */\n  isPortrait() {\n    return this.win.matchMedia?.('(orientation: portrait)').matches;\n  }\n  testUserAgent(expression) {\n    const nav = this.win.navigator;\n    return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n  }\n  /**\n   * Get the current url.\n   */\n  url() {\n    return this.win.location.href;\n  }\n  /**\n   * Gets the width of the platform's viewport using `window.innerWidth`.\n   */\n  width() {\n    return this.win.innerWidth;\n  }\n  /**\n   * Gets the height of the platform's viewport using `window.innerHeight`.\n   */\n  height() {\n    return this.win.innerHeight;\n  }\n  /** @nocollapse */\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nconst readQueryParam = (url, key) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n  if (el) {\n    el.addEventListener(eventName, ev => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? ev.detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\nclass NavController {\n  location;\n  serializer;\n  router;\n  topOutlet;\n  direction = DEFAULT_DIRECTION;\n  animated = DEFAULT_ANIMATED;\n  animationBuilder;\n  guessDirection = 'forward';\n  guessAnimation;\n  lastNavId = -1;\n  constructor(platform, location, serializer, router) {\n    this.location = location;\n    this.serializer = serializer;\n    this.router = router;\n    // Subscribe to router events to detect direction\n    if (router) {\n      router.events.subscribe(ev => {\n        if (ev instanceof NavigationStart) {\n          // restoredState is set if the browser back/forward button is used\n          const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n          this.guessDirection = this.guessAnimation = id < this.lastNavId ? 'back' : 'forward';\n          this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n        }\n      });\n    }\n    // Subscribe to backButton events\n    platform.backButton.subscribeWithPriority(0, processNextHandler => {\n      this.pop();\n      processNextHandler();\n    });\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n   *\n   * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n   * and that it will show a \"forward\" animation by default.\n   *\n   * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n   * ```\n   */\n  navigateForward(url, options = {}) {\n    this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('back');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n   * and that it will show a \"back\" animation by default.\n   *\n   * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n   * ```\n   */\n  navigateBack(url, options = {}) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('root');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **root** means that all existing pages in the stack will be removed,\n   * and the navigated page will become the single page in the stack.\n   *\n   * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n   * ```\n   */\n  navigateRoot(url, options = {}) {\n    this.setDirection('root', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n   * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n   * by default.\n   */\n  back(options = {\n    animated: true,\n    animationDirection: 'back'\n  }) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.location.back();\n  }\n  /**\n   * This methods goes back in the context of Ionic's stack navigation.\n   *\n   * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n   * This is the recommended way to go back when you are using `ion-router-outlet`.\n   *\n   * Resolves to `true` if it was able to pop.\n   */\n  pop() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let outlet = _this.topOutlet;\n      while (outlet) {\n        if (yield outlet.pop()) {\n          return true;\n        } else {\n          outlet = outlet.parentOutlet;\n        }\n      }\n      return false;\n    })();\n  }\n  /**\n   * This methods specifies the direction of the next navigation performed by the Angular router.\n   *\n   * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n   *\n   * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n   */\n  setDirection(direction, animated, animationDirection, animationBuilder) {\n    this.direction = direction;\n    this.animated = getAnimation(direction, animated, animationDirection);\n    this.animationBuilder = animationBuilder;\n  }\n  /**\n   * @internal\n   */\n  setTopOutlet(outlet) {\n    this.topOutlet = outlet;\n  }\n  /**\n   * @internal\n   */\n  consumeTransition() {\n    let direction = 'root';\n    let animation;\n    const animationBuilder = this.animationBuilder;\n    if (this.direction === 'auto') {\n      direction = this.guessDirection;\n      animation = this.guessAnimation;\n    } else {\n      animation = this.animated;\n      direction = this.direction;\n    }\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.animationBuilder = undefined;\n    return {\n      direction,\n      animation,\n      animationBuilder\n    };\n  }\n  navigate(url, options) {\n    if (Array.isArray(url)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigate(url, options);\n    } else {\n      /**\n       * navigateByUrl ignores any properties that\n       * would change the url, so things like queryParams\n       * would be ignored unless we create a url tree\n       * More Info: https://github.com/angular/angular/issues/18798\n       */\n      const urlTree = this.serializer.parse(url.toString());\n      if (options.queryParams !== undefined) {\n        urlTree.queryParams = {\n          ...options.queryParams\n        };\n      }\n      if (options.fragment !== undefined) {\n        urlTree.fragment = options.fragment;\n      }\n      /**\n       * `navigateByUrl` will still apply `NavigationExtras` properties\n       * that do not modify the url, such as `replaceUrl` which is why\n       * `options` is passed in here.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigateByUrl(urlTree, options);\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function NavController_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NavController)(i0.ɵɵinject(Platform), i0.ɵɵinject(i1.Location), i0.ɵɵinject(i3.UrlSerializer), i0.ɵɵinject(i3.Router, 8));\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NavController,\n    factory: NavController.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Platform\n    }, {\n      type: i1.Location\n    }, {\n      type: i3.UrlSerializer\n    }, {\n      type: i3.Router,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nconst getAnimation = (direction, animated, animationDirection) => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\nclass Config {\n  get(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.get(key, fallback);\n    }\n    return null;\n  }\n  getBoolean(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getBoolean(key, fallback);\n    }\n    return false;\n  }\n  getNumber(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getNumber(key, fallback);\n    }\n    return 0;\n  }\n  /** @nocollapse */\n  static ɵfac = function Config_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Config)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Config,\n    factory: Config.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Config, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n  if (typeof window !== 'undefined') {\n    const Ionic = window.Ionic;\n    if (Ionic?.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n  data;\n  constructor(data = {}) {\n    this.data = data;\n    console.warn(`[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.`);\n  }\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get(param) {\n    return this.data[param];\n  }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n  zone = inject(NgZone);\n  applicationRef = inject(ApplicationRef);\n  config = inject(ConfigToken);\n  create(environmentInjector, injector, elementReferenceKey) {\n    return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey, this.config.useSetInputAPI ?? false);\n  }\n  /** @nocollapse */\n  static ɵfac = function AngularDelegate_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AngularDelegate)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AngularDelegate,\n    factory: AngularDelegate.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularDelegate, [{\n    type: Injectable\n  }], null, null);\n})();\nclass AngularFrameworkDelegate {\n  environmentInjector;\n  injector;\n  applicationRef;\n  zone;\n  elementReferenceKey;\n  enableSignalsSupport;\n  elRefMap = new WeakMap();\n  elEventsMap = new WeakMap();\n  constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey, enableSignalsSupport) {\n    this.environmentInjector = environmentInjector;\n    this.injector = injector;\n    this.applicationRef = applicationRef;\n    this.zone = zone;\n    this.elementReferenceKey = elementReferenceKey;\n    this.enableSignalsSupport = enableSignalsSupport;\n  }\n  attachViewToDom(container, component, params, cssClasses) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentProps = {\n          ...params\n        };\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n        const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey, this.enableSignalsSupport);\n        resolve(el);\n      });\n    });\n  }\n  removeViewFromDom(_container, component) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey, enableSignalsSupport) => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector\n  });\n  const componentRef = createComponent(component, {\n    environmentInjector,\n    elementInjector: childInjector\n  });\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n    }\n    /**\n     * Angular 14.1 added support for setInput\n     * so we need to fall back to Object.assign\n     * for Angular 14.0.\n     */\n    if (enableSignalsSupport === true && componentRef.setInput !== undefined) {\n      const {\n        modal,\n        popover,\n        ...otherParams\n      } = params;\n      /**\n       * Any key/value pairs set in componentProps\n       * must be set as inputs on the component instance.\n       */\n      for (const key in otherParams) {\n        componentRef.setInput(key, otherParams[key]);\n      }\n      /**\n       * Using setInput will cause an error when\n       * setting modal/popover on a component that\n       * does not define them as an input. For backwards\n       * compatibility purposes we fall back to using\n       * Object.assign for these properties.\n       */\n      if (modal !== undefined) {\n        Object.assign(instance, {\n          modal\n        });\n      }\n      if (popover !== undefined) {\n        Object.assign(instance, {\n          popover\n        });\n      }\n    } else {\n      Object.assign(instance, params);\n    }\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n  applicationRef.attachView(componentRef.hostView);\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\nconst LIFECYCLES = [LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD];\nconst bindLifecycleEvents = (zone, instance, element) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter(eventName => typeof instance[eventName] === 'function').map(eventName => {\n      const handler = ev => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach(fn => fn());\n  });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = params => {\n  return [{\n    provide: NavParamsToken,\n    useValue: params\n  }, {\n    provide: NavParams,\n    useFactory: provideNavParamsInjectable,\n    deps: [NavParamsToken]\n  }];\n};\nconst provideNavParamsInjectable = params => {\n  return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\n/* eslint-disable */\n/* tslint:disable */\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      }\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nconst POPOVER_INPUTS = ['alignment', 'animated', 'arrow', 'keepContentsMounted', 'backdropDismiss', 'cssClass', 'dismissOnSelect', 'enterAnimation', 'event', 'focusTrap', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'translucent', 'trigger', 'triggerAction', 'reference', 'size', 'side'];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n  z;\n  // TODO(FW-2827): type\n  template;\n  isCmpOpen = false;\n  el;\n  constructor(c, r, z) {\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionPopoverDidPresent', 'ionPopoverWillPresent', 'ionPopoverWillDismiss', 'ionPopoverDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonPopover_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonPopover)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonPopover,\n    selectors: [[\"ion-popover\"]],\n    contentQueries: function IonPopover_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      alignment: \"alignment\",\n      animated: \"animated\",\n      arrow: \"arrow\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropDismiss: \"backdropDismiss\",\n      cssClass: \"cssClass\",\n      dismissOnSelect: \"dismissOnSelect\",\n      enterAnimation: \"enterAnimation\",\n      event: \"event\",\n      focusTrap: \"focusTrap\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\",\n      triggerAction: \"triggerAction\",\n      reference: \"reference\",\n      size: \"size\",\n      side: \"side\"\n    },\n    standalone: false\n  });\n};\nIonPopover = __decorate([ProxyCmp({\n  inputs: POPOVER_INPUTS,\n  methods: POPOVER_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonPopover);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonPopover, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-popover',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: POPOVER_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst MODAL_INPUTS = ['animated', 'keepContentsMounted', 'backdropBreakpoint', 'backdropDismiss', 'breakpoints', 'canDismiss', 'cssClass', 'enterAnimation', 'expandToScroll', 'event', 'focusTrap', 'handle', 'handleBehavior', 'initialBreakpoint', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'presentingElement', 'showBackdrop', 'translucent', 'trigger'];\nconst MODAL_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'setCurrentBreakpoint', 'getCurrentBreakpoint'];\nlet IonModal = class IonModal {\n  z;\n  // TODO(FW-2827): type\n  template;\n  isCmpOpen = false;\n  el;\n  constructor(c, r, z) {\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionModalDidPresent', 'ionModalWillPresent', 'ionModalWillDismiss', 'ionModalDidDismiss', 'ionBreakpointDidChange', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonModal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonModal)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonModal,\n    selectors: [[\"ion-modal\"]],\n    contentQueries: function IonModal_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      animated: \"animated\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropBreakpoint: \"backdropBreakpoint\",\n      backdropDismiss: \"backdropDismiss\",\n      breakpoints: \"breakpoints\",\n      canDismiss: \"canDismiss\",\n      cssClass: \"cssClass\",\n      enterAnimation: \"enterAnimation\",\n      expandToScroll: \"expandToScroll\",\n      event: \"event\",\n      focusTrap: \"focusTrap\",\n      handle: \"handle\",\n      handleBehavior: \"handleBehavior\",\n      initialBreakpoint: \"initialBreakpoint\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      presentingElement: \"presentingElement\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    standalone: false\n  });\n};\nIonModal = __decorate([ProxyCmp({\n  inputs: MODAL_INPUTS,\n  methods: MODAL_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonModal);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonModal, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-modal',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: MODAL_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst insertView = (views, view, direction) => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\nconst setRoot = (views, view) => {\n  views = views.filter(v => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\nconst setForward = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\nconst setBack = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\nconst getUrl = (router, activatedRoute) => {\n  const urlTree = router.createUrlTree(['.'], {\n    relativeTo: activatedRoute\n  });\n  return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\nconst toSegments = path => {\n  return path.split('/').map(s => s.trim()).filter(s => s !== '');\n};\nconst destroyView = view => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n  containerEl;\n  router;\n  navCtrl;\n  zone;\n  location;\n  views = [];\n  runningTask;\n  skipTransition = false;\n  tabsPrefix;\n  activeView;\n  nextId = 0;\n  constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n    this.containerEl = containerEl;\n    this.router = router;\n    this.navCtrl = navCtrl;\n    this.zone = zone;\n    this.location = location;\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n  createView(ref, activatedRoute) {\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref?.location?.nativeElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url\n    };\n  }\n  getExistingView(activatedRoute) {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find(vw => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n  setActive(enteringView) {\n    const consumeResult = this.navCtrl.consumeTransition();\n    let {\n      direction,\n      animation,\n      animationBuilder\n    } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n    const viewsSnapshot = this.views.slice();\n    let currentNavigation;\n    const router = this.router;\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n      // Angular < 7.2.0\n    } else if (router.navigations?.value) {\n      currentNavigation = router.navigations.value;\n    }\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if (currentNavigation?.extras?.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder).then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone)).then(() => ({\n          enteringView,\n          direction,\n          animation,\n          tabSwitch\n        }));\n      });\n    });\n  }\n  canGoBack(deep, stackId = this.getActiveStackId()) {\n    return this.getStack(stackId).length > deep;\n  }\n  pop(deep, stackId = this.getActiveStackId()) {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet?.route?._routerState?.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const {\n        animationBuilder\n      } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, {\n        ...view.savedExtras,\n        animation: animationBuilder\n      }).then(() => true);\n    });\n  }\n  startBackTransition() {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n      return this.wait(() => {\n        return this.transition(enteringView,\n        // entering view\n        leavingView,\n        // leaving view\n        'back', this.canGoBack(2), true, customAnimation);\n      });\n    }\n    return Promise.resolve();\n  }\n  endBackTransition(shouldComplete) {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n  getLastUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n  /**\n   * @internal\n   */\n  getRootUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n  getActiveStackId() {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n  /**\n   * @internal\n   */\n  getActiveView() {\n    return this.activeView;\n  }\n  hasRunningTask() {\n    return this.runningTask !== undefined;\n  }\n  destroy() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n  getStack(stackId) {\n    return this.views.filter(v => v.stackId === stackId);\n  }\n  insertView(enteringView, direction) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n  transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (containerEl.commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n  wait(task) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.runningTask !== undefined) {\n        yield _this2.runningTask;\n        _this2.runningTask = undefined;\n      }\n      const promise = _this2.runningTask = task();\n      promise.finally(() => _this2.runningTask = undefined);\n      return promise;\n    })();\n  }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n  if (typeof requestAnimationFrame === 'function') {\n    return new Promise(resolve => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter(view => !views.includes(view)).forEach(destroyView));\n  views.forEach(view => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n  parentOutlet;\n  nativeEl;\n  activatedView = null;\n  tabsPrefix;\n  _swipeGesture;\n  stackCtrl;\n  // Maintain map of activated route proxies for each component instance\n  proxyMap = new WeakMap();\n  // Keep the latest activated route in a subject for the proxy routes to switch map to\n  currentActivatedRoute$ = new BehaviorSubject(null);\n  activated = null;\n  /** @internal */\n  get activatedComponentRef() {\n    return this.activated;\n  }\n  _activatedRoute = null;\n  /**\n   * The name of the outlet\n   */\n  name = PRIMARY_OUTLET;\n  /** @internal */\n  stackWillChange = new EventEmitter();\n  /** @internal */\n  stackDidChange = new EventEmitter();\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  activateEvents = new EventEmitter();\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  deactivateEvents = new EventEmitter();\n  parentContexts = inject(ChildrenOutletContexts);\n  location = inject(ViewContainerRef);\n  environmentInjector = inject(EnvironmentInjector);\n  inputBinder = inject(INPUT_BINDER, {\n    optional: true\n  });\n  /** @nodoc */\n  supportsBindingToComponentInputs = true;\n  // Ionic providers\n  config = inject(Config);\n  navCtrl = inject(NavController);\n  set animation(animation) {\n    this.nativeEl.animation = animation;\n  }\n  set animated(animated) {\n    this.nativeEl.animated = animated;\n  }\n  set swipeGesture(swipe) {\n    this._swipeGesture = swipe;\n    this.nativeEl.swipeHandler = swipe ? {\n      canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n      onStart: () => this.stackCtrl.startBackTransition(),\n      onEnd: shouldContinue => this.stackCtrl.endBackTransition(shouldContinue)\n    } : undefined;\n  }\n  constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n    this.parentOutlet = parentOutlet;\n    this.nativeEl = elementRef.nativeElement;\n    this.name = name || PRIMARY_OUTLET;\n    this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n    this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n    this.parentContexts.onChildOutletCreated(this.name, this);\n  }\n  ngOnDestroy() {\n    this.stackCtrl.destroy();\n    this.inputBinder?.unsubscribeFromRouteData(this);\n  }\n  getContext() {\n    return this.parentContexts.getContext(this.name);\n  }\n  ngOnInit() {\n    this.initializeOutletWithName();\n  }\n  // Note: Ionic deviates from the Angular Router implementation here\n  initializeOutletWithName() {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const context = this.getContext();\n      if (context?.route) {\n        this.activateWith(context.route, context.injector);\n      }\n    }\n    new Promise(resolve => componentOnReady(this.nativeEl, resolve)).then(() => {\n      if (this._swipeGesture === undefined) {\n        this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n      }\n    });\n  }\n  get isActivated() {\n    return !!this.activated;\n  }\n  get component() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this.activated.instance;\n  }\n  get activatedRoute() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this._activatedRoute;\n  }\n  get activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach() {\n    throw new Error('incompatible reuse strategy');\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  attach(_ref, _activatedRoute) {\n    throw new Error('incompatible reuse strategy');\n  }\n  deactivate() {\n    if (this.activated) {\n      if (this.activatedView) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        this.activatedView.savedData = new Map(context.children['contexts']);\n        /**\n         * Angular v11.2.10 introduced a change\n         * where this route context is cleared out when\n         * a router-outlet is deactivated, However,\n         * we need this route information in order to\n         * return a user back to the correct tab when\n         * leaving and then going back to the tab context.\n         */\n        const primaryOutlet = this.activatedView.savedData.get('primary');\n        if (primaryOutlet && context.route) {\n          primaryOutlet.route = {\n            ...context.route\n          };\n        }\n        /**\n         * Ensure we are saving the NavigationExtras\n         * data otherwise it will be lost\n         */\n        this.activatedView.savedExtras = {};\n        if (context.route) {\n          const contextSnapshot = context.route.snapshot;\n          this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n          this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n        }\n      }\n      const c = this.component;\n      this.activatedView = null;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n  activateWith(activatedRoute, environmentInjector) {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    let cmpRef;\n    let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n    if (enteringView) {\n      cmpRef = this.activated = enteringView.ref;\n      const saved = enteringView.savedData;\n      if (saved) {\n        // self-restore\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        context.children['contexts'] = saved;\n      }\n      // Updated activated route proxy for this component\n      this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n    } else {\n      const snapshot = activatedRoute._futureSnapshot;\n      /**\n       * Angular 14 introduces a new `loadComponent` property to the route config.\n       * This function will assign a `component` property to the route snapshot.\n       * We check for the presence of this property to determine if the route is\n       * using standalone components.\n       */\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n      // We create an activated route proxy object that will maintain future updates for this component\n      // over its lifecycle in the stack.\n      const component$ = new BehaviorSubject(null);\n      const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n      const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const component = snapshot.routeConfig.component ?? snapshot.component;\n      /**\n       * View components need to be added as a child of ion-router-outlet\n       * for page transitions and swipe to go back.\n       * However, createComponent mounts components as siblings of the\n       * ViewContainerRef. As a result, outletContent must reference\n       * an ng-container inside of ion-router-outlet and not\n       * ion-router-outlet itself.\n       */\n      cmpRef = this.activated = this.outletContent.createComponent(component, {\n        index: this.outletContent.length,\n        injector,\n        environmentInjector: environmentInjector ?? this.environmentInjector\n      });\n      // Once the component is created we can push it to our local subject supplied to the proxy\n      component$.next(cmpRef.instance);\n      // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n      /**\n       * At this point this.activated has been set earlier\n       * in this function, so it is guaranteed to be non-null.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n      // Store references to the proxy by component\n      this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n      this.currentActivatedRoute$.next({\n        component: cmpRef.instance,\n        activatedRoute\n      });\n    }\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n    this.activatedView = enteringView;\n    /**\n     * The top outlet is set prior to the entering view's transition completing,\n     * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n     * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n     * activated.\n     *\n     * In this scenario, activeWith is called for both the tabs and the root router outlet.\n     * To avoid a race condition, we assign the top outlet synchronously.\n     */\n    this.navCtrl.setTopOutlet(this);\n    const leavingView = this.stackCtrl.getActiveView();\n    this.stackWillChange.emit({\n      enteringView,\n      tabSwitch: isTabSwitch(enteringView, leavingView)\n    });\n    this.stackCtrl.setActive(enteringView).then(data => {\n      this.activateEvents.emit(cmpRef.instance);\n      this.stackDidChange.emit(data);\n    });\n  }\n  /**\n   * Returns `true` if there are pages in the stack to go back.\n   */\n  canGoBack(deep = 1, stackId) {\n    return this.stackCtrl.canGoBack(deep, stackId);\n  }\n  /**\n   * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n   */\n  pop(deep = 1, stackId) {\n    return this.stackCtrl.pop(deep, stackId);\n  }\n  /**\n   * Returns the URL of the active page of each stack.\n   */\n  getLastUrl(stackId) {\n    const active = this.stackCtrl.getLastUrl(stackId);\n    return active ? active.url : undefined;\n  }\n  /**\n   * Returns the RouteView of the active page of each stack.\n   * @internal\n   */\n  getLastRouteView(stackId) {\n    return this.stackCtrl.getLastUrl(stackId);\n  }\n  /**\n   * Returns the root view in the tab stack.\n   * @internal\n   */\n  getRootView(stackId) {\n    return this.stackCtrl.getRootUrl(stackId);\n  }\n  /**\n   * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n   */\n  getActiveStackId() {\n    return this.stackCtrl.getActiveStackId();\n  }\n  /**\n   * Since the activated route can change over the life time of a component in an ion router outlet, we create\n   * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n   */\n  createActivatedRouteProxy(component$, activatedRoute) {\n    const proxy = new ActivatedRoute();\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n    proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n    proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n    proxy.url = this.proxyObservable(component$, 'url');\n    proxy.params = this.proxyObservable(component$, 'params');\n    proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n    proxy.fragment = this.proxyObservable(component$, 'fragment');\n    proxy.data = this.proxyObservable(component$, 'data');\n    return proxy;\n  }\n  /**\n   * Create a wrapped observable that will switch to the latest activated route matched by the given component\n   */\n  proxyObservable(component$, path) {\n    return component$.pipe(\n    // First wait until the component instance is pushed\n    filter(component => !!component), switchMap(component => this.currentActivatedRoute$.pipe(filter(current => current !== null && current.component === component), switchMap(current => current && current.activatedRoute[path]), distinctUntilChanged())));\n  }\n  /**\n   * Updates the activated route proxy for the given component to the new incoming router state\n   */\n  updateActivatedRouteProxy(component, activatedRoute) {\n    const proxy = this.proxyMap.get(component);\n    if (!proxy) {\n      throw new Error(`Could not find activated route proxy for view`);\n    }\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    this.currentActivatedRoute$.next({\n      component,\n      activatedRoute\n    });\n  }\n  /** @nocollapse */\n  static ɵfac = function IonRouterOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(IonRouterOutlet, 12));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonRouterOutlet,\n    selectors: [[\"ion-router-outlet\"]],\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      mode: \"mode\",\n      swipeGesture: \"swipeGesture\",\n      name: \"name\"\n    },\n    outputs: {\n      stackWillChange: \"stackWillChange\",\n      stackDidChange: \"stackDidChange\",\n      activateEvents: \"activate\",\n      deactivateEvents: \"deactivate\"\n    },\n    exportAs: [\"outlet\"],\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonRouterOutlet, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-router-outlet',\n      exportAs: 'outlet',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: ['animated', 'animation', 'mode', 'swipeGesture']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['name']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Attribute,\n        args: ['tabs']\n      }]\n    }, {\n      type: i1.Location\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.ActivatedRoute\n    }, {\n      type: IonRouterOutlet,\n      decorators: [{\n        type: SkipSelf\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    stackWillChange: [{\n      type: Output\n    }],\n    stackDidChange: [{\n      type: Output\n    }],\n    activateEvents: [{\n      type: Output,\n      args: ['activate']\n    }],\n    deactivateEvents: [{\n      type: Output,\n      args: ['deactivate']\n    }]\n  });\n})();\nclass OutletInjector {\n  route;\n  childContexts;\n  parent;\n  constructor(route, childContexts, parent) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n  outletDataSubscriptions = new Map();\n  bindActivatedRouteToOutletComponent(outlet) {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n  unsubscribeFromRouteData(outlet) {\n    this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n  subscribeToRouteData(outlet) {\n    const {\n      activatedRoute\n    } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n      data = {\n        ...queryParams,\n        ...params,\n        ...data\n      };\n      // Get the first result from the data subscription synchronously so it's available to\n      // the component as soon as possible (and doesn't require a second change detection).\n      if (index === 0) {\n        return of(data);\n      }\n      // Promise.resolve is used to avoid synchronously writing the wrong data when\n      // two of the Observables in the `combineLatest` stream emit one after\n      // another.\n      return Promise.resolve(data);\n    })).subscribe(data => {\n      // Outlet may have been deactivated or changed names to be associated with a different\n      // route\n      if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      const mirror = reflectComponentType(activatedRoute.component);\n      if (!mirror) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      for (const {\n        templateName\n      } of mirror.inputs) {\n        outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n      }\n    });\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n  /** @nocollapse */\n  static ɵfac = function RoutedComponentInputBinder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoutedComponentInputBinder)();\n  };\n  /** @nocollapse */\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RoutedComponentInputBinder,\n    factory: RoutedComponentInputBinder.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutedComponentInputBinder, [{\n    type: Injectable\n  }], null, null);\n})();\nconst provideComponentInputBinding = () => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router]\n  };\n};\nfunction componentInputBindingFactory(router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if (router?.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n  routerOutlet;\n  navCtrl;\n  config;\n  r;\n  z;\n  el;\n  constructor(routerOutlet, navCtrl, config, r, z, c) {\n    this.routerOutlet = routerOutlet;\n    this.navCtrl = navCtrl;\n    this.config = config;\n    this.r = r;\n    this.z = z;\n    c.detach();\n    this.el = this.r.nativeElement;\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n    if (this.routerOutlet?.canGoBack()) {\n      this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n      this.routerOutlet.pop();\n      ev.preventDefault();\n    } else if (defaultHref != null) {\n      this.navCtrl.navigateBack(defaultHref, {\n        animation: this.routerAnimation\n      });\n      ev.preventDefault();\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function IonBackButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonBackButton,\n    hostBindings: function IonBackButton_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function IonBackButton_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      color: \"color\",\n      defaultHref: \"defaultHref\",\n      disabled: \"disabled\",\n      icon: \"icon\",\n      mode: \"mode\",\n      routerAnimation: \"routerAnimation\",\n      text: \"text\",\n      type: \"type\"\n    },\n    standalone: false\n  });\n};\nIonBackButton = __decorate([ProxyCmp({\n  inputs: BACK_BUTTON_INPUTS\n})], IonBackButton);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonBackButton, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: BACK_BUTTON_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: IonRouterOutlet,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: NavController\n    }, {\n      type: Config\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n  locationStrategy;\n  navCtrl;\n  elementRef;\n  router;\n  routerLink;\n  routerDirection = 'forward';\n  routerAnimation;\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n    this.updateTabindex();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  /**\n   * The `tabindex` is set to `0` by default on the host element when\n   * the `routerLink` directive is used. This causes issues with Ionic\n   * components that wrap an `a` or `button` element, such as `ion-item`.\n   * See issue https://github.com/angular/angular/issues/28345\n   *\n   * This method removes the `tabindex` attribute from the host element\n   * to allow the Ionic component to manage the focus state correctly.\n   */\n  updateTabindex() {\n    // Ionic components that render a native anchor or button element\n    const ionicComponents = ['ION-BACK-BUTTON', 'ION-BREADCRUMB', 'ION-BUTTON', 'ION-CARD', 'ION-FAB-BUTTON', 'ION-ITEM', 'ION-ITEM-OPTION', 'ION-MENU-BUTTON', 'ION-SEGMENT-BUTTON', 'ION-TAB-BUTTON'];\n    const hostElement = this.elementRef.nativeElement;\n    if (ionicComponents.includes(hostElement.tagName)) {\n      if (hostElement.getAttribute('tabindex') === '0') {\n        hostElement.removeAttribute('tabindex');\n      }\n    }\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    /**\n     * This prevents the browser from\n     * performing a page reload when pressing\n     * an Ionic component with routerLink.\n     * The page reload interferes with routing\n     * and causes ion-back-button to disappear\n     * since the local history is wiped on reload.\n     */\n    ev.preventDefault();\n  }\n  /** @nocollapse */\n  static ɵfac = function RouterLinkDelegateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkDelegateDirective,\n    selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n    hostBindings: function RouterLinkDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkDelegateDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: ':not(a):not(area)[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass RouterLinkWithHrefDelegateDirective {\n  locationStrategy;\n  navCtrl;\n  elementRef;\n  router;\n  routerLink;\n  routerDirection = 'forward';\n  routerAnimation;\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick() {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n  }\n  /** @nocollapse */\n  static ɵfac = function RouterLinkWithHrefDelegateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkWithHrefDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkWithHrefDelegateDirective,\n    selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n    hostBindings: function RouterLinkWithHrefDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkWithHrefDelegateDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkWithHrefDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[routerLink],area[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = ['push', 'insert', 'insertPages', 'pop', 'popTo', 'popToRoot', 'removeIndex', 'setRoot', 'setPages', 'getActive', 'getByIndex', 'canGoBack', 'getPrevious'];\nlet IonNav = class IonNav {\n  z;\n  el;\n  constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n    this.z = z;\n    c.detach();\n    this.el = ref.nativeElement;\n    ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n    proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n  }\n  /** @nocollapse */\n  static ɵfac = function IonNav_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonNav,\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      root: \"root\",\n      rootParams: \"rootParams\",\n      swipeGesture: \"swipeGesture\"\n    },\n    standalone: false\n  });\n};\nIonNav = __decorate([ProxyCmp({\n  inputs: NAV_INPUTS,\n  methods: NAV_METHODS\n})], IonNav);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonNav, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: NAV_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.EnvironmentInjector\n    }, {\n      type: i0.Injector\n    }, {\n      type: AngularDelegate\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n  navCtrl;\n  tabsInner;\n  /**\n   * Emitted before the tab view is changed.\n   */\n  ionTabsWillChange = new EventEmitter();\n  /**\n   * Emitted after the tab view is changed.\n   */\n  ionTabsDidChange = new EventEmitter();\n  tabBarSlot = 'bottom';\n  hasTab = false;\n  selectedTab;\n  leavingTab;\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngAfterViewInit() {\n    /**\n     * Developers must pass at least one ion-tab\n     * inside of ion-tabs if they want to use a\n     * basic tab-based navigation without the\n     * history stack or URL updates associated\n     * with the router.\n     */\n    const firstTab = this.tabs.length > 0 ? this.tabs.first : undefined;\n    if (firstTab) {\n      this.hasTab = true;\n      this.setActiveTab(firstTab.tab);\n      this.tabSwitch();\n    }\n  }\n  ngAfterContentInit() {\n    this.detectSlotChanges();\n  }\n  ngAfterContentChecked() {\n    this.detectSlotChanges();\n  }\n  /**\n   * @internal\n   */\n  onStackWillChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      this.ionTabsWillChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  onStackDidChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      if (this.tabBar) {\n        this.tabBar.selectedTab = stackId;\n      }\n      this.ionTabsDidChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * When a tab button is clicked, there are several scenarios:\n   * 1. If the selected tab is currently active (the tab button has been clicked\n   *    again), then it should go to the root view for that tab.\n   *\n   *   a. Get the saved root view from the router outlet. If the saved root view\n   *      matches the tabRootUrl, set the route view to this view including the\n   *      navigation extras.\n   *   b. If the saved root view from the router outlet does\n   *      not match, navigate to the tabRootUrl. No navigation extras are\n   *      included.\n   *\n   * 2. If the current tab tab is not currently selected, get the last route\n   *    view from the router outlet.\n   *\n   *   a. If the last route view exists, navigate to that view including any\n   *      navigation extras\n   *   b. If the last route view doesn't exist, then navigate\n   *      to the default tabRootUrl\n   */\n  select(tabOrEvent) {\n    const isTabString = typeof tabOrEvent === 'string';\n    const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n    /**\n     * If the tabs are not using the router, then\n     * the tab switch logic is handled by the tabs\n     * component itself.\n     */\n    if (this.hasTab) {\n      this.setActiveTab(tab);\n      this.tabSwitch();\n      return;\n    }\n    const alreadySelected = this.outlet.getActiveStackId() === tab;\n    const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n    /**\n     * If this is a nested tab, prevent the event\n     * from bubbling otherwise the outer tabs\n     * will respond to this event too, causing\n     * the app to get directed to the wrong place.\n     */\n    if (!isTabString) {\n      tabOrEvent.stopPropagation();\n    }\n    if (alreadySelected) {\n      const activeStackId = this.outlet.getActiveStackId();\n      const activeView = this.outlet.getLastRouteView(activeStackId);\n      // If on root tab, do not navigate to root tab again\n      if (activeView?.url === tabRootUrl) {\n        return;\n      }\n      const rootView = this.outlet.getRootView(tab);\n      const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n      return this.navCtrl.navigateRoot(tabRootUrl, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    } else {\n      const lastRoute = this.outlet.getLastRouteView(tab);\n      /**\n       * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n       * selected tab\n       */\n      const url = lastRoute?.url || tabRootUrl;\n      const navigationExtras = lastRoute?.savedExtras;\n      return this.navCtrl.navigateRoot(url, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    }\n  }\n  setActiveTab(tab) {\n    const tabs = this.tabs;\n    const selectedTab = tabs.find(t => t.tab === tab);\n    if (!selectedTab) {\n      console.error(`[Ionic Error]: Tab with id: \"${tab}\" does not exist`);\n      return;\n    }\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab\n    });\n    selectedTab.el.active = true;\n  }\n  tabSwitch() {\n    const {\n      selectedTab,\n      leavingTab\n    } = this;\n    if (this.tabBar && selectedTab) {\n      this.tabBar.selectedTab = selectedTab.tab;\n    }\n    if (leavingTab?.tab !== selectedTab?.tab) {\n      if (leavingTab?.el) {\n        leavingTab.el.active = false;\n      }\n    }\n    if (selectedTab) {\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  getSelected() {\n    if (this.hasTab) {\n      return this.selectedTab?.tab;\n    }\n    return this.outlet.getActiveStackId();\n  }\n  /**\n   * Detects changes to the slot attribute of the tab bar.\n   *\n   * If the slot attribute has changed, then the tab bar\n   * should be relocated to the new slot position.\n   */\n  detectSlotChanges() {\n    this.tabBars.forEach(tabBar => {\n      // el is a protected attribute from the generated component wrapper\n      const currentSlot = tabBar.el.getAttribute('slot');\n      if (currentSlot !== this.tabBarSlot) {\n        this.tabBarSlot = currentSlot;\n        this.relocateTabBar();\n      }\n    });\n  }\n  /**\n   * Relocates the tab bar to the new slot position.\n   */\n  relocateTabBar() {\n    /**\n     * `el` is a protected attribute from the generated component wrapper.\n     * To avoid having to manually create the wrapper for tab bar, we\n     * cast the tab bar to any and access the protected attribute.\n     */\n    const tabBar = this.tabBar.el;\n    if (this.tabBarSlot === 'top') {\n      /**\n       * A tab bar with a slot of \"top\" should be inserted\n       * at the top of the container.\n       */\n      this.tabsInner.nativeElement.before(tabBar);\n    } else {\n      /**\n       * A tab bar with a slot of \"bottom\" or without a slot\n       * should be inserted at the end of the container.\n       */\n      this.tabsInner.nativeElement.after(tabBar);\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function IonTabs_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || IonTabs)(i0.ɵɵdirectiveInject(NavController));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonTabs,\n    selectors: [[\"ion-tabs\"]],\n    viewQuery: function IonTabs_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsInner = _t.first);\n      }\n    },\n    hostBindings: function IonTabs_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionTabButtonClick\", function IonTabs_ionTabButtonClick_HostBindingHandler($event) {\n          return ctx.select($event);\n        });\n      }\n    },\n    outputs: {\n      ionTabsWillChange: \"ionTabsWillChange\",\n      ionTabsDidChange: \"ionTabsDidChange\"\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonTabs, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-tabs'\n    }]\n  }], function () {\n    return [{\n      type: NavController\n    }];\n  }, {\n    tabsInner: [{\n      type: ViewChild,\n      args: ['tabsInner', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    ionTabsWillChange: [{\n      type: Output\n    }],\n    ionTabsDidChange: [{\n      type: Output\n    }],\n    select: [{\n      type: HostListener,\n      args: ['ionTabButtonClick', ['$event']]\n    }]\n  });\n})();\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n  injector;\n  elementRef;\n  onChange = () => {\n    /**/\n  };\n  onTouched = () => {\n    /**/\n  };\n  lastValue;\n  statusChanges;\n  constructor(injector, elementRef) {\n    this.injector = injector;\n    this.elementRef = elementRef;\n  }\n  writeValue(value) {\n    this.elementRef.nativeElement.value = this.lastValue = value;\n    setIonicClasses(this.elementRef);\n  }\n  /**\n   * Notifies the ControlValueAccessor of a change in the value of the control.\n   *\n   * This is called by each of the ValueAccessor directives when we want to update\n   * the status and validity of the form control. For example with text components this\n   * is called when the ionInput event is fired. For select components this is called\n   * when the ionChange event is fired.\n   *\n   * This also updates the Ionic form status classes on the element.\n   *\n   * @param el The component element.\n   * @param value The new value of the control.\n   */\n  handleValueChange(el, value) {\n    if (el === this.elementRef.nativeElement) {\n      if (value !== this.lastValue) {\n        this.lastValue = value;\n        this.onChange(value);\n      }\n      setIonicClasses(this.elementRef);\n    }\n  }\n  _handleBlurEvent(el) {\n    if (el === this.elementRef.nativeElement) {\n      this.onTouched();\n      setIonicClasses(this.elementRef);\n      // When ion-radio is blurred, el and this.elementRef.nativeElement are\n      // different so we need to check if the closest ion-radio-group is the same\n      // as this.elementRef.nativeElement and if so, we need to mark the radio group\n      // as touched\n    } else if (el.closest('ion-radio-group') === this.elementRef.nativeElement) {\n      this.onTouched();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.elementRef.nativeElement.disabled = isDisabled;\n  }\n  ngOnDestroy() {\n    if (this.statusChanges) {\n      this.statusChanges.unsubscribe();\n    }\n  }\n  ngAfterViewInit() {\n    let ngControl;\n    try {\n      ngControl = this.injector.get(NgControl);\n    } catch {\n      /* No FormControl or ngModel binding */\n    }\n    if (!ngControl) {\n      return;\n    }\n    // Listen for changes in validity, disabled, or pending states\n    if (ngControl.statusChanges) {\n      this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n    }\n    /**\n     * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n     * whenever it is implemented.\n     */\n    const formControl = ngControl.control;\n    if (formControl) {\n      const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n      methodsToPatch.forEach(method => {\n        if (typeof formControl[method] !== 'undefined') {\n          const oldFn = formControl[method].bind(formControl);\n          formControl[method] = (...params) => {\n            oldFn(...params);\n            setIonicClasses(this.elementRef);\n          };\n        }\n      });\n    }\n  }\n  /** @nocollapse */\n  static ɵfac = function ValueAccessor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ValueAccessor)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  /** @nocollapse */\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ValueAccessor,\n    hostBindings: function ValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionBlur\", function ValueAccessor_ionBlur_HostBindingHandler($event) {\n          return ctx._handleBlurEvent($event.target);\n        });\n      }\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ValueAccessor, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    _handleBlurEvent: [{\n      type: HostListener,\n      args: ['ionBlur', ['$event.target']]\n    }]\n  });\n})();\nconst setIonicClasses = element => {\n  raf(() => {\n    const input = element.nativeElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\nconst getClasses = element => {\n  const classList = element.classList;\n  const classes = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\nconst setClasses = (element, classes) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n  return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route) {\n    return false;\n  }\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route, _detachedTree) {\n    return;\n  }\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future, curr) {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n  ctrl;\n  constructor(ctrl) {\n    this.ctrl = ctrl;\n  }\n  /**\n   * Creates a new overlay\n   */\n  create(opts) {\n    return this.ctrl.create(opts || {});\n  }\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data, role, id) {\n    return this.ctrl.dismiss(data, role, id);\n  }\n  /**\n   * Returns the top overlay.\n   */\n  getTop() {\n    return this.ctrl.getTop();\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "Optional", "InjectionToken", "inject", "NgZone", "ApplicationRef", "Injector", "createComponent", "TemplateRef", "Directive", "ContentChild", "EventEmitter", "ViewContainerRef", "EnvironmentInjector", "Attribute", "SkipSelf", "Input", "Output", "reflectComponentType", "HostListener", "ElementRef", "ViewChild", "i3", "NavigationStart", "PRIMARY_OUTLET", "ChildrenOutletContexts", "ActivatedRoute", "Router", "i1", "DOCUMENT", "isPlatform", "getPlatforms", "LIFECYCLE_WILL_ENTER", "LIFECYCLE_DID_ENTER", "LIFECYCLE_WILL_LEAVE", "LIFECYCLE_DID_LEAVE", "LIFECYCLE_WILL_UNLOAD", "componentOnReady", "Subject", "fromEvent", "BehaviorSubject", "combineLatest", "of", "__decorate", "filter", "switchMap", "distinctUntilChanged", "NgControl", "_c0", "MenuController", "menuController", "constructor", "open", "menuId", "close", "toggle", "enable", "shouldEnable", "swipeGesture", "isOpen", "isEnabled", "get", "get<PERSON>pen", "getMenus", "registerAnimation", "name", "animation", "isAnimating", "_getOpenSync", "_createAnimation", "type", "menuCmp", "_register", "menu", "_unregister", "_setOpen", "shouldOpen", "animated", "DomController", "read", "cb", "getQueue", "write", "ɵfac", "DomController_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "win", "window", "<PERSON><PERSON>", "queue", "requestAnimationFrame", "Platform", "doc", "_readyPromise", "backButton", "keyboardDidShow", "keyboardDidHide", "pause", "resume", "resize", "zone", "run", "defaultView", "subscribeWithPriority", "priority", "callback", "subscribe", "ev", "register", "processNextHandler", "proxyEvent", "readyResolve", "Promise", "res", "addEventListener", "once", "is", "platformName", "platforms", "ready", "isRTL", "dir", "getQueryParam", "key", "readQueryParam", "location", "href", "isLandscape", "isPortrait", "matchMedia", "matches", "testUserAgent", "expression", "nav", "navigator", "userAgent", "indexOf", "url", "width", "innerWidth", "height", "innerHeight", "Platform_Factory", "ɵɵinject", "undefined", "decorators", "replace", "regex", "RegExp", "results", "exec", "decodeURIComponent", "emitter", "el", "eventName", "value", "detail", "next", "NavController", "serializer", "router", "topOutlet", "direction", "DEFAULT_DIRECTION", "DEFAULT_ANIMATED", "animationBuilder", "guessDirection", "guessAnimation", "lastNavId", "platform", "events", "id", "restoredState", "navigationId", "pop", "navigateForward", "options", "setDirection", "animationDirection", "navigate", "navigateBack", "navigateRoot", "back", "_this", "_asyncToGenerator", "outlet", "parentOutlet", "getAnimation", "setTopOutlet", "consumeTransition", "Array", "isArray", "urlTree", "parse", "toString", "queryParams", "fragment", "navigateByUrl", "NavController_Factory", "Location", "UrlSerializer", "Config", "fallback", "c", "getConfig", "getBoolean", "getNumber", "Config_Factory", "ConfigToken", "config", "NavParams", "data", "console", "warn", "param", "AngularDelegate", "applicationRef", "create", "environmentInjector", "injector", "elementReferenceKey", "AngularFrameworkDelegate", "useSetInputAPI", "AngularDelegate_Factory", "enableSignalsSupport", "elRefMap", "WeakMap", "elEventsMap", "attachViewToDom", "container", "component", "params", "cssClasses", "resolve", "componentProps", "attachView", "removeViewFromDom", "_container", "componentRef", "destroy", "delete", "unbindEvents", "childInjector", "providers", "getProviders", "parent", "elementInjector", "instance", "hostElement", "nativeElement", "error", "tagName", "toLowerCase", "setInput", "modal", "popover", "otherParams", "Object", "assign", "cssClass", "classList", "add", "bindLifecycleEvents", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "set", "LIFECYCLES", "element", "unregisters", "map", "handler", "removeEventListener", "for<PERSON>ach", "fn", "NavParamsToken", "provide", "useValue", "useFactory", "provideNavParamsInjectable", "deps", "proxyInputs", "Cmp", "inputs", "Prototype", "prototype", "item", "defineProperty", "val", "z", "runOutsideAngular", "proxyMethods", "methods", "methodName", "arguments", "apply", "proxyOutputs", "ProxyCmp", "opts", "decorator", "cls", "defineCustomElementFn", "POPOVER_INPUTS", "POPOVER_METHODS", "IonPopover", "template", "isCmpOpen", "r", "detectChanges", "IonPopover_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "selectors", "contentQueries", "IonPopover_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "alignment", "arrow", "keepContentsMounted", "<PERSON><PERSON><PERSON><PERSON>", "dismissOnSelect", "enterAnimation", "event", "focusTrap", "keyboardClose", "leaveAnimation", "mode", "showBackdrop", "translucent", "trigger", "triggerAction", "reference", "size", "side", "standalone", "selector", "static", "MODAL_INPUTS", "MODAL_METHODS", "IonModal", "IonModal_Factory", "IonModal_ContentQueries", "backdropBreakpoint", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>", "expandToScroll", "handle", "handleBehavior", "initialBreakpoint", "presentingElement", "insertView", "views", "view", "setRoot", "setForward", "setBack", "v", "stackId", "push", "index", "getUrl", "activatedRoute", "createUrlTree", "relativeTo", "serializeUrl", "isTabSwitch", "enteringView", "leavingView", "computeStackId", "prefixUrl", "segments", "toSegments", "i", "length", "path", "split", "s", "trim", "destroyView", "ref", "unlistenEvents", "StackController", "containerEl", "navCtrl", "runningTask", "skipTransition", "tabsPrefix", "activeView", "nextId", "createView", "getExistingView", "activatedUrlKey", "find", "vw", "changeDetectorRef", "reattach", "setActive", "consumeResult", "tabSwitch", "viewsSnapshot", "slice", "currentNavigation", "getCurrentNavigation", "navigations", "extras", "replaceUrl", "splice", "reused", "includes", "customAnimation", "wait", "detach", "transition", "canGoBack", "then", "cleanupAsync", "deep", "getActiveStackId", "getStack", "viewSavedData", "savedData", "primaryOutlet", "route", "_routerState", "snapshot", "savedExtras", "startBackTransition", "endBackTransition", "shouldComplete", "cleanup", "getLastUrl", "getRootUrl", "getActiveView", "hasRunningTask", "showGoBack", "progressAnimation", "enteringEl", "leavingEl", "commit", "duration", "task", "_this2", "promise", "finally", "activeRoute", "locationWithoutParams", "locationWithoutFragment", "setAttribute", "IonRouterOutlet", "nativeEl", "activatedView", "_swipeGesture", "stackCtrl", "proxyMap", "currentActivatedRoute$", "activated", "activatedComponentRef", "_activatedRoute", "stackWillChange", "stackDidChange", "activateEvents", "deactivateEvents", "parentContexts", "inputBinder", "INPUT_BINDER", "optional", "supportsBindingToComponentInputs", "swipe", "swi<PERSON><PERSON><PERSON><PERSON>", "canStart", "onStart", "onEnd", "shouldC<PERSON><PERSON>ue", "tabs", "commonLocation", "elementRef", "onChildOutletCreated", "ngOnDestroy", "unsubscribeFromRouteData", "getContext", "ngOnInit", "initializeOutletWithName", "context", "activateWith", "isActivated", "Error", "activatedRouteData", "attach", "_ref", "deactivate", "Map", "children", "contextSnapshot", "emit", "cmpRef", "saved", "updateActivatedRouteProxy", "_futureSnapshot", "childContexts", "getOrCreateContext", "component$", "activatedRouteProxy", "createActivatedRouteProxy", "OutletInjector", "routeConfig", "outletContent", "bindActivatedRouteToOutletComponent", "active", "getLastRouteView", "getRootView", "proxy", "_paramMap", "proxyObservable", "_queryParamMap", "pipe", "current", "IonRouterOutlet_Factory", "ɵɵinjectAttribute", "outputs", "exportAs", "notFoundValue", "RoutedComponentInputBinder", "outletDataSubscriptions", "subscribeToRouteData", "unsubscribe", "dataSubscription", "mirror", "templateName", "RoutedComponentInputBinder_Factory", "provideComponentInputBinding", "componentInputBindingFactory", "componentInputBindingEnabled", "BACK_BUTTON_INPUTS", "IonBackButton", "routerOutlet", "onClick", "defaultHref", "routerAnimation", "preventDefault", "IonBackButton_Factory", "hostBindings", "IonBackButton_HostBindings", "ɵɵlistener", "IonBackButton_click_HostBindingHandler", "$event", "color", "disabled", "icon", "text", "RouterLinkDelegateDirective", "locationStrategy", "routerLink", "routerDirection", "updateTargetUrlAndHref", "updateTabindex", "ngOnChanges", "ionicComponents", "getAttribute", "removeAttribute", "prepareExternalUrl", "RouterLinkDelegateDirective_Factory", "LocationStrategy", "RouterLink", "RouterLinkDelegateDirective_HostBindings", "RouterLinkDelegateDirective_click_HostBindingHandler", "features", "ɵɵNgOnChangesFeature", "RouterLinkWithHrefDelegateDirective", "RouterLinkWithHrefDelegateDirective_Factory", "RouterLinkWithHrefDelegateDirective_HostBindings", "RouterLinkWithHrefDelegateDirective_click_HostBindingHandler", "NAV_INPUTS", "NAV_METHODS", "IonNav", "angularDelegate", "delegate", "IonNav_Factory", "root", "rootParams", "IonTabs", "tabsInner", "ionTabsWillChange", "ionTabsDidChange", "tabBarSlot", "hasTab", "selectedTab", "leavingTab", "ngAfterViewInit", "firstTab", "setActiveTab", "tab", "ngAfterContentInit", "detectSlotChanges", "ngAfterContentChecked", "onStackWillChange", "onStackDidChange", "tabBar", "select", "tabOrEvent", "isTabString", "alreadySelected", "tabRootUrl", "stopPropagation", "activeStackId", "rootView", "navigationExtras", "lastRoute", "t", "getSelected", "tabBars", "currentSlot", "relocateTabBar", "before", "after", "IonTabs_Factory", "viewQuery", "IonTabs_Query", "ɵɵviewQuery", "IonTabs_HostBindings", "IonTabs_ionTabButtonClick_HostBindingHandler", "raf", "h", "__zone_symbol__requestAnimationFrame", "setTimeout", "ValueAccessor", "onChange", "onTouched", "lastValue", "statusChanges", "writeValue", "setIonicClasses", "handleValueChange", "_handleBlurEvent", "closest", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "ngControl", "formControl", "control", "methodsToPatch", "method", "oldFn", "bind", "ValueAccessor_Factory", "ValueAccessor_HostBindings", "ValueAccessor_ionBlur_HostBindingHandler", "target", "input", "hasValue", "classes", "getClasses", "setClasses", "startsWith", "substring", "remove", "search", "IonicRouteStrategy", "<PERSON><PERSON><PERSON><PERSON>", "_route", "<PERSON><PERSON><PERSON><PERSON>", "store", "_detachedTree", "retrieve", "shouldReuseRoute", "future", "curr", "futureParams", "currentParams", "keysA", "keys", "keysB", "OverlayBaseController", "ctrl", "dismiss", "role", "getTop"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/angular/fesm2022/ionic-angular-common.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\n\nclass MenuController {\n    menuController;\n    constructor(menuController) {\n        this.menuController = menuController;\n    }\n    /**\n     * Programmatically open the Menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully opened\n     */\n    open(menuId) {\n        return this.menuController.open(menuId);\n    }\n    /**\n     * Programmatically close the Menu. If no `menuId` is given as the first\n     * argument then it'll close any menu which is open. If a `menuId`\n     * is given then it'll close that exact menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully closed\n     */\n    close(menuId) {\n        return this.menuController.close(menuId);\n    }\n    /**\n     * Toggle the menu. If it's closed, it will open, and if opened, it\n     * will close.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu has been toggled\n     */\n    toggle(menuId) {\n        return this.menuController.toggle(menuId);\n    }\n    /**\n     * Used to enable or disable a menu. For example, there could be multiple\n     * left menus, but only one of them should be able to be opened at the same\n     * time. If there are multiple menus on the same side, then enabling one menu\n     * will also automatically disable all the others that are on the same side.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    enable(shouldEnable, menuId) {\n        return this.menuController.enable(shouldEnable, menuId);\n    }\n    /**\n     * Used to enable or disable the ability to swipe open the menu.\n     * @param shouldEnable  True if it should be swipe-able, false if not.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    swipeGesture(shouldEnable, menuId) {\n        return this.menuController.swipeGesture(shouldEnable, menuId);\n    }\n    /**\n     * @param [menuId] Optionally get the menu by its id, or side.\n     * @return Returns true if the specified menu is currently open, otherwise false.\n     * If the menuId is not specified, it returns true if ANY menu is currenly open.\n     */\n    isOpen(menuId) {\n        return this.menuController.isOpen(menuId);\n    }\n    /**\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns true if the menu is currently enabled, otherwise false.\n     */\n    isEnabled(menuId) {\n        return this.menuController.isEnabled(menuId);\n    }\n    /**\n     * Used to get a menu instance. If a `menuId` is not provided then it'll\n     * return the first menu found. If a `menuId` is `left` or `right`, then\n     * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n     * provided, then it'll try to find the menu using the menu's `id`\n     * property. If a menu is not found then it'll return `null`.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu if found, otherwise `null`.\n     */\n    get(menuId) {\n        return this.menuController.get(menuId);\n    }\n    /**\n     * @return Returns the instance of the menu already opened, otherwise `null`.\n     */\n    getOpen() {\n        return this.menuController.getOpen();\n    }\n    /**\n     * @return Returns an array of all menu instances.\n     */\n    getMenus() {\n        return this.menuController.getMenus();\n    }\n    registerAnimation(name, animation) {\n        return this.menuController.registerAnimation(name, animation);\n    }\n    isAnimating() {\n        return this.menuController.isAnimating();\n    }\n    _getOpenSync() {\n        return this.menuController._getOpenSync();\n    }\n    _createAnimation(type, menuCmp) {\n        return this.menuController._createAnimation(type, menuCmp);\n    }\n    _register(menu) {\n        return this.menuController._register(menu);\n    }\n    _unregister(menu) {\n        return this.menuController._unregister(menu);\n    }\n    _setOpen(menu, shouldOpen, animated) {\n        return this.menuController._setOpen(menu, shouldOpen, animated);\n    }\n}\n\nclass DomController {\n    /**\n     * Schedules a task to run during the READ phase of the next frame.\n     * This task should only read the DOM, but never modify it.\n     */\n    read(cb) {\n        getQueue().read(cb);\n    }\n    /**\n     * Schedules a task to run during the WRITE phase of the next frame.\n     * This task should write the DOM, but never READ it.\n     */\n    write(cb) {\n        getQueue().write(cb);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst getQueue = () => {\n    const win = typeof window !== 'undefined' ? window : null;\n    if (win != null) {\n        const Ionic = win.Ionic;\n        if (Ionic?.queue) {\n            return Ionic.queue;\n        }\n        return {\n            read: (cb) => win.requestAnimationFrame(cb),\n            write: (cb) => win.requestAnimationFrame(cb),\n        };\n    }\n    return {\n        read: (cb) => cb(),\n        write: (cb) => cb(),\n    };\n};\n\nclass Platform {\n    doc;\n    _readyPromise;\n    win;\n    /**\n     * @hidden\n     */\n    backButton = new Subject();\n    /**\n     * The keyboardDidShow event emits when the\n     * on-screen keyboard is presented.\n     */\n    keyboardDidShow = new Subject();\n    /**\n     * The keyboardDidHide event emits when the\n     * on-screen keyboard is hidden.\n     */\n    keyboardDidHide = new Subject();\n    /**\n     * The pause event emits when the native platform puts the application\n     * into the background, typically when the user switches to a different\n     * application. This event would emit when a Cordova app is put into\n     * the background, however, it would not fire on a standard web browser.\n     */\n    pause = new Subject();\n    /**\n     * The resume event emits when the native platform pulls the application\n     * out from the background. This event would emit when a Cordova app comes\n     * out from the background, however, it would not fire on a standard web browser.\n     */\n    resume = new Subject();\n    /**\n     * The resize event emits when the browser window has changed dimensions. This\n     * could be from a browser window being physically resized, or from a device\n     * changing orientation.\n     */\n    resize = new Subject();\n    constructor(doc, zone) {\n        this.doc = doc;\n        zone.run(() => {\n            this.win = doc.defaultView;\n            this.backButton.subscribeWithPriority = function (priority, callback) {\n                return this.subscribe((ev) => {\n                    return ev.register(priority, (processNextHandler) => zone.run(() => callback(processNextHandler)));\n                });\n            };\n            proxyEvent(this.pause, doc, 'pause', zone);\n            proxyEvent(this.resume, doc, 'resume', zone);\n            proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n            proxyEvent(this.resize, this.win, 'resize', zone);\n            proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n            proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n            let readyResolve;\n            this._readyPromise = new Promise((res) => {\n                readyResolve = res;\n            });\n            if (this.win?.['cordova']) {\n                doc.addEventListener('deviceready', () => {\n                    readyResolve('cordova');\n                }, { once: true });\n            }\n            else {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                readyResolve('dom');\n            }\n        });\n    }\n    /**\n     * @returns returns true/false based on platform.\n     * @description\n     * Depending on the platform the user is on, `is(platformName)` will\n     * return `true` or `false`. Note that the same app can return `true`\n     * for more than one platform name. For example, an app running from\n     * an iPad would return `true` for the platform names: `mobile`,\n     * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n     * from Cordova then `cordova` would be true, and if it was running\n     * from a web browser on the iPad then `mobileweb` would be `true`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     if (this.platform.is('ios')) {\n     *       // This will only print when on iOS\n     *       console.log('I am an iOS device!');\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * | Platform Name   | Description                        |\n     * |-----------------|------------------------------------|\n     * | android         | on a device running Android.       |\n     * | capacitor       | on a device running Capacitor.     |\n     * | cordova         | on a device running Cordova.       |\n     * | ios             | on a device running iOS.           |\n     * | ipad            | on an iPad device.                 |\n     * | iphone          | on an iPhone device.               |\n     * | phablet         | on a phablet device.               |\n     * | tablet          | on a tablet device.                |\n     * | electron        | in Electron on a desktop device.   |\n     * | pwa             | as a PWA app.                      |\n     * | mobile          | on a mobile device.                |\n     * | mobileweb       | on a mobile device in a browser.   |\n     * | desktop         | on a desktop device.               |\n     * | hybrid          | is a cordova or capacitor app.     |\n     *\n     */\n    is(platformName) {\n        return isPlatform(this.win, platformName);\n    }\n    /**\n     * @returns the array of platforms\n     * @description\n     * Depending on what device you are on, `platforms` can return multiple values.\n     * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n     * it would return `mobile`, `ios`, and `iphone`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     // This will print an array of the current platforms\n     *     console.log(this.platform.platforms());\n     *   }\n     * }\n     * ```\n     */\n    platforms() {\n        return getPlatforms(this.win);\n    }\n    /**\n     * Returns a promise when the platform is ready and native functionality\n     * can be called. If the app is running from within a web browser, then\n     * the promise will resolve when the DOM is ready. When the app is running\n     * from an application engine such as Cordova, then the promise will\n     * resolve when Cordova triggers the `deviceready` event.\n     *\n     * The resolved value is the `readySource`, which states which platform\n     * ready was used. For example, when Cordova is ready, the resolved ready\n     * source is `cordova`. The default ready source value will be `dom`. The\n     * `readySource` is useful if different logic should run depending on the\n     * platform the app is running from. For example, only Cordova can execute\n     * the status bar plugin, so the web should not run status bar plugin logic.\n     *\n     * ```\n     * import { Component } from '@angular/core';\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyApp {\n     *   constructor(public platform: Platform) {\n     *     this.platform.ready().then((readySource) => {\n     *       console.log('Platform ready from', readySource);\n     *       // Platform now ready, execute any required native code\n     *     });\n     *   }\n     * }\n     * ```\n     */\n    ready() {\n        return this._readyPromise;\n    }\n    /**\n     * Returns if this app is using right-to-left language direction or not.\n     * We recommend the app's `index.html` file already has the correct `dir`\n     * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n     * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n     */\n    get isRTL() {\n        return this.doc.dir === 'rtl';\n    }\n    /**\n     * Get the query string parameter\n     */\n    getQueryParam(key) {\n        return readQueryParam(this.win.location.href, key);\n    }\n    /**\n     * Returns `true` if the app is in landscape mode.\n     */\n    isLandscape() {\n        return !this.isPortrait();\n    }\n    /**\n     * Returns `true` if the app is in portrait mode.\n     */\n    isPortrait() {\n        return this.win.matchMedia?.('(orientation: portrait)').matches;\n    }\n    testUserAgent(expression) {\n        const nav = this.win.navigator;\n        return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n    }\n    /**\n     * Get the current url.\n     */\n    url() {\n        return this.win.location.href;\n    }\n    /**\n     * Gets the width of the platform's viewport using `window.innerWidth`.\n     */\n    width() {\n        return this.win.innerWidth;\n    }\n    /**\n     * Gets the height of the platform's viewport using `window.innerHeight`.\n     */\n    height() {\n        return this.win.innerHeight;\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, deps: [{ token: DOCUMENT }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }]; } });\nconst readQueryParam = (url, key) => {\n    key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n    const results = regex.exec(url);\n    return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n    if (el) {\n        el.addEventListener(eventName, (ev) => {\n            /**\n             * `zone.run` is required to make sure that we are running inside the Angular zone\n             * at all times. This is necessary since an app that has Capacitor will\n             * override the `document.addEventListener` with its own implementation.\n             * The override causes the event to no longer be in the Angular zone.\n             */\n            zone.run(() => {\n                // ?? cordova might emit \"null\" events\n                const value = ev != null ? ev.detail : undefined;\n                emitter.next(value);\n            });\n        });\n    }\n};\n\nclass NavController {\n    location;\n    serializer;\n    router;\n    topOutlet;\n    direction = DEFAULT_DIRECTION;\n    animated = DEFAULT_ANIMATED;\n    animationBuilder;\n    guessDirection = 'forward';\n    guessAnimation;\n    lastNavId = -1;\n    constructor(platform, location, serializer, router) {\n        this.location = location;\n        this.serializer = serializer;\n        this.router = router;\n        // Subscribe to router events to detect direction\n        if (router) {\n            router.events.subscribe((ev) => {\n                if (ev instanceof NavigationStart) {\n                    // restoredState is set if the browser back/forward button is used\n                    const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n                    this.guessDirection = this.guessAnimation = id < this.lastNavId ? 'back' : 'forward';\n                    this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n                }\n            });\n        }\n        // Subscribe to backButton events\n        platform.backButton.subscribeWithPriority(0, (processNextHandler) => {\n            this.pop();\n            processNextHandler();\n        });\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n     *\n     * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n     * and that it will show a \"forward\" animation by default.\n     *\n     * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n     * ```\n     */\n    navigateForward(url, options = {}) {\n        this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('back');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n     * and that it will show a \"back\" animation by default.\n     *\n     * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n     * ```\n     */\n    navigateBack(url, options = {}) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('root');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **root** means that all existing pages in the stack will be removed,\n     * and the navigated page will become the single page in the stack.\n     *\n     * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n     * ```\n     */\n    navigateRoot(url, options = {}) {\n        this.setDirection('root', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n     * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n     * by default.\n     */\n    back(options = { animated: true, animationDirection: 'back' }) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.location.back();\n    }\n    /**\n     * This methods goes back in the context of Ionic's stack navigation.\n     *\n     * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n     * This is the recommended way to go back when you are using `ion-router-outlet`.\n     *\n     * Resolves to `true` if it was able to pop.\n     */\n    async pop() {\n        let outlet = this.topOutlet;\n        while (outlet) {\n            if (await outlet.pop()) {\n                return true;\n            }\n            else {\n                outlet = outlet.parentOutlet;\n            }\n        }\n        return false;\n    }\n    /**\n     * This methods specifies the direction of the next navigation performed by the Angular router.\n     *\n     * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n     *\n     * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n     */\n    setDirection(direction, animated, animationDirection, animationBuilder) {\n        this.direction = direction;\n        this.animated = getAnimation(direction, animated, animationDirection);\n        this.animationBuilder = animationBuilder;\n    }\n    /**\n     * @internal\n     */\n    setTopOutlet(outlet) {\n        this.topOutlet = outlet;\n    }\n    /**\n     * @internal\n     */\n    consumeTransition() {\n        let direction = 'root';\n        let animation;\n        const animationBuilder = this.animationBuilder;\n        if (this.direction === 'auto') {\n            direction = this.guessDirection;\n            animation = this.guessAnimation;\n        }\n        else {\n            animation = this.animated;\n            direction = this.direction;\n        }\n        this.direction = DEFAULT_DIRECTION;\n        this.animated = DEFAULT_ANIMATED;\n        this.animationBuilder = undefined;\n        return {\n            direction,\n            animation,\n            animationBuilder,\n        };\n    }\n    navigate(url, options) {\n        if (Array.isArray(url)) {\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigate(url, options);\n        }\n        else {\n            /**\n             * navigateByUrl ignores any properties that\n             * would change the url, so things like queryParams\n             * would be ignored unless we create a url tree\n             * More Info: https://github.com/angular/angular/issues/18798\n             */\n            const urlTree = this.serializer.parse(url.toString());\n            if (options.queryParams !== undefined) {\n                urlTree.queryParams = { ...options.queryParams };\n            }\n            if (options.fragment !== undefined) {\n                urlTree.fragment = options.fragment;\n            }\n            /**\n             * `navigateByUrl` will still apply `NavigationExtras` properties\n             * that do not modify the url, such as `replaceUrl` which is why\n             * `options` is passed in here.\n             */\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigateByUrl(urlTree, options);\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, deps: [{ token: Platform }, { token: i1.Location }, { token: i3.UrlSerializer }, { token: i3.Router, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: Platform }, { type: i1.Location }, { type: i3.UrlSerializer }, { type: i3.Router, decorators: [{\n                    type: Optional\n                }] }]; } });\nconst getAnimation = (direction, animated, animationDirection) => {\n    if (animated === false) {\n        return undefined;\n    }\n    if (animationDirection !== undefined) {\n        return animationDirection;\n    }\n    if (direction === 'forward' || direction === 'back') {\n        return direction;\n    }\n    else if (direction === 'root' && animated === true) {\n        return 'forward';\n    }\n    return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\n\nclass Config {\n    get(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.get(key, fallback);\n        }\n        return null;\n    }\n    getBoolean(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getBoolean(key, fallback);\n        }\n        return false;\n    }\n    getNumber(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getNumber(key, fallback);\n        }\n        return 0;\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n    if (typeof window !== 'undefined') {\n        const Ionic = window.Ionic;\n        if (Ionic?.config) {\n            return Ionic.config;\n        }\n    }\n    return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n    data;\n    constructor(data = {}) {\n        this.data = data;\n        console.warn(`[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.`);\n    }\n    /**\n     * Get the value of a nav-parameter for the current view\n     *\n     * ```ts\n     * import { NavParams } from 'ionic-angular';\n     *\n     * export class MyClass{\n     *  constructor(public navParams: NavParams){\n     *    // userParams is an object we have in our nav-parameters\n     *    this.navParams.get('userParams');\n     *  }\n     * }\n     * ```\n     *\n     * @param param Which param you want to look up\n     */\n    get(param) {\n        return this.data[param];\n    }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n    zone = inject(NgZone);\n    applicationRef = inject(ApplicationRef);\n    config = inject(ConfigToken);\n    create(environmentInjector, injector, elementReferenceKey) {\n        return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey, this.config.useSetInputAPI ?? false);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate, decorators: [{\n            type: Injectable\n        }] });\nclass AngularFrameworkDelegate {\n    environmentInjector;\n    injector;\n    applicationRef;\n    zone;\n    elementReferenceKey;\n    enableSignalsSupport;\n    elRefMap = new WeakMap();\n    elEventsMap = new WeakMap();\n    constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey, enableSignalsSupport) {\n        this.environmentInjector = environmentInjector;\n        this.injector = injector;\n        this.applicationRef = applicationRef;\n        this.zone = zone;\n        this.elementReferenceKey = elementReferenceKey;\n        this.enableSignalsSupport = enableSignalsSupport;\n    }\n    attachViewToDom(container, component, params, cssClasses) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentProps = {\n                    ...params,\n                };\n                /**\n                 * Ionic Angular passes a reference to a modal\n                 * or popover that can be accessed using a\n                 * variable in the overlay component. If\n                 * elementReferenceKey is defined, then we should\n                 * pass a reference to the component using\n                 * elementReferenceKey as the key.\n                 */\n                if (this.elementReferenceKey !== undefined) {\n                    componentProps[this.elementReferenceKey] = container;\n                }\n                const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey, this.enableSignalsSupport);\n                resolve(el);\n            });\n        });\n    }\n    removeViewFromDom(_container, component) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentRef = this.elRefMap.get(component);\n                if (componentRef) {\n                    componentRef.destroy();\n                    this.elRefMap.delete(component);\n                    const unbindEvents = this.elEventsMap.get(component);\n                    if (unbindEvents) {\n                        unbindEvents();\n                        this.elEventsMap.delete(component);\n                    }\n                }\n                resolve();\n            });\n        });\n    }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey, enableSignalsSupport) => {\n    /**\n     * Wraps the injector with a custom injector that\n     * provides NavParams to the component.\n     *\n     * NavParams is a legacy feature from Ionic v3 that allows\n     * Angular developers to provide data to a component\n     * and access it by providing NavParams as a dependency\n     * in the constructor.\n     *\n     * The modern approach is to access the data directly\n     * from the component's class instance.\n     */\n    const childInjector = Injector.create({\n        providers: getProviders(params),\n        parent: injector,\n    });\n    const componentRef = createComponent(component, {\n        environmentInjector,\n        elementInjector: childInjector,\n    });\n    const instance = componentRef.instance;\n    const hostElement = componentRef.location.nativeElement;\n    if (params) {\n        /**\n         * For modals and popovers, a reference to the component is\n         * added to `params` during the call to attachViewToDom. If\n         * a reference using this name is already set, this means\n         * the app is trying to use the name as a component prop,\n         * which will cause collisions.\n         */\n        if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n            console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n        }\n        /**\n         * Angular 14.1 added support for setInput\n         * so we need to fall back to Object.assign\n         * for Angular 14.0.\n         */\n        if (enableSignalsSupport === true && componentRef.setInput !== undefined) {\n            const { modal, popover, ...otherParams } = params;\n            /**\n             * Any key/value pairs set in componentProps\n             * must be set as inputs on the component instance.\n             */\n            for (const key in otherParams) {\n                componentRef.setInput(key, otherParams[key]);\n            }\n            /**\n             * Using setInput will cause an error when\n             * setting modal/popover on a component that\n             * does not define them as an input. For backwards\n             * compatibility purposes we fall back to using\n             * Object.assign for these properties.\n             */\n            if (modal !== undefined) {\n                Object.assign(instance, { modal });\n            }\n            if (popover !== undefined) {\n                Object.assign(instance, { popover });\n            }\n        }\n        else {\n            Object.assign(instance, params);\n        }\n    }\n    if (cssClasses) {\n        for (const cssClass of cssClasses) {\n            hostElement.classList.add(cssClass);\n        }\n    }\n    const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n    container.appendChild(hostElement);\n    applicationRef.attachView(componentRef.hostView);\n    elRefMap.set(hostElement, componentRef);\n    elEventsMap.set(hostElement, unbindEvents);\n    return hostElement;\n};\nconst LIFECYCLES = [\n    LIFECYCLE_WILL_ENTER,\n    LIFECYCLE_DID_ENTER,\n    LIFECYCLE_WILL_LEAVE,\n    LIFECYCLE_DID_LEAVE,\n    LIFECYCLE_WILL_UNLOAD,\n];\nconst bindLifecycleEvents = (zone, instance, element) => {\n    return zone.run(() => {\n        const unregisters = LIFECYCLES.filter((eventName) => typeof instance[eventName] === 'function').map((eventName) => {\n            const handler = (ev) => instance[eventName](ev.detail);\n            element.addEventListener(eventName, handler);\n            return () => element.removeEventListener(eventName, handler);\n        });\n        return () => unregisters.forEach((fn) => fn());\n    });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = (params) => {\n    return [\n        {\n            provide: NavParamsToken,\n            useValue: params,\n        },\n        {\n            provide: NavParams,\n            useFactory: provideNavParamsInjectable,\n            deps: [NavParamsToken],\n        },\n    ];\n};\nconst provideNavParamsInjectable = (params) => {\n    return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\n/* eslint-disable */\n/* tslint:disable */\nconst proxyInputs = (Cmp, inputs) => {\n    const Prototype = Cmp.prototype;\n    inputs.forEach((item) => {\n        Object.defineProperty(Prototype, item, {\n            get() {\n                return this.el[item];\n            },\n            set(val) {\n                this.z.runOutsideAngular(() => (this.el[item] = val));\n            },\n        });\n    });\n};\nconst proxyMethods = (Cmp, methods) => {\n    const Prototype = Cmp.prototype;\n    methods.forEach((methodName) => {\n        Prototype[methodName] = function () {\n            const args = arguments;\n            return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n        };\n    });\n};\nconst proxyOutputs = (instance, el, events) => {\n    events.forEach((eventName) => (instance[eventName] = fromEvent(el, eventName)));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n    const decorator = function (cls) {\n        const { defineCustomElementFn, inputs, methods } = opts;\n        if (defineCustomElementFn !== undefined) {\n            defineCustomElementFn();\n        }\n        if (inputs) {\n            proxyInputs(cls, inputs);\n        }\n        if (methods) {\n            proxyMethods(cls, methods);\n        }\n        return cls;\n    };\n    return decorator;\n}\n\nconst POPOVER_INPUTS = [\n    'alignment',\n    'animated',\n    'arrow',\n    'keepContentsMounted',\n    'backdropDismiss',\n    'cssClass',\n    'dismissOnSelect',\n    'enterAnimation',\n    'event',\n    'focusTrap',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n    'triggerAction',\n    'reference',\n    'size',\n    'side',\n];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n    z;\n    // TODO(FW-2827): type\n    template;\n    isCmpOpen = false;\n    el;\n    constructor(c, r, z) {\n        this.z = z;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionPopoverDidPresent',\n            'ionPopoverWillPresent',\n            'ionPopoverWillDismiss',\n            'ionPopoverDidDismiss',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonPopover, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonPopover, selector: \"ion-popover\", inputs: { alignment: \"alignment\", animated: \"animated\", arrow: \"arrow\", keepContentsMounted: \"keepContentsMounted\", backdropDismiss: \"backdropDismiss\", cssClass: \"cssClass\", dismissOnSelect: \"dismissOnSelect\", enterAnimation: \"enterAnimation\", event: \"event\", focusTrap: \"focusTrap\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\", triggerAction: \"triggerAction\", reference: \"reference\", size: \"size\", side: \"side\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\n};\nIonPopover = __decorate([\n    ProxyCmp({\n        inputs: POPOVER_INPUTS,\n        methods: POPOVER_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonPopover);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonPopover, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-popover',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: POPOVER_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst MODAL_INPUTS = [\n    'animated',\n    'keepContentsMounted',\n    'backdropBreakpoint',\n    'backdropDismiss',\n    'breakpoints',\n    'canDismiss',\n    'cssClass',\n    'enterAnimation',\n    'expandToScroll',\n    'event',\n    'focusTrap',\n    'handle',\n    'handleBehavior',\n    'initialBreakpoint',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'presentingElement',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n];\nconst MODAL_METHODS = [\n    'present',\n    'dismiss',\n    'onDidDismiss',\n    'onWillDismiss',\n    'setCurrentBreakpoint',\n    'getCurrentBreakpoint',\n];\nlet IonModal = class IonModal {\n    z;\n    // TODO(FW-2827): type\n    template;\n    isCmpOpen = false;\n    el;\n    constructor(c, r, z) {\n        this.z = z;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionModalDidPresent',\n            'ionModalWillPresent',\n            'ionModalWillDismiss',\n            'ionModalDidDismiss',\n            'ionBreakpointDidChange',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonModal, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonModal, selector: \"ion-modal\", inputs: { animated: \"animated\", keepContentsMounted: \"keepContentsMounted\", backdropBreakpoint: \"backdropBreakpoint\", backdropDismiss: \"backdropDismiss\", breakpoints: \"breakpoints\", canDismiss: \"canDismiss\", cssClass: \"cssClass\", enterAnimation: \"enterAnimation\", expandToScroll: \"expandToScroll\", event: \"event\", focusTrap: \"focusTrap\", handle: \"handle\", handleBehavior: \"handleBehavior\", initialBreakpoint: \"initialBreakpoint\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", presentingElement: \"presentingElement\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\n};\nIonModal = __decorate([\n    ProxyCmp({\n        inputs: MODAL_INPUTS,\n        methods: MODAL_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonModal);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonModal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-modal',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: MODAL_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst insertView = (views, view, direction) => {\n    if (direction === 'root') {\n        return setRoot(views, view);\n    }\n    else if (direction === 'forward') {\n        return setForward(views, view);\n    }\n    else {\n        return setBack(views, view);\n    }\n};\nconst setRoot = (views, view) => {\n    views = views.filter((v) => v.stackId !== view.stackId);\n    views.push(view);\n    return views;\n};\nconst setForward = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        views = views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        views.push(view);\n    }\n    return views;\n};\nconst setBack = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        return views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        return setRoot(views, view);\n    }\n};\nconst getUrl = (router, activatedRoute) => {\n    const urlTree = router.createUrlTree(['.'], { relativeTo: activatedRoute });\n    return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n    if (!leavingView) {\n        return true;\n    }\n    return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n    if (!prefixUrl) {\n        return undefined;\n    }\n    const segments = toSegments(url);\n    for (let i = 0; i < segments.length; i++) {\n        if (i >= prefixUrl.length) {\n            return segments[i];\n        }\n        if (segments[i] !== prefixUrl[i]) {\n            return undefined;\n        }\n    }\n    return undefined;\n};\nconst toSegments = (path) => {\n    return path\n        .split('/')\n        .map((s) => s.trim())\n        .filter((s) => s !== '');\n};\nconst destroyView = (view) => {\n    if (view) {\n        view.ref.destroy();\n        view.unlistenEvents();\n    }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n    containerEl;\n    router;\n    navCtrl;\n    zone;\n    location;\n    views = [];\n    runningTask;\n    skipTransition = false;\n    tabsPrefix;\n    activeView;\n    nextId = 0;\n    constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n        this.containerEl = containerEl;\n        this.router = router;\n        this.navCtrl = navCtrl;\n        this.zone = zone;\n        this.location = location;\n        this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n    }\n    createView(ref, activatedRoute) {\n        const url = getUrl(this.router, activatedRoute);\n        const element = ref?.location?.nativeElement;\n        const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n        return {\n            id: this.nextId++,\n            stackId: computeStackId(this.tabsPrefix, url),\n            unlistenEvents,\n            element,\n            ref,\n            url,\n        };\n    }\n    getExistingView(activatedRoute) {\n        const activatedUrlKey = getUrl(this.router, activatedRoute);\n        const view = this.views.find((vw) => vw.url === activatedUrlKey);\n        if (view) {\n            view.ref.changeDetectorRef.reattach();\n        }\n        return view;\n    }\n    setActive(enteringView) {\n        const consumeResult = this.navCtrl.consumeTransition();\n        let { direction, animation, animationBuilder } = consumeResult;\n        const leavingView = this.activeView;\n        const tabSwitch = isTabSwitch(enteringView, leavingView);\n        if (tabSwitch) {\n            direction = 'back';\n            animation = undefined;\n        }\n        const viewsSnapshot = this.views.slice();\n        let currentNavigation;\n        const router = this.router;\n        // Angular >= 7.2.0\n        if (router.getCurrentNavigation) {\n            currentNavigation = router.getCurrentNavigation();\n            // Angular < 7.2.0\n        }\n        else if (router.navigations?.value) {\n            currentNavigation = router.navigations.value;\n        }\n        /**\n         * If the navigation action\n         * sets `replaceUrl: true`\n         * then we need to make sure\n         * we remove the last item\n         * from our views stack\n         */\n        if (currentNavigation?.extras?.replaceUrl) {\n            if (this.views.length > 0) {\n                this.views.splice(-1, 1);\n            }\n        }\n        const reused = this.views.includes(enteringView);\n        const views = this.insertView(enteringView, direction);\n        // Trigger change detection before transition starts\n        // This will call ngOnInit() the first time too, just after the view\n        // was attached to the dom, but BEFORE the transition starts\n        if (!reused) {\n            enteringView.ref.changeDetectorRef.detectChanges();\n        }\n        /**\n         * If we are going back from a page that\n         * was presented using a custom animation\n         * we should default to using that\n         * unless the developer explicitly\n         * provided another animation.\n         */\n        const customAnimation = enteringView.animationBuilder;\n        if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n            animationBuilder = customAnimation;\n        }\n        /**\n         * Save any custom animation so that navigating\n         * back will use this custom animation by default.\n         */\n        if (leavingView) {\n            leavingView.animationBuilder = animationBuilder;\n        }\n        // Wait until previous transitions finish\n        return this.zone.runOutsideAngular(() => {\n            return this.wait(() => {\n                // disconnect leaving page from change detection to\n                // reduce jank during the page transition\n                if (leavingView) {\n                    leavingView.ref.changeDetectorRef.detach();\n                }\n                // In case the enteringView is the same as the leavingPage we need to reattach()\n                enteringView.ref.changeDetectorRef.reattach();\n                return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder)\n                    .then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone))\n                    .then(() => ({\n                    enteringView,\n                    direction,\n                    animation,\n                    tabSwitch,\n                }));\n            });\n        });\n    }\n    canGoBack(deep, stackId = this.getActiveStackId()) {\n        return this.getStack(stackId).length > deep;\n    }\n    pop(deep, stackId = this.getActiveStackId()) {\n        return this.zone.run(() => {\n            const views = this.getStack(stackId);\n            if (views.length <= deep) {\n                return Promise.resolve(false);\n            }\n            const view = views[views.length - deep - 1];\n            let url = view.url;\n            const viewSavedData = view.savedData;\n            if (viewSavedData) {\n                const primaryOutlet = viewSavedData.get('primary');\n                if (primaryOutlet?.route?._routerState?.snapshot.url) {\n                    url = primaryOutlet.route._routerState.snapshot.url;\n                }\n            }\n            const { animationBuilder } = this.navCtrl.consumeTransition();\n            return this.navCtrl.navigateBack(url, { ...view.savedExtras, animation: animationBuilder }).then(() => true);\n        });\n    }\n    startBackTransition() {\n        const leavingView = this.activeView;\n        if (leavingView) {\n            const views = this.getStack(leavingView.stackId);\n            const enteringView = views[views.length - 2];\n            const customAnimation = enteringView.animationBuilder;\n            return this.wait(() => {\n                return this.transition(enteringView, // entering view\n                leavingView, // leaving view\n                'back', this.canGoBack(2), true, customAnimation);\n            });\n        }\n        return Promise.resolve();\n    }\n    endBackTransition(shouldComplete) {\n        if (shouldComplete) {\n            this.skipTransition = true;\n            this.pop(1);\n        }\n        else if (this.activeView) {\n            cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n        }\n    }\n    getLastUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[views.length - 1] : undefined;\n    }\n    /**\n     * @internal\n     */\n    getRootUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[0] : undefined;\n    }\n    getActiveStackId() {\n        return this.activeView ? this.activeView.stackId : undefined;\n    }\n    /**\n     * @internal\n     */\n    getActiveView() {\n        return this.activeView;\n    }\n    hasRunningTask() {\n        return this.runningTask !== undefined;\n    }\n    destroy() {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        this.containerEl = undefined;\n        this.views.forEach(destroyView);\n        this.activeView = undefined;\n        this.views = [];\n    }\n    getStack(stackId) {\n        return this.views.filter((v) => v.stackId === stackId);\n    }\n    insertView(enteringView, direction) {\n        this.activeView = enteringView;\n        this.views = insertView(this.views, enteringView, direction);\n        return this.views.slice();\n    }\n    transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n        if (this.skipTransition) {\n            this.skipTransition = false;\n            return Promise.resolve(false);\n        }\n        if (leavingView === enteringView) {\n            return Promise.resolve(false);\n        }\n        const enteringEl = enteringView ? enteringView.element : undefined;\n        const leavingEl = leavingView ? leavingView.element : undefined;\n        const containerEl = this.containerEl;\n        if (enteringEl && enteringEl !== leavingEl) {\n            enteringEl.classList.add('ion-page');\n            enteringEl.classList.add('ion-page-invisible');\n            if (containerEl.commit) {\n                return containerEl.commit(enteringEl, leavingEl, {\n                    duration: direction === undefined ? 0 : undefined,\n                    direction,\n                    showGoBack,\n                    progressAnimation,\n                    animationBuilder,\n                });\n            }\n        }\n        return Promise.resolve(false);\n    }\n    async wait(task) {\n        if (this.runningTask !== undefined) {\n            await this.runningTask;\n            this.runningTask = undefined;\n        }\n        const promise = (this.runningTask = task());\n        promise.finally(() => (this.runningTask = undefined));\n        return promise;\n    }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n    if (typeof requestAnimationFrame === 'function') {\n        return new Promise((resolve) => {\n            requestAnimationFrame(() => {\n                cleanup(activeRoute, views, viewsSnapshot, location, zone);\n                resolve();\n            });\n        });\n    }\n    return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n    /**\n     * Re-enter the Angular zone when destroying page components. This will allow\n     * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n     */\n    zone.run(() => viewsSnapshot.filter((view) => !views.includes(view)).forEach(destroyView));\n    views.forEach((view) => {\n        /**\n         * In the event that a user navigated multiple\n         * times in rapid succession, we want to make sure\n         * we don't pre-emptively detach a view while\n         * it is in mid-transition.\n         *\n         * In this instance we also do not care about query\n         * params or fragments as it will be the same view regardless\n         */\n        const locationWithoutParams = location.path().split('?')[0];\n        const locationWithoutFragment = locationWithoutParams.split('#')[0];\n        if (view !== activeRoute && view.url !== locationWithoutFragment) {\n            const element = view.element;\n            element.setAttribute('aria-hidden', 'true');\n            element.classList.add('ion-page-hidden');\n            view.ref.changeDetectorRef.detach();\n        }\n    });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n    parentOutlet;\n    nativeEl;\n    activatedView = null;\n    tabsPrefix;\n    _swipeGesture;\n    stackCtrl;\n    // Maintain map of activated route proxies for each component instance\n    proxyMap = new WeakMap();\n    // Keep the latest activated route in a subject for the proxy routes to switch map to\n    currentActivatedRoute$ = new BehaviorSubject(null);\n    activated = null;\n    /** @internal */\n    get activatedComponentRef() {\n        return this.activated;\n    }\n    _activatedRoute = null;\n    /**\n     * The name of the outlet\n     */\n    name = PRIMARY_OUTLET;\n    /** @internal */\n    stackWillChange = new EventEmitter();\n    /** @internal */\n    stackDidChange = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    activateEvents = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    deactivateEvents = new EventEmitter();\n    parentContexts = inject(ChildrenOutletContexts);\n    location = inject(ViewContainerRef);\n    environmentInjector = inject(EnvironmentInjector);\n    inputBinder = inject(INPUT_BINDER, { optional: true });\n    /** @nodoc */\n    supportsBindingToComponentInputs = true;\n    // Ionic providers\n    config = inject(Config);\n    navCtrl = inject(NavController);\n    set animation(animation) {\n        this.nativeEl.animation = animation;\n    }\n    set animated(animated) {\n        this.nativeEl.animated = animated;\n    }\n    set swipeGesture(swipe) {\n        this._swipeGesture = swipe;\n        this.nativeEl.swipeHandler = swipe\n            ? {\n                canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n                onStart: () => this.stackCtrl.startBackTransition(),\n                onEnd: (shouldContinue) => this.stackCtrl.endBackTransition(shouldContinue),\n            }\n            : undefined;\n    }\n    constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n        this.parentOutlet = parentOutlet;\n        this.nativeEl = elementRef.nativeElement;\n        this.name = name || PRIMARY_OUTLET;\n        this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n        this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n        this.parentContexts.onChildOutletCreated(this.name, this);\n    }\n    ngOnDestroy() {\n        this.stackCtrl.destroy();\n        this.inputBinder?.unsubscribeFromRouteData(this);\n    }\n    getContext() {\n        return this.parentContexts.getContext(this.name);\n    }\n    ngOnInit() {\n        this.initializeOutletWithName();\n    }\n    // Note: Ionic deviates from the Angular Router implementation here\n    initializeOutletWithName() {\n        if (!this.activated) {\n            // If the outlet was not instantiated at the time the route got activated we need to populate\n            // the outlet when it is initialized (ie inside a NgIf)\n            const context = this.getContext();\n            if (context?.route) {\n                this.activateWith(context.route, context.injector);\n            }\n        }\n        new Promise((resolve) => componentOnReady(this.nativeEl, resolve)).then(() => {\n            if (this._swipeGesture === undefined) {\n                this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n            }\n        });\n    }\n    get isActivated() {\n        return !!this.activated;\n    }\n    get component() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this.activated.instance;\n    }\n    get activatedRoute() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this._activatedRoute;\n    }\n    get activatedRouteData() {\n        if (this._activatedRoute) {\n            return this._activatedRoute.snapshot.data;\n        }\n        return {};\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to detach the subtree\n     */\n    detach() {\n        throw new Error('incompatible reuse strategy');\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    attach(_ref, _activatedRoute) {\n        throw new Error('incompatible reuse strategy');\n    }\n    deactivate() {\n        if (this.activated) {\n            if (this.activatedView) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                this.activatedView.savedData = new Map(context.children['contexts']);\n                /**\n                 * Angular v11.2.10 introduced a change\n                 * where this route context is cleared out when\n                 * a router-outlet is deactivated, However,\n                 * we need this route information in order to\n                 * return a user back to the correct tab when\n                 * leaving and then going back to the tab context.\n                 */\n                const primaryOutlet = this.activatedView.savedData.get('primary');\n                if (primaryOutlet && context.route) {\n                    primaryOutlet.route = { ...context.route };\n                }\n                /**\n                 * Ensure we are saving the NavigationExtras\n                 * data otherwise it will be lost\n                 */\n                this.activatedView.savedExtras = {};\n                if (context.route) {\n                    const contextSnapshot = context.route.snapshot;\n                    this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n                    this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n                }\n            }\n            const c = this.component;\n            this.activatedView = null;\n            this.activated = null;\n            this._activatedRoute = null;\n            this.deactivateEvents.emit(c);\n        }\n    }\n    activateWith(activatedRoute, environmentInjector) {\n        if (this.isActivated) {\n            throw new Error('Cannot activate an already activated outlet');\n        }\n        this._activatedRoute = activatedRoute;\n        let cmpRef;\n        let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n        if (enteringView) {\n            cmpRef = this.activated = enteringView.ref;\n            const saved = enteringView.savedData;\n            if (saved) {\n                // self-restore\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                context.children['contexts'] = saved;\n            }\n            // Updated activated route proxy for this component\n            this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n        }\n        else {\n            const snapshot = activatedRoute._futureSnapshot;\n            /**\n             * Angular 14 introduces a new `loadComponent` property to the route config.\n             * This function will assign a `component` property to the route snapshot.\n             * We check for the presence of this property to determine if the route is\n             * using standalone components.\n             */\n            const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n            // We create an activated route proxy object that will maintain future updates for this component\n            // over its lifecycle in the stack.\n            const component$ = new BehaviorSubject(null);\n            const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n            const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const component = snapshot.routeConfig.component ?? snapshot.component;\n            /**\n             * View components need to be added as a child of ion-router-outlet\n             * for page transitions and swipe to go back.\n             * However, createComponent mounts components as siblings of the\n             * ViewContainerRef. As a result, outletContent must reference\n             * an ng-container inside of ion-router-outlet and not\n             * ion-router-outlet itself.\n             */\n            cmpRef = this.activated = this.outletContent.createComponent(component, {\n                index: this.outletContent.length,\n                injector,\n                environmentInjector: environmentInjector ?? this.environmentInjector,\n            });\n            // Once the component is created we can push it to our local subject supplied to the proxy\n            component$.next(cmpRef.instance);\n            // Calling `markForCheck` to make sure we will run the change detection when the\n            // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n            /**\n             * At this point this.activated has been set earlier\n             * in this function, so it is guaranteed to be non-null.\n             */\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n            // Store references to the proxy by component\n            this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n            this.currentActivatedRoute$.next({ component: cmpRef.instance, activatedRoute });\n        }\n        this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n        this.activatedView = enteringView;\n        /**\n         * The top outlet is set prior to the entering view's transition completing,\n         * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n         * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n         * activated.\n         *\n         * In this scenario, activeWith is called for both the tabs and the root router outlet.\n         * To avoid a race condition, we assign the top outlet synchronously.\n         */\n        this.navCtrl.setTopOutlet(this);\n        const leavingView = this.stackCtrl.getActiveView();\n        this.stackWillChange.emit({\n            enteringView,\n            tabSwitch: isTabSwitch(enteringView, leavingView),\n        });\n        this.stackCtrl.setActive(enteringView).then((data) => {\n            this.activateEvents.emit(cmpRef.instance);\n            this.stackDidChange.emit(data);\n        });\n    }\n    /**\n     * Returns `true` if there are pages in the stack to go back.\n     */\n    canGoBack(deep = 1, stackId) {\n        return this.stackCtrl.canGoBack(deep, stackId);\n    }\n    /**\n     * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n     */\n    pop(deep = 1, stackId) {\n        return this.stackCtrl.pop(deep, stackId);\n    }\n    /**\n     * Returns the URL of the active page of each stack.\n     */\n    getLastUrl(stackId) {\n        const active = this.stackCtrl.getLastUrl(stackId);\n        return active ? active.url : undefined;\n    }\n    /**\n     * Returns the RouteView of the active page of each stack.\n     * @internal\n     */\n    getLastRouteView(stackId) {\n        return this.stackCtrl.getLastUrl(stackId);\n    }\n    /**\n     * Returns the root view in the tab stack.\n     * @internal\n     */\n    getRootView(stackId) {\n        return this.stackCtrl.getRootUrl(stackId);\n    }\n    /**\n     * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n     */\n    getActiveStackId() {\n        return this.stackCtrl.getActiveStackId();\n    }\n    /**\n     * Since the activated route can change over the life time of a component in an ion router outlet, we create\n     * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n     */\n    createActivatedRouteProxy(component$, activatedRoute) {\n        const proxy = new ActivatedRoute();\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n        proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n        proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n        proxy.url = this.proxyObservable(component$, 'url');\n        proxy.params = this.proxyObservable(component$, 'params');\n        proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n        proxy.fragment = this.proxyObservable(component$, 'fragment');\n        proxy.data = this.proxyObservable(component$, 'data');\n        return proxy;\n    }\n    /**\n     * Create a wrapped observable that will switch to the latest activated route matched by the given component\n     */\n    proxyObservable(component$, path) {\n        return component$.pipe(\n        // First wait until the component instance is pushed\n        filter((component) => !!component), switchMap((component) => this.currentActivatedRoute$.pipe(filter((current) => current !== null && current.component === component), switchMap((current) => current && current.activatedRoute[path]), distinctUntilChanged())));\n    }\n    /**\n     * Updates the activated route proxy for the given component to the new incoming router state\n     */\n    updateActivatedRouteProxy(component, activatedRoute) {\n        const proxy = this.proxyMap.get(component);\n        if (!proxy) {\n            throw new Error(`Could not find activated route proxy for view`);\n        }\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        this.currentActivatedRoute$.next({ component, activatedRoute });\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonRouterOutlet, deps: [{ token: 'name', attribute: true }, { token: 'tabs', attribute: true, optional: true }, { token: i1.Location }, { token: i0.ElementRef }, { token: i3.Router }, { token: i0.NgZone }, { token: i3.ActivatedRoute }, { token: IonRouterOutlet, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonRouterOutlet, selector: \"ion-router-outlet\", inputs: { animated: \"animated\", animation: \"animation\", mode: \"mode\", swipeGesture: \"swipeGesture\", name: \"name\" }, outputs: { stackWillChange: \"stackWillChange\", stackDidChange: \"stackDidChange\", activateEvents: \"activate\", deactivateEvents: \"deactivate\" }, exportAs: [\"outlet\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonRouterOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-router-outlet',\n                    exportAs: 'outlet',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: ['animated', 'animation', 'mode', 'swipeGesture'],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['name']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Attribute,\n                    args: ['tabs']\n                }] }, { type: i1.Location }, { type: i0.ElementRef }, { type: i3.Router }, { type: i0.NgZone }, { type: i3.ActivatedRoute }, { type: IonRouterOutlet, decorators: [{\n                    type: SkipSelf\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { name: [{\n                type: Input\n            }], stackWillChange: [{\n                type: Output\n            }], stackDidChange: [{\n                type: Output\n            }], activateEvents: [{\n                type: Output,\n                args: ['activate']\n            }], deactivateEvents: [{\n                type: Output,\n                args: ['deactivate']\n            }] } });\nclass OutletInjector {\n    route;\n    childContexts;\n    parent;\n    constructor(route, childContexts, parent) {\n        this.route = route;\n        this.childContexts = childContexts;\n        this.parent = parent;\n    }\n    get(token, notFoundValue) {\n        if (token === ActivatedRoute) {\n            return this.route;\n        }\n        if (token === ChildrenOutletContexts) {\n            return this.childContexts;\n        }\n        return this.parent.get(token, notFoundValue);\n    }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n    outletDataSubscriptions = new Map();\n    bindActivatedRouteToOutletComponent(outlet) {\n        this.unsubscribeFromRouteData(outlet);\n        this.subscribeToRouteData(outlet);\n    }\n    unsubscribeFromRouteData(outlet) {\n        this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n        this.outletDataSubscriptions.delete(outlet);\n    }\n    subscribeToRouteData(outlet) {\n        const { activatedRoute } = outlet;\n        const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data])\n            .pipe(switchMap(([queryParams, params, data], index) => {\n            data = { ...queryParams, ...params, ...data };\n            // Get the first result from the data subscription synchronously so it's available to\n            // the component as soon as possible (and doesn't require a second change detection).\n            if (index === 0) {\n                return of(data);\n            }\n            // Promise.resolve is used to avoid synchronously writing the wrong data when\n            // two of the Observables in the `combineLatest` stream emit one after\n            // another.\n            return Promise.resolve(data);\n        }))\n            .subscribe((data) => {\n            // Outlet may have been deactivated or changed names to be associated with a different\n            // route\n            if (!outlet.isActivated ||\n                !outlet.activatedComponentRef ||\n                outlet.activatedRoute !== activatedRoute ||\n                activatedRoute.component === null) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            const mirror = reflectComponentType(activatedRoute.component);\n            if (!mirror) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            for (const { templateName } of mirror.inputs) {\n                outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n            }\n        });\n        this.outletDataSubscriptions.set(outlet, dataSubscription);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder, decorators: [{\n            type: Injectable\n        }] });\nconst provideComponentInputBinding = () => {\n    return {\n        provide: INPUT_BINDER,\n        useFactory: componentInputBindingFactory,\n        deps: [Router],\n    };\n};\nfunction componentInputBindingFactory(router) {\n    /**\n     * We cast the router to any here, since the componentInputBindingEnabled\n     * property is not available until Angular v16.\n     */\n    if (router?.componentInputBindingEnabled) {\n        return new RoutedComponentInputBinder();\n    }\n    return null;\n}\n\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n    routerOutlet;\n    navCtrl;\n    config;\n    r;\n    z;\n    el;\n    constructor(routerOutlet, navCtrl, config, r, z, c) {\n        this.routerOutlet = routerOutlet;\n        this.navCtrl = navCtrl;\n        this.config = config;\n        this.r = r;\n        this.z = z;\n        c.detach();\n        this.el = this.r.nativeElement;\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n        if (this.routerOutlet?.canGoBack()) {\n            this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n            this.routerOutlet.pop();\n            ev.preventDefault();\n        }\n        else if (defaultHref != null) {\n            this.navCtrl.navigateBack(defaultHref, { animation: this.routerAnimation });\n            ev.preventDefault();\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonBackButton, deps: [{ token: IonRouterOutlet, optional: true }, { token: NavController }, { token: Config }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonBackButton, inputs: { color: \"color\", defaultHref: \"defaultHref\", disabled: \"disabled\", icon: \"icon\", mode: \"mode\", routerAnimation: \"routerAnimation\", text: \"text\", type: \"type\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, ngImport: i0 });\n};\nIonBackButton = __decorate([\n    ProxyCmp({\n        inputs: BACK_BUTTON_INPUTS,\n    })\n], IonBackButton);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonBackButton, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: BACK_BUTTON_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: IonRouterOutlet, decorators: [{\n                    type: Optional\n                }] }, { type: NavController }, { type: Config }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n    locationStrategy;\n    navCtrl;\n    elementRef;\n    router;\n    routerLink;\n    routerDirection = 'forward';\n    routerAnimation;\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n        this.updateTabindex();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    /**\n     * The `tabindex` is set to `0` by default on the host element when\n     * the `routerLink` directive is used. This causes issues with Ionic\n     * components that wrap an `a` or `button` element, such as `ion-item`.\n     * See issue https://github.com/angular/angular/issues/28345\n     *\n     * This method removes the `tabindex` attribute from the host element\n     * to allow the Ionic component to manage the focus state correctly.\n     */\n    updateTabindex() {\n        // Ionic components that render a native anchor or button element\n        const ionicComponents = [\n            'ION-BACK-BUTTON',\n            'ION-BREADCRUMB',\n            'ION-BUTTON',\n            'ION-CARD',\n            'ION-FAB-BUTTON',\n            'ION-ITEM',\n            'ION-ITEM-OPTION',\n            'ION-MENU-BUTTON',\n            'ION-SEGMENT-BUTTON',\n            'ION-TAB-BUTTON',\n        ];\n        const hostElement = this.elementRef.nativeElement;\n        if (ionicComponents.includes(hostElement.tagName)) {\n            if (hostElement.getAttribute('tabindex') === '0') {\n                hostElement.removeAttribute('tabindex');\n            }\n        }\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n        /**\n         * This prevents the browser from\n         * performing a page reload when pressing\n         * an Ionic component with routerLink.\n         * The page reload interferes with routing\n         * and causes ion-back-button to disappear\n         * since the local history is wiped on reload.\n         */\n        ev.preventDefault();\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: RouterLinkDelegateDirective, selector: \":not(a):not(area)[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: ':not(a):not(area)[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\nclass RouterLinkWithHrefDelegateDirective {\n    locationStrategy;\n    navCtrl;\n    elementRef;\n    router;\n    routerLink;\n    routerDirection = 'forward';\n    routerAnimation;\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick() {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: RouterLinkWithHrefDelegateDirective, selector: \"a[routerLink],area[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick()\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'a[routerLink],area[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = [\n    'push',\n    'insert',\n    'insertPages',\n    'pop',\n    'popTo',\n    'popToRoot',\n    'removeIndex',\n    'setRoot',\n    'setPages',\n    'getActive',\n    'getByIndex',\n    'canGoBack',\n    'getPrevious',\n];\nlet IonNav = class IonNav {\n    z;\n    el;\n    constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n        this.z = z;\n        c.detach();\n        this.el = ref.nativeElement;\n        ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n        proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonNav, deps: [{ token: i0.ElementRef }, { token: i0.EnvironmentInjector }, { token: i0.Injector }, { token: AngularDelegate }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonNav, inputs: { animated: \"animated\", animation: \"animation\", root: \"root\", rootParams: \"rootParams\", swipeGesture: \"swipeGesture\" }, ngImport: i0 });\n};\nIonNav = __decorate([\n    ProxyCmp({\n        inputs: NAV_INPUTS,\n        methods: NAV_METHODS,\n    })\n], IonNav);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonNav, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: NAV_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.EnvironmentInjector }, { type: i0.Injector }, { type: AngularDelegate }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; } });\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n    navCtrl;\n    tabsInner;\n    /**\n     * Emitted before the tab view is changed.\n     */\n    ionTabsWillChange = new EventEmitter();\n    /**\n     * Emitted after the tab view is changed.\n     */\n    ionTabsDidChange = new EventEmitter();\n    tabBarSlot = 'bottom';\n    hasTab = false;\n    selectedTab;\n    leavingTab;\n    constructor(navCtrl) {\n        this.navCtrl = navCtrl;\n    }\n    ngAfterViewInit() {\n        /**\n         * Developers must pass at least one ion-tab\n         * inside of ion-tabs if they want to use a\n         * basic tab-based navigation without the\n         * history stack or URL updates associated\n         * with the router.\n         */\n        const firstTab = this.tabs.length > 0 ? this.tabs.first : undefined;\n        if (firstTab) {\n            this.hasTab = true;\n            this.setActiveTab(firstTab.tab);\n            this.tabSwitch();\n        }\n    }\n    ngAfterContentInit() {\n        this.detectSlotChanges();\n    }\n    ngAfterContentChecked() {\n        this.detectSlotChanges();\n    }\n    /**\n     * @internal\n     */\n    onStackWillChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            this.ionTabsWillChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * @internal\n     */\n    onStackDidChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            if (this.tabBar) {\n                this.tabBar.selectedTab = stackId;\n            }\n            this.ionTabsDidChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * When a tab button is clicked, there are several scenarios:\n     * 1. If the selected tab is currently active (the tab button has been clicked\n     *    again), then it should go to the root view for that tab.\n     *\n     *   a. Get the saved root view from the router outlet. If the saved root view\n     *      matches the tabRootUrl, set the route view to this view including the\n     *      navigation extras.\n     *   b. If the saved root view from the router outlet does\n     *      not match, navigate to the tabRootUrl. No navigation extras are\n     *      included.\n     *\n     * 2. If the current tab tab is not currently selected, get the last route\n     *    view from the router outlet.\n     *\n     *   a. If the last route view exists, navigate to that view including any\n     *      navigation extras\n     *   b. If the last route view doesn't exist, then navigate\n     *      to the default tabRootUrl\n     */\n    select(tabOrEvent) {\n        const isTabString = typeof tabOrEvent === 'string';\n        const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n        /**\n         * If the tabs are not using the router, then\n         * the tab switch logic is handled by the tabs\n         * component itself.\n         */\n        if (this.hasTab) {\n            this.setActiveTab(tab);\n            this.tabSwitch();\n            return;\n        }\n        const alreadySelected = this.outlet.getActiveStackId() === tab;\n        const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n        /**\n         * If this is a nested tab, prevent the event\n         * from bubbling otherwise the outer tabs\n         * will respond to this event too, causing\n         * the app to get directed to the wrong place.\n         */\n        if (!isTabString) {\n            tabOrEvent.stopPropagation();\n        }\n        if (alreadySelected) {\n            const activeStackId = this.outlet.getActiveStackId();\n            const activeView = this.outlet.getLastRouteView(activeStackId);\n            // If on root tab, do not navigate to root tab again\n            if (activeView?.url === tabRootUrl) {\n                return;\n            }\n            const rootView = this.outlet.getRootView(tab);\n            const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n            return this.navCtrl.navigateRoot(tabRootUrl, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n        else {\n            const lastRoute = this.outlet.getLastRouteView(tab);\n            /**\n             * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n             * selected tab\n             */\n            const url = lastRoute?.url || tabRootUrl;\n            const navigationExtras = lastRoute?.savedExtras;\n            return this.navCtrl.navigateRoot(url, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n    }\n    setActiveTab(tab) {\n        const tabs = this.tabs;\n        const selectedTab = tabs.find((t) => t.tab === tab);\n        if (!selectedTab) {\n            console.error(`[Ionic Error]: Tab with id: \"${tab}\" does not exist`);\n            return;\n        }\n        this.leavingTab = this.selectedTab;\n        this.selectedTab = selectedTab;\n        this.ionTabsWillChange.emit({ tab });\n        selectedTab.el.active = true;\n    }\n    tabSwitch() {\n        const { selectedTab, leavingTab } = this;\n        if (this.tabBar && selectedTab) {\n            this.tabBar.selectedTab = selectedTab.tab;\n        }\n        if (leavingTab?.tab !== selectedTab?.tab) {\n            if (leavingTab?.el) {\n                leavingTab.el.active = false;\n            }\n        }\n        if (selectedTab) {\n            this.ionTabsDidChange.emit({ tab: selectedTab.tab });\n        }\n    }\n    getSelected() {\n        if (this.hasTab) {\n            return this.selectedTab?.tab;\n        }\n        return this.outlet.getActiveStackId();\n    }\n    /**\n     * Detects changes to the slot attribute of the tab bar.\n     *\n     * If the slot attribute has changed, then the tab bar\n     * should be relocated to the new slot position.\n     */\n    detectSlotChanges() {\n        this.tabBars.forEach((tabBar) => {\n            // el is a protected attribute from the generated component wrapper\n            const currentSlot = tabBar.el.getAttribute('slot');\n            if (currentSlot !== this.tabBarSlot) {\n                this.tabBarSlot = currentSlot;\n                this.relocateTabBar();\n            }\n        });\n    }\n    /**\n     * Relocates the tab bar to the new slot position.\n     */\n    relocateTabBar() {\n        /**\n         * `el` is a protected attribute from the generated component wrapper.\n         * To avoid having to manually create the wrapper for tab bar, we\n         * cast the tab bar to any and access the protected attribute.\n         */\n        const tabBar = this.tabBar.el;\n        if (this.tabBarSlot === 'top') {\n            /**\n             * A tab bar with a slot of \"top\" should be inserted\n             * at the top of the container.\n             */\n            this.tabsInner.nativeElement.before(tabBar);\n        }\n        else {\n            /**\n             * A tab bar with a slot of \"bottom\" or without a slot\n             * should be inserted at the end of the container.\n             */\n            this.tabsInner.nativeElement.after(tabBar);\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonTabs, deps: [{ token: NavController }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonTabs, selector: \"ion-tabs\", outputs: { ionTabsWillChange: \"ionTabsWillChange\", ionTabsDidChange: \"ionTabsDidChange\" }, host: { listeners: { \"ionTabButtonClick\": \"select($event)\" } }, viewQueries: [{ propertyName: \"tabsInner\", first: true, predicate: [\"tabsInner\"], descendants: true, read: ElementRef, static: true }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonTabs, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-tabs',\n                }]\n        }], ctorParameters: function () { return [{ type: NavController }]; }, propDecorators: { tabsInner: [{\n                type: ViewChild,\n                args: ['tabsInner', { read: ElementRef, static: true }]\n            }], ionTabsWillChange: [{\n                type: Output\n            }], ionTabsDidChange: [{\n                type: Output\n            }], select: [{\n                type: HostListener,\n                args: ['ionTabButtonClick', ['$event']]\n            }] } });\n\nconst raf = (h) => {\n    if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n        return __zone_symbol__requestAnimationFrame(h);\n    }\n    if (typeof requestAnimationFrame === 'function') {\n        return requestAnimationFrame(h);\n    }\n    return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n    injector;\n    elementRef;\n    onChange = () => {\n        /**/\n    };\n    onTouched = () => {\n        /**/\n    };\n    lastValue;\n    statusChanges;\n    constructor(injector, elementRef) {\n        this.injector = injector;\n        this.elementRef = elementRef;\n    }\n    writeValue(value) {\n        this.elementRef.nativeElement.value = this.lastValue = value;\n        setIonicClasses(this.elementRef);\n    }\n    /**\n     * Notifies the ControlValueAccessor of a change in the value of the control.\n     *\n     * This is called by each of the ValueAccessor directives when we want to update\n     * the status and validity of the form control. For example with text components this\n     * is called when the ionInput event is fired. For select components this is called\n     * when the ionChange event is fired.\n     *\n     * This also updates the Ionic form status classes on the element.\n     *\n     * @param el The component element.\n     * @param value The new value of the control.\n     */\n    handleValueChange(el, value) {\n        if (el === this.elementRef.nativeElement) {\n            if (value !== this.lastValue) {\n                this.lastValue = value;\n                this.onChange(value);\n            }\n            setIonicClasses(this.elementRef);\n        }\n    }\n    _handleBlurEvent(el) {\n        if (el === this.elementRef.nativeElement) {\n            this.onTouched();\n            setIonicClasses(this.elementRef);\n            // When ion-radio is blurred, el and this.elementRef.nativeElement are\n            // different so we need to check if the closest ion-radio-group is the same\n            // as this.elementRef.nativeElement and if so, we need to mark the radio group\n            // as touched\n        }\n        else if (el.closest('ion-radio-group') === this.elementRef.nativeElement) {\n            this.onTouched();\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.elementRef.nativeElement.disabled = isDisabled;\n    }\n    ngOnDestroy() {\n        if (this.statusChanges) {\n            this.statusChanges.unsubscribe();\n        }\n    }\n    ngAfterViewInit() {\n        let ngControl;\n        try {\n            ngControl = this.injector.get(NgControl);\n        }\n        catch {\n            /* No FormControl or ngModel binding */\n        }\n        if (!ngControl) {\n            return;\n        }\n        // Listen for changes in validity, disabled, or pending states\n        if (ngControl.statusChanges) {\n            this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n        }\n        /**\n         * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n         * whenever it is implemented.\n         */\n        const formControl = ngControl.control;\n        if (formControl) {\n            const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n            methodsToPatch.forEach((method) => {\n                if (typeof formControl[method] !== 'undefined') {\n                    const oldFn = formControl[method].bind(formControl);\n                    formControl[method] = (...params) => {\n                        oldFn(...params);\n                        setIonicClasses(this.elementRef);\n                    };\n                }\n            });\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ValueAccessor, deps: [{ token: i0.Injector }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: ValueAccessor, host: { listeners: { \"ionBlur\": \"_handleBlurEvent($event.target)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ValueAccessor, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: i0.ElementRef }]; }, propDecorators: { _handleBlurEvent: [{\n                type: HostListener,\n                args: ['ionBlur', ['$event.target']]\n            }] } });\nconst setIonicClasses = (element) => {\n    raf(() => {\n        const input = element.nativeElement;\n        const hasValue = input.value != null && input.value.toString().length > 0;\n        const classes = getClasses(input);\n        setClasses(input, classes);\n        const item = input.closest('ion-item');\n        if (item) {\n            if (hasValue) {\n                setClasses(item, [...classes, 'item-has-value']);\n            }\n            else {\n                setClasses(item, classes);\n            }\n        }\n    });\n};\nconst getClasses = (element) => {\n    const classList = element.classList;\n    const classes = [];\n    for (let i = 0; i < classList.length; i++) {\n        const item = classList.item(i);\n        if (item !== null && startsWith(item, 'ng-')) {\n            classes.push(`ion-${item.substring(3)}`);\n        }\n    }\n    return classes;\n};\nconst setClasses = (element, classes) => {\n    const classList = element.classList;\n    classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n    classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n    return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n    /**\n     * Whether the given route should detach for later reuse.\n     */\n    shouldDetach(_route) {\n        return false;\n    }\n    /**\n     * Returns `false`, meaning the route (and its subtree) is never reattached\n     */\n    shouldAttach(_route) {\n        return false;\n    }\n    /**\n     * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n     */\n    store(_route, _detachedTree) {\n        return;\n    }\n    /**\n     * Returns `null` because this strategy does not store routes for later re-use.\n     */\n    retrieve(_route) {\n        return null;\n    }\n    /**\n     * Determines if a route should be reused.\n     * This strategy returns `true` when the future route config and\n     * current route config are identical and all route parameters are identical.\n     */\n    shouldReuseRoute(future, curr) {\n        if (future.routeConfig !== curr.routeConfig) {\n            return false;\n        }\n        // checking router params\n        const futureParams = future.params;\n        const currentParams = curr.params;\n        const keysA = Object.keys(futureParams);\n        const keysB = Object.keys(currentParams);\n        if (keysA.length !== keysB.length) {\n            return false;\n        }\n        // Test for A's keys different from B.\n        for (const key of keysA) {\n            if (currentParams[key] !== futureParams[key]) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n    ctrl;\n    constructor(ctrl) {\n        this.ctrl = ctrl;\n    }\n    /**\n     * Creates a new overlay\n     */\n    create(opts) {\n        return this.ctrl.create((opts || {}));\n    }\n    /**\n     * When `id` is not provided, it dismisses the top overlay.\n     */\n    dismiss(data, role, id) {\n        return this.ctrl.dismiss(data, role, id);\n    }\n    /**\n     * Returns the top overlay.\n     */\n    getTop() {\n        return this.ctrl.getTop();\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjU,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,eAAe,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACjH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAChM,SAASC,OAAO,EAAEC,SAAS,EAAEC,eAAe,EAAEC,aAAa,EAAEC,EAAE,QAAQ,MAAM;AAC7E,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAE3C,MAAMC,cAAc,CAAC;EACjBC,cAAc;EACdC,WAAWA,CAACD,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIE,IAAIA,CAACC,MAAM,EAAE;IACT,OAAO,IAAI,CAACH,cAAc,CAACE,IAAI,CAACC,MAAM,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACD,MAAM,EAAE;IACV,OAAO,IAAI,CAACH,cAAc,CAACI,KAAK,CAACD,MAAM,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACF,MAAM,EAAE;IACX,OAAO,IAAI,CAACH,cAAc,CAACK,MAAM,CAACF,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAACC,YAAY,EAAEJ,MAAM,EAAE;IACzB,OAAO,IAAI,CAACH,cAAc,CAACM,MAAM,CAACC,YAAY,EAAEJ,MAAM,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAACD,YAAY,EAAEJ,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACH,cAAc,CAACQ,YAAY,CAACD,YAAY,EAAEJ,MAAM,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIM,MAAMA,CAACN,MAAM,EAAE;IACX,OAAO,IAAI,CAACH,cAAc,CAACS,MAAM,CAACN,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIO,SAASA,CAACP,MAAM,EAAE;IACd,OAAO,IAAI,CAACH,cAAc,CAACU,SAAS,CAACP,MAAM,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACR,MAAM,EAAE;IACR,OAAO,IAAI,CAACH,cAAc,CAACW,GAAG,CAACR,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;EACIS,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACZ,cAAc,CAACY,OAAO,CAAC,CAAC;EACxC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,cAAc,CAACa,QAAQ,CAAC,CAAC;EACzC;EACAC,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/B,OAAO,IAAI,CAAChB,cAAc,CAACc,iBAAiB,CAACC,IAAI,EAAEC,SAAS,CAAC;EACjE;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjB,cAAc,CAACiB,WAAW,CAAC,CAAC;EAC5C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClB,cAAc,CAACkB,YAAY,CAAC,CAAC;EAC7C;EACAC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC5B,OAAO,IAAI,CAACrB,cAAc,CAACmB,gBAAgB,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC9D;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAO,IAAI,CAACvB,cAAc,CAACsB,SAAS,CAACC,IAAI,CAAC;EAC9C;EACAC,WAAWA,CAACD,IAAI,EAAE;IACd,OAAO,IAAI,CAACvB,cAAc,CAACwB,WAAW,CAACD,IAAI,CAAC;EAChD;EACAE,QAAQA,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC3B,cAAc,CAACyB,QAAQ,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,CAAC;EACnE;AACJ;AAEA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACIC,IAAIA,CAACC,EAAE,EAAE;IACLC,QAAQ,CAAC,CAAC,CAACF,IAAI,CAACC,EAAE,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIE,KAAKA,CAACF,EAAE,EAAE;IACNC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACF,EAAE,CAAC;EACxB;EACA;EAAmB,OAAOG,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFP,aAAa;EAAA;EACpI;EAAmB,OAAOQ,KAAK,kBAD8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EACYV,aAAa;IAAAW,OAAA,EAAbX,aAAa,CAAAK,IAAA;IAAAO,UAAA,EAAc;EAAM;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHiH7F,EAAE,CAAA8F,iBAAA,CAGvBd,aAAa,EAAc,CAAC;IAC5GR,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMT,QAAQ,GAAGA,CAAA,KAAM;EACnB,MAAMa,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;EACzD,IAAID,GAAG,IAAI,IAAI,EAAE;IACb,MAAME,KAAK,GAAGF,GAAG,CAACE,KAAK;IACvB,IAAIA,KAAK,EAAEC,KAAK,EAAE;MACd,OAAOD,KAAK,CAACC,KAAK;IACtB;IACA,OAAO;MACHlB,IAAI,EAAGC,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE,CAAC;MAC3CE,KAAK,EAAGF,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE;IAC/C,CAAC;EACL;EACA,OAAO;IACHD,IAAI,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;IAClBE,KAAK,EAAGF,EAAE,IAAKA,EAAE,CAAC;EACtB,CAAC;AACL,CAAC;AAED,MAAMmB,QAAQ,CAAC;EACXC,GAAG;EACHC,aAAa;EACbP,GAAG;EACH;AACJ;AACA;EACIQ,UAAU,GAAG,IAAIhE,OAAO,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;EACIiE,eAAe,GAAG,IAAIjE,OAAO,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;EACIkE,eAAe,GAAG,IAAIlE,OAAO,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;AACA;EACImE,KAAK,GAAG,IAAInE,OAAO,CAAC,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACIoE,MAAM,GAAG,IAAIpE,OAAO,CAAC,CAAC;EACtB;AACJ;AACA;AACA;AACA;EACIqE,MAAM,GAAG,IAAIrE,OAAO,CAAC,CAAC;EACtBa,WAAWA,CAACiD,GAAG,EAAEQ,IAAI,EAAE;IACnB,IAAI,CAACR,GAAG,GAAGA,GAAG;IACdQ,IAAI,CAACC,GAAG,CAAC,MAAM;MACX,IAAI,CAACf,GAAG,GAAGM,GAAG,CAACU,WAAW;MAC1B,IAAI,CAACR,UAAU,CAACS,qBAAqB,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;QAClE,OAAO,IAAI,CAACC,SAAS,CAAEC,EAAE,IAAK;UAC1B,OAAOA,EAAE,CAACC,QAAQ,CAACJ,QAAQ,EAAGK,kBAAkB,IAAKT,IAAI,CAACC,GAAG,CAAC,MAAMI,QAAQ,CAACI,kBAAkB,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,CAAC,IAAI,CAACb,KAAK,EAAEL,GAAG,EAAE,OAAO,EAAEQ,IAAI,CAAC;MAC1CU,UAAU,CAAC,IAAI,CAACZ,MAAM,EAAEN,GAAG,EAAE,QAAQ,EAAEQ,IAAI,CAAC;MAC5CU,UAAU,CAAC,IAAI,CAAChB,UAAU,EAAEF,GAAG,EAAE,eAAe,EAAEQ,IAAI,CAAC;MACvDU,UAAU,CAAC,IAAI,CAACX,MAAM,EAAE,IAAI,CAACb,GAAG,EAAE,QAAQ,EAAEc,IAAI,CAAC;MACjDU,UAAU,CAAC,IAAI,CAACf,eAAe,EAAE,IAAI,CAACT,GAAG,EAAE,oBAAoB,EAAEc,IAAI,CAAC;MACtEU,UAAU,CAAC,IAAI,CAACd,eAAe,EAAE,IAAI,CAACV,GAAG,EAAE,oBAAoB,EAAEc,IAAI,CAAC;MACtE,IAAIW,YAAY;MAChB,IAAI,CAAClB,aAAa,GAAG,IAAImB,OAAO,CAAEC,GAAG,IAAK;QACtCF,YAAY,GAAGE,GAAG;MACtB,CAAC,CAAC;MACF,IAAI,IAAI,CAAC3B,GAAG,GAAG,SAAS,CAAC,EAAE;QACvBM,GAAG,CAACsB,gBAAgB,CAAC,aAAa,EAAE,MAAM;UACtCH,YAAY,CAAC,SAAS,CAAC;QAC3B,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,MACI;QACD;QACAJ,YAAY,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,EAAEA,CAACC,YAAY,EAAE;IACb,OAAO/F,UAAU,CAAC,IAAI,CAACgE,GAAG,EAAE+B,YAAY,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,OAAO/F,YAAY,CAAC,IAAI,CAAC+D,GAAG,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAAC1B,aAAa;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI2B,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC5B,GAAG,CAAC6B,GAAG,KAAK,KAAK;EACjC;EACA;AACJ;AACA;EACIC,aAAaA,CAACC,GAAG,EAAE;IACf,OAAOC,cAAc,CAAC,IAAI,CAACtC,GAAG,CAACuC,QAAQ,CAACC,IAAI,EAAEH,GAAG,CAAC;EACtD;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIA,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1C,GAAG,CAAC2C,UAAU,GAAG,yBAAyB,CAAC,CAACC,OAAO;EACnE;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,MAAMC,GAAG,GAAG,IAAI,CAAC/C,GAAG,CAACgD,SAAS;IAC9B,OAAO,CAAC,EAAED,GAAG,EAAEE,SAAS,IAAIF,GAAG,CAACE,SAAS,CAACC,OAAO,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;EACvE;EACA;AACJ;AACA;EACIK,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACnD,GAAG,CAACuC,QAAQ,CAACC,IAAI;EACjC;EACA;AACJ;AACA;EACIY,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACpD,GAAG,CAACqD,UAAU;EAC9B;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtD,GAAG,CAACuD,WAAW;EAC/B;EACA;EAAmB,OAAOlE,IAAI,YAAAmE,iBAAAjE,iBAAA;IAAA,YAAAA,iBAAA,IAAyFc,QAAQ,EAnPlBrG,EAAE,CAAAyJ,QAAA,CAmPkC1H,QAAQ,GAnP5C/B,EAAE,CAAAyJ,QAAA,CAmPuDzJ,EAAE,CAACM,MAAM;EAAA;EAC/K;EAAmB,OAAOkF,KAAK,kBApP8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EAoPYW,QAAQ;IAAAV,OAAA,EAARU,QAAQ,CAAAhB,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtPiH7F,EAAE,CAAA8F,iBAAA,CAsPvBO,QAAQ,EAAc,CAAC;IACvG7B,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DnF,IAAI,EAAEtE,MAAM;QACZ6F,IAAI,EAAE,CAAChE,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEyC,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAMgI,cAAc,GAAGA,CAACa,GAAG,EAAEd,GAAG,KAAK;EACjCA,GAAG,GAAGA,GAAG,CAACuB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EACrC,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,QAAQ,GAAGzB,GAAG,GAAG,WAAW,CAAC;EACtD,MAAM0B,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACb,GAAG,CAAC;EAC/B,OAAOY,OAAO,GAAGE,kBAAkB,CAACF,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI;AAC9E,CAAC;AACD,MAAMpC,UAAU,GAAGA,CAAC0C,OAAO,EAAEC,EAAE,EAAEC,SAAS,EAAEtD,IAAI,KAAK;EACjD,IAAIqD,EAAE,EAAE;IACJA,EAAE,CAACvC,gBAAgB,CAACwC,SAAS,EAAG/C,EAAE,IAAK;MACnC;AACZ;AACA;AACA;AACA;AACA;MACYP,IAAI,CAACC,GAAG,CAAC,MAAM;QACX;QACA,MAAMsD,KAAK,GAAGhD,EAAE,IAAI,IAAI,GAAGA,EAAE,CAACiD,MAAM,GAAGZ,SAAS;QAChDQ,OAAO,CAACK,IAAI,CAACF,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ,CAAC;AAED,MAAMG,aAAa,CAAC;EAChBjC,QAAQ;EACRkC,UAAU;EACVC,MAAM;EACNC,SAAS;EACTC,SAAS,GAAGC,iBAAiB;EAC7B9F,QAAQ,GAAG+F,gBAAgB;EAC3BC,gBAAgB;EAChBC,cAAc,GAAG,SAAS;EAC1BC,cAAc;EACdC,SAAS,GAAG,CAAC,CAAC;EACd7H,WAAWA,CAAC8H,QAAQ,EAAE5C,QAAQ,EAAEkC,UAAU,EAAEC,MAAM,EAAE;IAChD,IAAI,CAACnC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACkC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACU,MAAM,CAAChE,SAAS,CAAEC,EAAE,IAAK;QAC5B,IAAIA,EAAE,YAAY5F,eAAe,EAAE;UAC/B;UACA,MAAM4J,EAAE,GAAGhE,EAAE,CAACiE,aAAa,GAAGjE,EAAE,CAACiE,aAAa,CAACC,YAAY,GAAGlE,EAAE,CAACgE,EAAE;UACnE,IAAI,CAACL,cAAc,GAAG,IAAI,CAACC,cAAc,GAAGI,EAAE,GAAG,IAAI,CAACH,SAAS,GAAG,MAAM,GAAG,SAAS;UACpF,IAAI,CAACA,SAAS,GAAG,IAAI,CAACF,cAAc,KAAK,SAAS,GAAG3D,EAAE,CAACgE,EAAE,GAAGA,EAAE;QACnE;MACJ,CAAC,CAAC;IACN;IACA;IACAF,QAAQ,CAAC3E,UAAU,CAACS,qBAAqB,CAAC,CAAC,EAAGM,kBAAkB,IAAK;MACjE,IAAI,CAACiE,GAAG,CAAC,CAAC;MACVjE,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkE,eAAeA,CAACtC,GAAG,EAAEuC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAACC,YAAY,CAAC,SAAS,EAAED,OAAO,CAAC3G,QAAQ,EAAE2G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACtH,SAAS,CAAC;IAC7F,OAAO,IAAI,CAACyH,QAAQ,CAAC1C,GAAG,EAAEuC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,YAAYA,CAAC3C,GAAG,EAAEuC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC3G,QAAQ,EAAE2G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACtH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACyH,QAAQ,CAAC1C,GAAG,EAAEuC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAAC5C,GAAG,EAAEuC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC3G,QAAQ,EAAE2G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACtH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACyH,QAAQ,CAAC1C,GAAG,EAAEuC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIM,IAAIA,CAACN,OAAO,GAAG;IAAE3G,QAAQ,EAAE,IAAI;IAAE6G,kBAAkB,EAAE;EAAO,CAAC,EAAE;IAC3D,IAAI,CAACD,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC3G,QAAQ,EAAE2G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACtH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACmE,QAAQ,CAACyD,IAAI,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUR,GAAGA,CAAA,EAAG;IAAA,IAAAS,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAIC,MAAM,GAAGF,KAAI,CAACtB,SAAS;MAC3B,OAAOwB,MAAM,EAAE;QACX,UAAUA,MAAM,CAACX,GAAG,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI;QACf,CAAC,MACI;UACDW,MAAM,GAAGA,MAAM,CAACC,YAAY;QAChC;MACJ;MACA,OAAO,KAAK;IAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIT,YAAYA,CAACf,SAAS,EAAE7F,QAAQ,EAAE6G,kBAAkB,EAAEb,gBAAgB,EAAE;IACpE,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC7F,QAAQ,GAAGsH,YAAY,CAACzB,SAAS,EAAE7F,QAAQ,EAAE6G,kBAAkB,CAAC;IACrE,IAAI,CAACb,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;EACIuB,YAAYA,CAACH,MAAM,EAAE;IACjB,IAAI,CAACxB,SAAS,GAAGwB,MAAM;EAC3B;EACA;AACJ;AACA;EACII,iBAAiBA,CAAA,EAAG;IAChB,IAAI3B,SAAS,GAAG,MAAM;IACtB,IAAIxG,SAAS;IACb,MAAM2G,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAI,IAAI,CAACH,SAAS,KAAK,MAAM,EAAE;MAC3BA,SAAS,GAAG,IAAI,CAACI,cAAc;MAC/B5G,SAAS,GAAG,IAAI,CAAC6G,cAAc;IACnC,CAAC,MACI;MACD7G,SAAS,GAAG,IAAI,CAACW,QAAQ;MACzB6F,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGC,iBAAiB;IAClC,IAAI,CAAC9F,QAAQ,GAAG+F,gBAAgB;IAChC,IAAI,CAACC,gBAAgB,GAAGrB,SAAS;IACjC,OAAO;MACHkB,SAAS;MACTxG,SAAS;MACT2G;IACJ,CAAC;EACL;EACAc,QAAQA,CAAC1C,GAAG,EAAEuC,OAAO,EAAE;IACnB,IAAIc,KAAK,CAACC,OAAO,CAACtD,GAAG,CAAC,EAAE;MACpB;MACA,OAAO,IAAI,CAACuB,MAAM,CAACmB,QAAQ,CAAC1C,GAAG,EAAEuC,OAAO,CAAC;IAC7C,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMgB,OAAO,GAAG,IAAI,CAACjC,UAAU,CAACkC,KAAK,CAACxD,GAAG,CAACyD,QAAQ,CAAC,CAAC,CAAC;MACrD,IAAIlB,OAAO,CAACmB,WAAW,KAAKnD,SAAS,EAAE;QACnCgD,OAAO,CAACG,WAAW,GAAG;UAAE,GAAGnB,OAAO,CAACmB;QAAY,CAAC;MACpD;MACA,IAAInB,OAAO,CAACoB,QAAQ,KAAKpD,SAAS,EAAE;QAChCgD,OAAO,CAACI,QAAQ,GAAGpB,OAAO,CAACoB,QAAQ;MACvC;MACA;AACZ;AACA;AACA;AACA;MACY;MACA,OAAO,IAAI,CAACpC,MAAM,CAACqC,aAAa,CAACL,OAAO,EAAEhB,OAAO,CAAC;IACtD;EACJ;EACA;EAAmB,OAAOrG,IAAI,YAAA2H,sBAAAzH,iBAAA;IAAA,YAAAA,iBAAA,IAAyFiF,aAAa,EAvdvBxK,EAAE,CAAAyJ,QAAA,CAuduCpD,QAAQ,GAvdjDrG,EAAE,CAAAyJ,QAAA,CAud4D3H,EAAE,CAACmL,QAAQ,GAvdzEjN,EAAE,CAAAyJ,QAAA,CAudoFjI,EAAE,CAAC0L,aAAa,GAvdtGlN,EAAE,CAAAyJ,QAAA,CAudiHjI,EAAE,CAACK,MAAM;EAAA;EACzO;EAAmB,OAAO2D,KAAK,kBAxd8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EAwdY8E,aAAa;IAAA7E,OAAA,EAAb6E,aAAa,CAAAnF,IAAA;IAAAO,UAAA,EAAc;EAAM;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1diH7F,EAAE,CAAA8F,iBAAA,CA0dvB0E,aAAa,EAAc,CAAC;IAC5GhG,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAE6B;IAAS,CAAC,EAAE;MAAE7B,IAAI,EAAE1C,EAAE,CAACmL;IAAS,CAAC,EAAE;MAAEzI,IAAI,EAAEhD,EAAE,CAAC0L;IAAc,CAAC,EAAE;MAAE1I,IAAI,EAAEhD,EAAE,CAACK,MAAM;MAAE8H,UAAU,EAAE,CAAC;QACrInF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMkM,YAAY,GAAGA,CAACzB,SAAS,EAAE7F,QAAQ,EAAE6G,kBAAkB,KAAK;EAC9D,IAAI7G,QAAQ,KAAK,KAAK,EAAE;IACpB,OAAO2E,SAAS;EACpB;EACA,IAAIkC,kBAAkB,KAAKlC,SAAS,EAAE;IAClC,OAAOkC,kBAAkB;EAC7B;EACA,IAAIhB,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACjD,OAAOA,SAAS;EACpB,CAAC,MACI,IAAIA,SAAS,KAAK,MAAM,IAAI7F,QAAQ,KAAK,IAAI,EAAE;IAChD,OAAO,SAAS;EACpB;EACA,OAAO2E,SAAS;AACpB,CAAC;AACD,MAAMmB,iBAAiB,GAAG,MAAM;AAChC,MAAMC,gBAAgB,GAAGpB,SAAS;AAElC,MAAMyD,MAAM,CAAC;EACTpJ,GAAGA,CAACsE,GAAG,EAAE+E,QAAQ,EAAE;IACf,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACtJ,GAAG,CAACsE,GAAG,EAAE+E,QAAQ,CAAC;IAC/B;IACA,OAAO,IAAI;EACf;EACAG,UAAUA,CAAClF,GAAG,EAAE+E,QAAQ,EAAE;IACtB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACE,UAAU,CAAClF,GAAG,EAAE+E,QAAQ,CAAC;IACtC;IACA,OAAO,KAAK;EAChB;EACAI,SAASA,CAACnF,GAAG,EAAE+E,QAAQ,EAAE;IACrB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACG,SAAS,CAACnF,GAAG,EAAE+E,QAAQ,CAAC;IACrC;IACA,OAAO,CAAC;EACZ;EACA;EAAmB,OAAO/H,IAAI,YAAAoI,eAAAlI,iBAAA;IAAA,YAAAA,iBAAA,IAAyF4H,MAAM;EAAA;EAC7H;EAAmB,OAAO3H,KAAK,kBA3gB8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EA2gBYyH,MAAM;IAAAxH,OAAA,EAANwH,MAAM,CAAA9H,IAAA;IAAAO,UAAA,EAAc;EAAM;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7gBiH7F,EAAE,CAAA8F,iBAAA,CA6gBvBqH,MAAM,EAAc,CAAC;IACrG3I,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM8H,WAAW,GAAG,IAAItN,cAAc,CAAC,YAAY,CAAC;AACpD,MAAMkN,SAAS,GAAGA,CAAA,KAAM;EACpB,IAAI,OAAOrH,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK;IAC1B,IAAIA,KAAK,EAAEyH,MAAM,EAAE;MACf,OAAOzH,KAAK,CAACyH,MAAM;IACvB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,IAAI;EACJxK,WAAWA,CAACwK,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChBC,OAAO,CAACC,IAAI,CAAC,gLAAgL,CAAC;EAClM;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhK,GAAGA,CAACiK,KAAK,EAAE;IACP,OAAO,IAAI,CAACH,IAAI,CAACG,KAAK,CAAC;EAC3B;AACJ;;AAEA;AACA,MAAMC,eAAe,CAAC;EAClBnH,IAAI,GAAGzG,MAAM,CAACC,MAAM,CAAC;EACrB4N,cAAc,GAAG7N,MAAM,CAACE,cAAc,CAAC;EACvCoN,MAAM,GAAGtN,MAAM,CAACqN,WAAW,CAAC;EAC5BS,MAAMA,CAACC,mBAAmB,EAAEC,QAAQ,EAAEC,mBAAmB,EAAE;IACvD,OAAO,IAAIC,wBAAwB,CAACH,mBAAmB,EAAEC,QAAQ,EAAE,IAAI,CAACH,cAAc,EAAE,IAAI,CAACpH,IAAI,EAAEwH,mBAAmB,EAAE,IAAI,CAACX,MAAM,CAACa,cAAc,IAAI,KAAK,CAAC;EAChK;EACA;EAAmB,OAAOnJ,IAAI,YAAAoJ,wBAAAlJ,iBAAA;IAAA,YAAAA,iBAAA,IAAyF0I,eAAe;EAAA;EACtI;EAAmB,OAAOzI,KAAK,kBAtlB8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EAslBYuI,eAAe;IAAAtI,OAAA,EAAfsI,eAAe,CAAA5I;EAAA;AAC9I;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAxlBiH7F,EAAE,CAAA8F,iBAAA,CAwlBvBmI,eAAe,EAAc,CAAC;IAC9GzJ,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAMsO,wBAAwB,CAAC;EAC3BH,mBAAmB;EACnBC,QAAQ;EACRH,cAAc;EACdpH,IAAI;EACJwH,mBAAmB;EACnBI,oBAAoB;EACpBC,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;EACxBC,WAAW,GAAG,IAAID,OAAO,CAAC,CAAC;EAC3BvL,WAAWA,CAAC+K,mBAAmB,EAAEC,QAAQ,EAAEH,cAAc,EAAEpH,IAAI,EAAEwH,mBAAmB,EAAEI,oBAAoB,EAAE;IACxG,IAAI,CAACN,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACpH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACwH,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACI,oBAAoB,GAAGA,oBAAoB;EACpD;EACAI,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACtD,OAAO,IAAI,CAACpI,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,OAAO,IAAIW,OAAO,CAAEyH,OAAO,IAAK;QAC5B,MAAMC,cAAc,GAAG;UACnB,GAAGH;QACP,CAAC;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACX,mBAAmB,KAAK5E,SAAS,EAAE;UACxC0F,cAAc,CAAC,IAAI,CAACd,mBAAmB,CAAC,GAAGS,SAAS;QACxD;QACA,MAAM5E,EAAE,GAAGkF,UAAU,CAAC,IAAI,CAACvI,IAAI,EAAE,IAAI,CAACsH,mBAAmB,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,cAAc,EAAE,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACE,WAAW,EAAEE,SAAS,EAAEC,SAAS,EAAEI,cAAc,EAAEF,UAAU,EAAE,IAAI,CAACZ,mBAAmB,EAAE,IAAI,CAACI,oBAAoB,CAAC;QACtOS,OAAO,CAAChF,EAAE,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAmF,iBAAiBA,CAACC,UAAU,EAAEP,SAAS,EAAE;IACrC,OAAO,IAAI,CAAClI,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,OAAO,IAAIW,OAAO,CAAEyH,OAAO,IAAK;QAC5B,MAAMK,YAAY,GAAG,IAAI,CAACb,QAAQ,CAAC5K,GAAG,CAACiL,SAAS,CAAC;QACjD,IAAIQ,YAAY,EAAE;UACdA,YAAY,CAACC,OAAO,CAAC,CAAC;UACtB,IAAI,CAACd,QAAQ,CAACe,MAAM,CAACV,SAAS,CAAC;UAC/B,MAAMW,YAAY,GAAG,IAAI,CAACd,WAAW,CAAC9K,GAAG,CAACiL,SAAS,CAAC;UACpD,IAAIW,YAAY,EAAE;YACdA,YAAY,CAAC,CAAC;YACd,IAAI,CAACd,WAAW,CAACa,MAAM,CAACV,SAAS,CAAC;UACtC;QACJ;QACAG,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACA,MAAME,UAAU,GAAGA,CAACvI,IAAI,EAAEsH,mBAAmB,EAAEC,QAAQ,EAAEH,cAAc,EAAES,QAAQ,EAAEE,WAAW,EAAEE,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEZ,mBAAmB,EAAEI,oBAAoB,KAAK;EACpL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMkB,aAAa,GAAGpP,QAAQ,CAAC2N,MAAM,CAAC;IAClC0B,SAAS,EAAEC,YAAY,CAACb,MAAM,CAAC;IAC/Bc,MAAM,EAAE1B;EACZ,CAAC,CAAC;EACF,MAAMmB,YAAY,GAAG/O,eAAe,CAACuO,SAAS,EAAE;IAC5CZ,mBAAmB;IACnB4B,eAAe,EAAEJ;EACrB,CAAC,CAAC;EACF,MAAMK,QAAQ,GAAGT,YAAY,CAACS,QAAQ;EACtC,MAAMC,WAAW,GAAGV,YAAY,CAACjH,QAAQ,CAAC4H,aAAa;EACvD,IAAIlB,MAAM,EAAE;IACR;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIX,mBAAmB,IAAI2B,QAAQ,CAAC3B,mBAAmB,CAAC,KAAK5E,SAAS,EAAE;MACpEoE,OAAO,CAACsC,KAAK,CAAC,kBAAkB9B,mBAAmB,sCAAsCS,SAAS,CAACsB,OAAO,CAACC,WAAW,CAAC,CAAC,2BAA2BhC,mBAAmB,mBAAmBU,SAAS,CAAC7K,IAAI,GAAG,CAAC;IAC/M;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIuK,oBAAoB,KAAK,IAAI,IAAIc,YAAY,CAACe,QAAQ,KAAK7G,SAAS,EAAE;MACtE,MAAM;QAAE8G,KAAK;QAAEC,OAAO;QAAE,GAAGC;MAAY,CAAC,GAAGzB,MAAM;MACjD;AACZ;AACA;AACA;MACY,KAAK,MAAM5G,GAAG,IAAIqI,WAAW,EAAE;QAC3BlB,YAAY,CAACe,QAAQ,CAAClI,GAAG,EAAEqI,WAAW,CAACrI,GAAG,CAAC,CAAC;MAChD;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAImI,KAAK,KAAK9G,SAAS,EAAE;QACrBiH,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAE;UAAEO;QAAM,CAAC,CAAC;MACtC;MACA,IAAIC,OAAO,KAAK/G,SAAS,EAAE;QACvBiH,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAE;UAAEQ;QAAQ,CAAC,CAAC;MACxC;IACJ,CAAC,MACI;MACDE,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAEhB,MAAM,CAAC;IACnC;EACJ;EACA,IAAIC,UAAU,EAAE;IACZ,KAAK,MAAM2B,QAAQ,IAAI3B,UAAU,EAAE;MAC/BgB,WAAW,CAACY,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC;IACvC;EACJ;EACA,MAAMlB,YAAY,GAAGqB,mBAAmB,CAAClK,IAAI,EAAEmJ,QAAQ,EAAEC,WAAW,CAAC;EACrEnB,SAAS,CAACkC,WAAW,CAACf,WAAW,CAAC;EAClChC,cAAc,CAACmB,UAAU,CAACG,YAAY,CAAC0B,QAAQ,CAAC;EAChDvC,QAAQ,CAACwC,GAAG,CAACjB,WAAW,EAAEV,YAAY,CAAC;EACvCX,WAAW,CAACsC,GAAG,CAACjB,WAAW,EAAEP,YAAY,CAAC;EAC1C,OAAOO,WAAW;AACtB,CAAC;AACD,MAAMkB,UAAU,GAAG,CACflP,oBAAoB,EACpBC,mBAAmB,EACnBC,oBAAoB,EACpBC,mBAAmB,EACnBC,qBAAqB,CACxB;AACD,MAAM0O,mBAAmB,GAAGA,CAAClK,IAAI,EAAEmJ,QAAQ,EAAEoB,OAAO,KAAK;EACrD,OAAOvK,IAAI,CAACC,GAAG,CAAC,MAAM;IAClB,MAAMuK,WAAW,GAAGF,UAAU,CAACtO,MAAM,CAAEsH,SAAS,IAAK,OAAO6F,QAAQ,CAAC7F,SAAS,CAAC,KAAK,UAAU,CAAC,CAACmH,GAAG,CAAEnH,SAAS,IAAK;MAC/G,MAAMoH,OAAO,GAAInK,EAAE,IAAK4I,QAAQ,CAAC7F,SAAS,CAAC,CAAC/C,EAAE,CAACiD,MAAM,CAAC;MACtD+G,OAAO,CAACzJ,gBAAgB,CAACwC,SAAS,EAAEoH,OAAO,CAAC;MAC5C,OAAO,MAAMH,OAAO,CAACI,mBAAmB,CAACrH,SAAS,EAAEoH,OAAO,CAAC;IAChE,CAAC,CAAC;IACF,OAAO,MAAMF,WAAW,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,cAAc,GAAG,IAAIxR,cAAc,CAAC,gBAAgB,CAAC;AAC3D,MAAM0P,YAAY,GAAIb,MAAM,IAAK;EAC7B,OAAO,CACH;IACI4C,OAAO,EAAED,cAAc;IACvBE,QAAQ,EAAE7C;EACd,CAAC,EACD;IACI4C,OAAO,EAAEjE,SAAS;IAClBmE,UAAU,EAAEC,0BAA0B;IACtCC,IAAI,EAAE,CAACL,cAAc;EACzB,CAAC,CACJ;AACL,CAAC;AACD,MAAMI,0BAA0B,GAAI/C,MAAM,IAAK;EAC3C,OAAO,IAAIrB,SAAS,CAACqB,MAAM,CAAC;AAChC,CAAC;;AAED;AACA;AACA;AACA,MAAMiD,WAAW,GAAGA,CAACC,GAAG,EAAEC,MAAM,KAAK;EACjC,MAAMC,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BF,MAAM,CAACV,OAAO,CAAEa,IAAI,IAAK;IACrB5B,MAAM,CAAC6B,cAAc,CAACH,SAAS,EAAEE,IAAI,EAAE;MACnCxO,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACoG,EAAE,CAACoI,IAAI,CAAC;MACxB,CAAC;MACDpB,GAAGA,CAACsB,GAAG,EAAE;QACL,IAAI,CAACC,CAAC,CAACC,iBAAiB,CAAC,MAAO,IAAI,CAACxI,EAAE,CAACoI,IAAI,CAAC,GAAGE,GAAI,CAAC;MACzD;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,MAAMG,YAAY,GAAGA,CAACT,GAAG,EAAEU,OAAO,KAAK;EACnC,MAAMR,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BO,OAAO,CAACnB,OAAO,CAAEoB,UAAU,IAAK;IAC5BT,SAAS,CAACS,UAAU,CAAC,GAAG,YAAY;MAChC,MAAM/M,IAAI,GAAGgN,SAAS;MACtB,OAAO,IAAI,CAACL,CAAC,CAACC,iBAAiB,CAAC,MAAM,IAAI,CAACxI,EAAE,CAAC2I,UAAU,CAAC,CAACE,KAAK,CAAC,IAAI,CAAC7I,EAAE,EAAEpE,IAAI,CAAC,CAAC;IACnF,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD,MAAMkN,YAAY,GAAGA,CAAChD,QAAQ,EAAE9F,EAAE,EAAEiB,MAAM,KAAK;EAC3CA,MAAM,CAACsG,OAAO,CAAEtH,SAAS,IAAM6F,QAAQ,CAAC7F,SAAS,CAAC,GAAG3H,SAAS,CAAC0H,EAAE,EAAEC,SAAS,CAAE,CAAC;AACnF,CAAC;AACD;AACA,SAAS8I,QAAQA,CAACC,IAAI,EAAE;EACpB,MAAMC,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC7B,MAAM;MAAEC,qBAAqB;MAAElB,MAAM;MAAES;IAAQ,CAAC,GAAGM,IAAI;IACvD,IAAIG,qBAAqB,KAAK5J,SAAS,EAAE;MACrC4J,qBAAqB,CAAC,CAAC;IAC3B;IACA,IAAIlB,MAAM,EAAE;MACRF,WAAW,CAACmB,GAAG,EAAEjB,MAAM,CAAC;IAC5B;IACA,IAAIS,OAAO,EAAE;MACTD,YAAY,CAACS,GAAG,EAAER,OAAO,CAAC;IAC9B;IACA,OAAOQ,GAAG;EACd,CAAC;EACD,OAAOD,SAAS;AACpB;AAEA,MAAMG,cAAc,GAAG,CACnB,WAAW,EACX,UAAU,EACV,OAAO,EACP,qBAAqB,EACrB,iBAAiB,EACjB,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,OAAO,EACP,WAAW,EACX,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,cAAc,EACd,aAAa,EACb,SAAS,EACT,eAAe,EACf,WAAW,EACX,MAAM,EACN,MAAM,CACT;AACD,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC;AAC/E,IAAIC,UAAU,GAAG,MAAMA,UAAU,CAAC;EAC9Bf,CAAC;EACD;EACAgB,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBxJ,EAAE;EACF9G,WAAWA,CAACgK,CAAC,EAAEuG,CAAC,EAAElB,CAAC,EAAE;IACjB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACvI,EAAE,GAAGyJ,CAAC,CAACzD,aAAa;IACzB,IAAI,CAAChG,EAAE,CAACvC,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAAC+L,SAAS,GAAG,IAAI;MACrBtG,CAAC,CAACwG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAC1J,EAAE,CAACvC,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAAC+L,SAAS,GAAG,KAAK;MACtBtG,CAAC,CAACwG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFZ,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC9I,EAAE,EAAE,CACxB,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,EACvB,sBAAsB,EACtB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;EACA;EAAmB,OAAO9E,IAAI,YAAAyO,mBAAAvO,iBAAA;IAAA,YAAAA,iBAAA,IAAyFkO,UAAU,EAv2BpBzT,EAAE,CAAA+T,iBAAA,CAu2BoC/T,EAAE,CAACgU,iBAAiB,GAv2B1DhU,EAAE,CAAA+T,iBAAA,CAu2BqE/T,EAAE,CAACsB,UAAU,GAv2BpFtB,EAAE,CAAA+T,iBAAA,CAu2B+F/T,EAAE,CAACM,MAAM;EAAA;EACvN;EAAmB,OAAO2T,IAAI,kBAx2B+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAw2BJiP,UAAU;IAAAU,SAAA;IAAAC,cAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;MAAA,IAAAF,EAAA;QAx2BRtU,EAAE,CAAAyU,cAAA,CAAAD,QAAA,EAw2BioB9T,WAAW;MAAA;MAAA,IAAA4T,EAAA;QAAA,IAAAI,EAAA;QAx2B9oB1U,EAAE,CAAA2U,cAAA,CAAAD,EAAA,GAAF1U,EAAE,CAAA4U,WAAA,QAAAL,GAAA,CAAAb,QAAA,GAAAgB,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAzC,MAAA;MAAA0C,SAAA;MAAA/P,QAAA;MAAAgQ,KAAA;MAAAC,mBAAA;MAAAC,eAAA;MAAApE,QAAA;MAAAqE,eAAA;MAAAC,cAAA;MAAAC,KAAA;MAAAC,SAAA;MAAAxR,MAAA;MAAAyR,aAAA;MAAAC,cAAA;MAAAC,IAAA;MAAAC,YAAA;MAAAC,WAAA;MAAAC,OAAA;MAAAC,aAAA;MAAAC,SAAA;MAAAC,IAAA;MAAAC,IAAA;IAAA;IAAAC,UAAA;EAAA;AAy2BnH,CAAC;AACDvC,UAAU,GAAG5Q,UAAU,CAAC,CACpBqQ,QAAQ,CAAC;EACLd,MAAM,EAAEmB,cAAc;EACtBV,OAAO,EAAEW;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,UAAU,CAAC;AACd;EAAA,QAAA5N,SAAA,oBAAAA,SAAA,KAt3BiH7F,EAAE,CAAA8F,iBAAA,CAs3BvB2N,UAAU,EAAc,CAAC;IACzGjP,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE,aAAa;MACvB;MACA7D,MAAM,EAAEmB;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/O,IAAI,EAAExE,EAAE,CAACgU;IAAkB,CAAC,EAAE;MAAExP,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoT,QAAQ,EAAE,CAAC;MACjJlP,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAEwV,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,YAAY,GAAG,CACjB,UAAU,EACV,qBAAqB,EACrB,oBAAoB,EACpB,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,OAAO,EACP,WAAW,EACX,QAAQ,EACR,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,SAAS,CACZ;AACD,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,SAAS,EACT,cAAc,EACd,eAAe,EACf,sBAAsB,EACtB,sBAAsB,CACzB;AACD,IAAIC,QAAQ,GAAG,MAAMA,QAAQ,CAAC;EAC1B3D,CAAC;EACD;EACAgB,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBxJ,EAAE;EACF9G,WAAWA,CAACgK,CAAC,EAAEuG,CAAC,EAAElB,CAAC,EAAE;IACjB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACvI,EAAE,GAAGyJ,CAAC,CAACzD,aAAa;IACzB,IAAI,CAAChG,EAAE,CAACvC,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAAC+L,SAAS,GAAG,IAAI;MACrBtG,CAAC,CAACwG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAC1J,EAAE,CAACvC,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAAC+L,SAAS,GAAG,KAAK;MACtBtG,CAAC,CAACwG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFZ,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC9I,EAAE,EAAE,CACxB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,wBAAwB,EACxB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;EACA;EAAmB,OAAO9E,IAAI,YAAAiR,iBAAA/Q,iBAAA;IAAA,YAAAA,iBAAA,IAAyF8Q,QAAQ,EA/7BlBrW,EAAE,CAAA+T,iBAAA,CA+7BkC/T,EAAE,CAACgU,iBAAiB,GA/7BxDhU,EAAE,CAAA+T,iBAAA,CA+7BmE/T,EAAE,CAACsB,UAAU,GA/7BlFtB,EAAE,CAAA+T,iBAAA,CA+7B6F/T,EAAE,CAACM,MAAM;EAAA;EACrN;EAAmB,OAAO2T,IAAI,kBAh8B+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAg8BJ6R,QAAQ;IAAAlC,SAAA;IAAAC,cAAA,WAAAmC,wBAAAjC,EAAA,EAAAC,GAAA,EAAAC,QAAA;MAAA,IAAAF,EAAA;QAh8BNtU,EAAE,CAAAyU,cAAA,CAAAD,QAAA,EAg8BmuB9T,WAAW;MAAA;MAAA,IAAA4T,EAAA;QAAA,IAAAI,EAAA;QAh8BhvB1U,EAAE,CAAA2U,cAAA,CAAAD,EAAA,GAAF1U,EAAE,CAAA4U,WAAA,QAAAL,GAAA,CAAAb,QAAA,GAAAgB,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAzC,MAAA;MAAArN,QAAA;MAAAiQ,mBAAA;MAAAwB,kBAAA;MAAAvB,eAAA;MAAAwB,WAAA;MAAAC,UAAA;MAAA7F,QAAA;MAAAsE,cAAA;MAAAwB,cAAA;MAAAvB,KAAA;MAAAC,SAAA;MAAAuB,MAAA;MAAAC,cAAA;MAAAC,iBAAA;MAAAjT,MAAA;MAAAyR,aAAA;MAAAC,cAAA;MAAAC,IAAA;MAAAuB,iBAAA;MAAAtB,YAAA;MAAAC,WAAA;MAAAC,OAAA;IAAA;IAAAK,UAAA;EAAA;AAi8BnH,CAAC;AACDK,QAAQ,GAAGxT,UAAU,CAAC,CAClBqQ,QAAQ,CAAC;EACLd,MAAM,EAAE+D,YAAY;EACpBtD,OAAO,EAAEuD;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,QAAQ,CAAC;AACZ;EAAA,QAAAxQ,SAAA,oBAAAA,SAAA,KA98BiH7F,EAAE,CAAA8F,iBAAA,CA88BvBuQ,QAAQ,EAAc,CAAC;IACvG7R,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE,WAAW;MACrB;MACA7D,MAAM,EAAE+D;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3R,IAAI,EAAExE,EAAE,CAACgU;IAAkB,CAAC,EAAE;MAAExP,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoT,QAAQ,EAAE,CAAC;MACjJlP,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAEwV,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMc,UAAU,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEtM,SAAS,KAAK;EAC3C,IAAIA,SAAS,KAAK,MAAM,EAAE;IACtB,OAAOuM,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B,CAAC,MACI,IAAItM,SAAS,KAAK,SAAS,EAAE;IAC9B,OAAOwM,UAAU,CAACH,KAAK,EAAEC,IAAI,CAAC;EAClC,CAAC,MACI;IACD,OAAOG,OAAO,CAACJ,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMC,OAAO,GAAGA,CAACF,KAAK,EAAEC,IAAI,KAAK;EAC7BD,KAAK,GAAGA,KAAK,CAACnU,MAAM,CAAEwU,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,CAAC;EACvDN,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EAChB,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMG,UAAU,GAAGA,CAACH,KAAK,EAAEC,IAAI,KAAK;EAChC,MAAMO,KAAK,GAAGR,KAAK,CAAC/N,OAAO,CAACgO,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZR,KAAK,GAAGA,KAAK,CAACnU,MAAM,CAAEwU,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAACjM,EAAE,IAAI6L,IAAI,CAAC7L,EAAE,CAAC;EAC9E,CAAC,MACI;IACD4L,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EACpB;EACA,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMI,OAAO,GAAGA,CAACJ,KAAK,EAAEC,IAAI,KAAK;EAC7B,MAAMO,KAAK,GAAGR,KAAK,CAAC/N,OAAO,CAACgO,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZ,OAAOR,KAAK,CAACnU,MAAM,CAAEwU,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAACjM,EAAE,IAAI6L,IAAI,CAAC7L,EAAE,CAAC;EAC7E,CAAC,MACI;IACD,OAAO8L,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMQ,MAAM,GAAGA,CAAChN,MAAM,EAAEiN,cAAc,KAAK;EACvC,MAAMjL,OAAO,GAAGhC,MAAM,CAACkN,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE;IAAEC,UAAU,EAAEF;EAAe,CAAC,CAAC;EAC3E,OAAOjN,MAAM,CAACoN,YAAY,CAACpL,OAAO,CAAC;AACvC,CAAC;AACD,MAAMqL,WAAW,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC/C,IAAI,CAACA,WAAW,EAAE;IACd,OAAO,IAAI;EACf;EACA,OAAOD,YAAY,CAACT,OAAO,KAAKU,WAAW,CAACV,OAAO;AACvD,CAAC;AACD,MAAMW,cAAc,GAAGA,CAACC,SAAS,EAAEhP,GAAG,KAAK;EACvC,IAAI,CAACgP,SAAS,EAAE;IACZ,OAAOzO,SAAS;EACpB;EACA,MAAM0O,QAAQ,GAAGC,UAAU,CAAClP,GAAG,CAAC;EAChC,KAAK,IAAImP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIA,CAAC,IAAIH,SAAS,CAACI,MAAM,EAAE;MACvB,OAAOH,QAAQ,CAACE,CAAC,CAAC;IACtB;IACA,IAAIF,QAAQ,CAACE,CAAC,CAAC,KAAKH,SAAS,CAACG,CAAC,CAAC,EAAE;MAC9B,OAAO5O,SAAS;IACpB;EACJ;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,MAAM2O,UAAU,GAAIG,IAAI,IAAK;EACzB,OAAOA,IAAI,CACNC,KAAK,CAAC,GAAG,CAAC,CACVlH,GAAG,CAAEmH,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACpB7V,MAAM,CAAE4V,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;AAChC,CAAC;AACD,MAAME,WAAW,GAAI1B,IAAI,IAAK;EAC1B,IAAIA,IAAI,EAAE;IACNA,IAAI,CAAC2B,GAAG,CAACpJ,OAAO,CAAC,CAAC;IAClByH,IAAI,CAAC4B,cAAc,CAAC,CAAC;EACzB;AACJ,CAAC;;AAED;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAW;EACXtO,MAAM;EACNuO,OAAO;EACPnS,IAAI;EACJyB,QAAQ;EACR0O,KAAK,GAAG,EAAE;EACViC,WAAW;EACXC,cAAc,GAAG,KAAK;EACtBC,UAAU;EACVC,UAAU;EACVC,MAAM,GAAG,CAAC;EACVjW,WAAWA,CAAC+V,UAAU,EAAEJ,WAAW,EAAEtO,MAAM,EAAEuO,OAAO,EAAEnS,IAAI,EAAEyB,QAAQ,EAAE;IAClE,IAAI,CAACyQ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACtO,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACnS,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACyB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC6Q,UAAU,GAAGA,UAAU,KAAK1P,SAAS,GAAG2O,UAAU,CAACe,UAAU,CAAC,GAAG1P,SAAS;EACnF;EACA6P,UAAUA,CAACV,GAAG,EAAElB,cAAc,EAAE;IAC5B,MAAMxO,GAAG,GAAGuO,MAAM,CAAC,IAAI,CAAChN,MAAM,EAAEiN,cAAc,CAAC;IAC/C,MAAMtG,OAAO,GAAGwH,GAAG,EAAEtQ,QAAQ,EAAE4H,aAAa;IAC5C,MAAM2I,cAAc,GAAG9H,mBAAmB,CAAC,IAAI,CAAClK,IAAI,EAAE+R,GAAG,CAAC5I,QAAQ,EAAEoB,OAAO,CAAC;IAC5E,OAAO;MACHhG,EAAE,EAAE,IAAI,CAACiO,MAAM,EAAE;MACjB/B,OAAO,EAAEW,cAAc,CAAC,IAAI,CAACkB,UAAU,EAAEjQ,GAAG,CAAC;MAC7C2P,cAAc;MACdzH,OAAO;MACPwH,GAAG;MACH1P;IACJ,CAAC;EACL;EACAqQ,eAAeA,CAAC7B,cAAc,EAAE;IAC5B,MAAM8B,eAAe,GAAG/B,MAAM,CAAC,IAAI,CAAChN,MAAM,EAAEiN,cAAc,CAAC;IAC3D,MAAMT,IAAI,GAAG,IAAI,CAACD,KAAK,CAACyC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACxQ,GAAG,KAAKsQ,eAAe,CAAC;IAChE,IAAIvC,IAAI,EAAE;MACNA,IAAI,CAAC2B,GAAG,CAACe,iBAAiB,CAACC,QAAQ,CAAC,CAAC;IACzC;IACA,OAAO3C,IAAI;EACf;EACA4C,SAASA,CAAC9B,YAAY,EAAE;IACpB,MAAM+B,aAAa,GAAG,IAAI,CAACd,OAAO,CAAC1M,iBAAiB,CAAC,CAAC;IACtD,IAAI;MAAE3B,SAAS;MAAExG,SAAS;MAAE2G;IAAiB,CAAC,GAAGgP,aAAa;IAC9D,MAAM9B,WAAW,GAAG,IAAI,CAACoB,UAAU;IACnC,MAAMW,SAAS,GAAGjC,WAAW,CAACC,YAAY,EAAEC,WAAW,CAAC;IACxD,IAAI+B,SAAS,EAAE;MACXpP,SAAS,GAAG,MAAM;MAClBxG,SAAS,GAAGsF,SAAS;IACzB;IACA,MAAMuQ,aAAa,GAAG,IAAI,CAAChD,KAAK,CAACiD,KAAK,CAAC,CAAC;IACxC,IAAIC,iBAAiB;IACrB,MAAMzP,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B;IACA,IAAIA,MAAM,CAAC0P,oBAAoB,EAAE;MAC7BD,iBAAiB,GAAGzP,MAAM,CAAC0P,oBAAoB,CAAC,CAAC;MACjD;IACJ,CAAC,MACI,IAAI1P,MAAM,CAAC2P,WAAW,EAAEhQ,KAAK,EAAE;MAChC8P,iBAAiB,GAAGzP,MAAM,CAAC2P,WAAW,CAAChQ,KAAK;IAChD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI8P,iBAAiB,EAAEG,MAAM,EAAEC,UAAU,EAAE;MACvC,IAAI,IAAI,CAACtD,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACtB,KAAK,CAACuD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B;IACJ;IACA,MAAMC,MAAM,GAAG,IAAI,CAACxD,KAAK,CAACyD,QAAQ,CAAC1C,YAAY,CAAC;IAChD,MAAMf,KAAK,GAAG,IAAI,CAACD,UAAU,CAACgB,YAAY,EAAEpN,SAAS,CAAC;IACtD;IACA;IACA;IACA,IAAI,CAAC6P,MAAM,EAAE;MACTzC,YAAY,CAACa,GAAG,CAACe,iBAAiB,CAAC/F,aAAa,CAAC,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM8G,eAAe,GAAG3C,YAAY,CAACjN,gBAAgB;IACrD,IAAIA,gBAAgB,KAAKrB,SAAS,IAAIkB,SAAS,KAAK,MAAM,IAAI,CAACoP,SAAS,IAAIW,eAAe,KAAKjR,SAAS,EAAE;MACvGqB,gBAAgB,GAAG4P,eAAe;IACtC;IACA;AACR;AACA;AACA;IACQ,IAAI1C,WAAW,EAAE;MACbA,WAAW,CAAClN,gBAAgB,GAAGA,gBAAgB;IACnD;IACA;IACA,OAAO,IAAI,CAACjE,IAAI,CAAC6L,iBAAiB,CAAC,MAAM;MACrC,OAAO,IAAI,CAACiI,IAAI,CAAC,MAAM;QACnB;QACA;QACA,IAAI3C,WAAW,EAAE;UACbA,WAAW,CAACY,GAAG,CAACe,iBAAiB,CAACiB,MAAM,CAAC,CAAC;QAC9C;QACA;QACA7C,YAAY,CAACa,GAAG,CAACe,iBAAiB,CAACC,QAAQ,CAAC,CAAC;QAC7C,OAAO,IAAI,CAACiB,UAAU,CAAC9C,YAAY,EAAEC,WAAW,EAAE7T,SAAS,EAAE,IAAI,CAAC2W,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAEhQ,gBAAgB,CAAC,CACnGiQ,IAAI,CAAC,MAAMC,YAAY,CAACjD,YAAY,EAAEf,KAAK,EAAEgD,aAAa,EAAE,IAAI,CAAC1R,QAAQ,EAAE,IAAI,CAACzB,IAAI,CAAC,CAAC,CACtFkU,IAAI,CAAC,OAAO;UACbhD,YAAY;UACZpN,SAAS;UACTxG,SAAS;UACT4V;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAe,SAASA,CAACG,IAAI,EAAE3D,OAAO,GAAG,IAAI,CAAC4D,gBAAgB,CAAC,CAAC,EAAE;IAC/C,OAAO,IAAI,CAACC,QAAQ,CAAC7D,OAAO,CAAC,CAACgB,MAAM,GAAG2C,IAAI;EAC/C;EACA1P,GAAGA,CAAC0P,IAAI,EAAE3D,OAAO,GAAG,IAAI,CAAC4D,gBAAgB,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI,CAACrU,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,MAAMkQ,KAAK,GAAG,IAAI,CAACmE,QAAQ,CAAC7D,OAAO,CAAC;MACpC,IAAIN,KAAK,CAACsB,MAAM,IAAI2C,IAAI,EAAE;QACtB,OAAOxT,OAAO,CAACyH,OAAO,CAAC,KAAK,CAAC;MACjC;MACA,MAAM+H,IAAI,GAAGD,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG2C,IAAI,GAAG,CAAC,CAAC;MAC3C,IAAI/R,GAAG,GAAG+N,IAAI,CAAC/N,GAAG;MAClB,MAAMkS,aAAa,GAAGnE,IAAI,CAACoE,SAAS;MACpC,IAAID,aAAa,EAAE;QACf,MAAME,aAAa,GAAGF,aAAa,CAACtX,GAAG,CAAC,SAAS,CAAC;QAClD,IAAIwX,aAAa,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,CAACvS,GAAG,EAAE;UAClDA,GAAG,GAAGoS,aAAa,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAACvS,GAAG;QACvD;MACJ;MACA,MAAM;QAAE4B;MAAiB,CAAC,GAAG,IAAI,CAACkO,OAAO,CAAC1M,iBAAiB,CAAC,CAAC;MAC7D,OAAO,IAAI,CAAC0M,OAAO,CAACnN,YAAY,CAAC3C,GAAG,EAAE;QAAE,GAAG+N,IAAI,CAACyE,WAAW;QAAEvX,SAAS,EAAE2G;MAAiB,CAAC,CAAC,CAACiQ,IAAI,CAAC,MAAM,IAAI,CAAC;IAChH,CAAC,CAAC;EACN;EACAY,mBAAmBA,CAAA,EAAG;IAClB,MAAM3D,WAAW,GAAG,IAAI,CAACoB,UAAU;IACnC,IAAIpB,WAAW,EAAE;MACb,MAAMhB,KAAK,GAAG,IAAI,CAACmE,QAAQ,CAACnD,WAAW,CAACV,OAAO,CAAC;MAChD,MAAMS,YAAY,GAAGf,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC;MAC5C,MAAMoC,eAAe,GAAG3C,YAAY,CAACjN,gBAAgB;MACrD,OAAO,IAAI,CAAC6P,IAAI,CAAC,MAAM;QACnB,OAAO,IAAI,CAACE,UAAU,CAAC9C,YAAY;QAAE;QACrCC,WAAW;QAAE;QACb,MAAM,EAAE,IAAI,CAAC8C,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEJ,eAAe,CAAC;MACrD,CAAC,CAAC;IACN;IACA,OAAOjT,OAAO,CAACyH,OAAO,CAAC,CAAC;EAC5B;EACA0M,iBAAiBA,CAACC,cAAc,EAAE;IAC9B,IAAIA,cAAc,EAAE;MAChB,IAAI,CAAC3C,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAC3N,GAAG,CAAC,CAAC,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAAC6N,UAAU,EAAE;MACtB0C,OAAO,CAAC,IAAI,CAAC1C,UAAU,EAAE,IAAI,CAACpC,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAAC1O,QAAQ,EAAE,IAAI,CAACzB,IAAI,CAAC;IAC9E;EACJ;EACAkV,UAAUA,CAACzE,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACmE,QAAQ,CAAC7D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC,GAAG7O,SAAS;EACjE;EACA;AACJ;AACA;EACIuS,UAAUA,CAAC1E,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACmE,QAAQ,CAAC7D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAAC,CAAC,CAAC,GAAGvN,SAAS;EAClD;EACAyR,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC9B,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC9B,OAAO,GAAG7N,SAAS;EAChE;EACA;AACJ;AACA;EACIwS,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7C,UAAU;EAC1B;EACA8C,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACjD,WAAW,KAAKxP,SAAS;EACzC;EACA+F,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACuJ,WAAW,GAAGtP,SAAS;IAC5B,IAAI,CAACuN,KAAK,CAACvF,OAAO,CAACkH,WAAW,CAAC;IAC/B,IAAI,CAACS,UAAU,GAAG3P,SAAS;IAC3B,IAAI,CAACuN,KAAK,GAAG,EAAE;EACnB;EACAmE,QAAQA,CAAC7D,OAAO,EAAE;IACd,OAAO,IAAI,CAACN,KAAK,CAACnU,MAAM,CAAEwU,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKA,OAAO,CAAC;EAC1D;EACAP,UAAUA,CAACgB,YAAY,EAAEpN,SAAS,EAAE;IAChC,IAAI,CAACyO,UAAU,GAAGrB,YAAY;IAC9B,IAAI,CAACf,KAAK,GAAGD,UAAU,CAAC,IAAI,CAACC,KAAK,EAAEe,YAAY,EAAEpN,SAAS,CAAC;IAC5D,OAAO,IAAI,CAACqM,KAAK,CAACiD,KAAK,CAAC,CAAC;EAC7B;EACAY,UAAUA,CAAC9C,YAAY,EAAEC,WAAW,EAAErN,SAAS,EAAEwR,UAAU,EAAEC,iBAAiB,EAAEtR,gBAAgB,EAAE;IAC9F,IAAI,IAAI,CAACoO,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B,OAAOzR,OAAO,CAACyH,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,IAAI8I,WAAW,KAAKD,YAAY,EAAE;MAC9B,OAAOtQ,OAAO,CAACyH,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,MAAMmN,UAAU,GAAGtE,YAAY,GAAGA,YAAY,CAAC3G,OAAO,GAAG3H,SAAS;IAClE,MAAM6S,SAAS,GAAGtE,WAAW,GAAGA,WAAW,CAAC5G,OAAO,GAAG3H,SAAS;IAC/D,MAAMsP,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIsD,UAAU,IAAIA,UAAU,KAAKC,SAAS,EAAE;MACxCD,UAAU,CAACxL,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACpCuL,UAAU,CAACxL,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAC9C,IAAIiI,WAAW,CAACwD,MAAM,EAAE;QACpB,OAAOxD,WAAW,CAACwD,MAAM,CAACF,UAAU,EAAEC,SAAS,EAAE;UAC7CE,QAAQ,EAAE7R,SAAS,KAAKlB,SAAS,GAAG,CAAC,GAAGA,SAAS;UACjDkB,SAAS;UACTwR,UAAU;UACVC,iBAAiB;UACjBtR;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAOrD,OAAO,CAACyH,OAAO,CAAC,KAAK,CAAC;EACjC;EACMyL,IAAIA,CAAC8B,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAzQ,iBAAA;MACb,IAAIyQ,MAAI,CAACzD,WAAW,KAAKxP,SAAS,EAAE;QAChC,MAAMiT,MAAI,CAACzD,WAAW;QACtByD,MAAI,CAACzD,WAAW,GAAGxP,SAAS;MAChC;MACA,MAAMkT,OAAO,GAAID,MAAI,CAACzD,WAAW,GAAGwD,IAAI,CAAC,CAAE;MAC3CE,OAAO,CAACC,OAAO,CAAC,MAAOF,MAAI,CAACzD,WAAW,GAAGxP,SAAU,CAAC;MACrD,OAAOkT,OAAO;IAAC;EACnB;AACJ;AACA,MAAM3B,YAAY,GAAGA,CAAC6B,WAAW,EAAE7F,KAAK,EAAEgD,aAAa,EAAE1R,QAAQ,EAAEzB,IAAI,KAAK;EACxE,IAAI,OAAOV,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAO,IAAIsB,OAAO,CAAEyH,OAAO,IAAK;MAC5B/I,qBAAqB,CAAC,MAAM;QACxB2V,OAAO,CAACe,WAAW,EAAE7F,KAAK,EAAEgD,aAAa,EAAE1R,QAAQ,EAAEzB,IAAI,CAAC;QAC1DqI,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOzH,OAAO,CAACyH,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD,MAAM4M,OAAO,GAAGA,CAACe,WAAW,EAAE7F,KAAK,EAAEgD,aAAa,EAAE1R,QAAQ,EAAEzB,IAAI,KAAK;EACnE;AACJ;AACA;AACA;EACIA,IAAI,CAACC,GAAG,CAAC,MAAMkT,aAAa,CAACnX,MAAM,CAAEoU,IAAI,IAAK,CAACD,KAAK,CAACyD,QAAQ,CAACxD,IAAI,CAAC,CAAC,CAACxF,OAAO,CAACkH,WAAW,CAAC,CAAC;EAC1F3B,KAAK,CAACvF,OAAO,CAAEwF,IAAI,IAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM6F,qBAAqB,GAAGxU,QAAQ,CAACiQ,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAMuE,uBAAuB,GAAGD,qBAAqB,CAACtE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnE,IAAIvB,IAAI,KAAK4F,WAAW,IAAI5F,IAAI,CAAC/N,GAAG,KAAK6T,uBAAuB,EAAE;MAC9D,MAAM3L,OAAO,GAAG6F,IAAI,CAAC7F,OAAO;MAC5BA,OAAO,CAAC4L,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC3C5L,OAAO,CAACP,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxCmG,IAAI,CAAC2B,GAAG,CAACe,iBAAiB,CAACiB,MAAM,CAAC,CAAC;IACvC;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA,MAAMqC,eAAe,CAAC;EAClB9Q,YAAY;EACZ+Q,QAAQ;EACRC,aAAa,GAAG,IAAI;EACpBhE,UAAU;EACViE,aAAa;EACbC,SAAS;EACT;EACAC,QAAQ,GAAG,IAAI3O,OAAO,CAAC,CAAC;EACxB;EACA4O,sBAAsB,GAAG,IAAI9a,eAAe,CAAC,IAAI,CAAC;EAClD+a,SAAS,GAAG,IAAI;EAChB;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACD,SAAS;EACzB;EACAE,eAAe,GAAG,IAAI;EACtB;AACJ;AACA;EACIxZ,IAAI,GAAGzC,cAAc;EACrB;EACAkc,eAAe,GAAG,IAAI/c,YAAY,CAAC,CAAC;EACpC;EACAgd,cAAc,GAAG,IAAIhd,YAAY,CAAC,CAAC;EACnC;EACAid,cAAc,GAAG,IAAIjd,YAAY,CAAC,CAAC;EACnC;EACAkd,gBAAgB,GAAG,IAAIld,YAAY,CAAC,CAAC;EACrCmd,cAAc,GAAG3d,MAAM,CAACsB,sBAAsB,CAAC;EAC/C4G,QAAQ,GAAGlI,MAAM,CAACS,gBAAgB,CAAC;EACnCsN,mBAAmB,GAAG/N,MAAM,CAACU,mBAAmB,CAAC;EACjDkd,WAAW,GAAG5d,MAAM,CAAC6d,YAAY,EAAE;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACtD;EACAC,gCAAgC,GAAG,IAAI;EACvC;EACAzQ,MAAM,GAAGtN,MAAM,CAAC8M,MAAM,CAAC;EACvB8L,OAAO,GAAG5Y,MAAM,CAACmK,aAAa,CAAC;EAC/B,IAAIpG,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAAC+Y,QAAQ,CAAC/Y,SAAS,GAAGA,SAAS;EACvC;EACA,IAAIW,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACoY,QAAQ,CAACpY,QAAQ,GAAGA,QAAQ;EACrC;EACA,IAAInB,YAAYA,CAACya,KAAK,EAAE;IACpB,IAAI,CAAChB,aAAa,GAAGgB,KAAK;IAC1B,IAAI,CAAClB,QAAQ,CAACmB,YAAY,GAAGD,KAAK,GAC5B;MACEE,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACjB,SAAS,CAACvC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACuC,SAAS,CAACnB,cAAc,CAAC,CAAC;MAC/EqC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAClB,SAAS,CAAC1B,mBAAmB,CAAC,CAAC;MACnD6C,KAAK,EAAGC,cAAc,IAAK,IAAI,CAACpB,SAAS,CAACzB,iBAAiB,CAAC6C,cAAc;IAC9E,CAAC,GACChV,SAAS;EACnB;EACArG,WAAWA,CAACc,IAAI,EAAEwa,IAAI,EAAEC,cAAc,EAAEC,UAAU,EAAEnU,MAAM,EAAE5D,IAAI,EAAE6Q,cAAc,EAAEvL,YAAY,EAAE;IAC5F,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC+Q,QAAQ,GAAG0B,UAAU,CAAC1O,aAAa;IACxC,IAAI,CAAChM,IAAI,GAAGA,IAAI,IAAIzC,cAAc;IAClC,IAAI,CAAC0X,UAAU,GAAGuF,IAAI,KAAK,MAAM,GAAGjH,MAAM,CAAChN,MAAM,EAAEiN,cAAc,CAAC,GAAGjO,SAAS;IAC9E,IAAI,CAAC4T,SAAS,GAAG,IAAIvE,eAAe,CAAC,IAAI,CAACK,UAAU,EAAE,IAAI,CAAC+D,QAAQ,EAAEzS,MAAM,EAAE,IAAI,CAACuO,OAAO,EAAEnS,IAAI,EAAE8X,cAAc,CAAC;IAChH,IAAI,CAACZ,cAAc,CAACc,oBAAoB,CAAC,IAAI,CAAC3a,IAAI,EAAE,IAAI,CAAC;EAC7D;EACA4a,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzB,SAAS,CAAC7N,OAAO,CAAC,CAAC;IACxB,IAAI,CAACwO,WAAW,EAAEe,wBAAwB,CAAC,IAAI,CAAC;EACpD;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjB,cAAc,CAACiB,UAAU,CAAC,IAAI,CAAC9a,IAAI,CAAC;EACpD;EACA+a,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;EACAA,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;MACjB;MACA;MACA,MAAM2B,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;MACjC,IAAIG,OAAO,EAAE5D,KAAK,EAAE;QAChB,IAAI,CAAC6D,YAAY,CAACD,OAAO,CAAC5D,KAAK,EAAE4D,OAAO,CAAC/Q,QAAQ,CAAC;MACtD;IACJ;IACA,IAAI3G,OAAO,CAAEyH,OAAO,IAAK5M,gBAAgB,CAAC,IAAI,CAAC4a,QAAQ,EAAEhO,OAAO,CAAC,CAAC,CAAC6L,IAAI,CAAC,MAAM;MAC1E,IAAI,IAAI,CAACqC,aAAa,KAAK3T,SAAS,EAAE;QAClC,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAAC+J,MAAM,CAACJ,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC4P,QAAQ,CAAC3H,IAAI,KAAK,KAAK,CAAC;MAChG;IACJ,CAAC,CAAC;EACN;EACA,IAAI8J,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAAC7B,SAAS;EAC3B;EACA,IAAIzO,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACyO,SAAS,EAAE;MACjB,MAAM,IAAI8B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC9B,SAAS,CAACxN,QAAQ;EAClC;EACA,IAAI0H,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC8F,SAAS,EAAE;MACjB,MAAM,IAAI8B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC5B,eAAe;EAC/B;EACA,IAAI6B,kBAAkBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC7B,eAAe,EAAE;MACtB,OAAO,IAAI,CAACA,eAAe,CAACjC,QAAQ,CAAC7N,IAAI;IAC7C;IACA,OAAO,CAAC,CAAC;EACb;EACA;AACJ;AACA;EACIgN,MAAMA,CAAA,EAAG;IACL,MAAM,IAAI0E,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACA;AACJ;AACA;EACI;EACAE,MAAMA,CAACC,IAAI,EAAE/B,eAAe,EAAE;IAC1B,MAAM,IAAI4B,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACAI,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAClC,SAAS,EAAE;MAChB,IAAI,IAAI,CAACL,aAAa,EAAE;QACpB;QACA,MAAMgC,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC7B,aAAa,CAAC9B,SAAS,GAAG,IAAIsE,GAAG,CAACR,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMtE,aAAa,GAAG,IAAI,CAAC6B,aAAa,CAAC9B,SAAS,CAACvX,GAAG,CAAC,SAAS,CAAC;QACjE,IAAIwX,aAAa,IAAI6D,OAAO,CAAC5D,KAAK,EAAE;UAChCD,aAAa,CAACC,KAAK,GAAG;YAAE,GAAG4D,OAAO,CAAC5D;UAAM,CAAC;QAC9C;QACA;AAChB;AACA;AACA;QACgB,IAAI,CAAC4B,aAAa,CAACzB,WAAW,GAAG,CAAC,CAAC;QACnC,IAAIyD,OAAO,CAAC5D,KAAK,EAAE;UACf,MAAMsE,eAAe,GAAGV,OAAO,CAAC5D,KAAK,CAACE,QAAQ;UAC9C,IAAI,CAAC0B,aAAa,CAACzB,WAAW,CAAC9O,WAAW,GAAGiT,eAAe,CAACjT,WAAW;UACxE,IAAI,CAACuQ,aAAa,CAACzB,WAAW,CAAC7O,QAAQ,GAAGgT,eAAe,CAAChT,QAAQ;QACtE;MACJ;MACA,MAAMO,CAAC,GAAG,IAAI,CAAC2B,SAAS;MACxB,IAAI,CAACoO,aAAa,GAAG,IAAI;MACzB,IAAI,CAACK,SAAS,GAAG,IAAI;MACrB,IAAI,CAACE,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACI,gBAAgB,CAACgC,IAAI,CAAC1S,CAAC,CAAC;IACjC;EACJ;EACAgS,YAAYA,CAAC1H,cAAc,EAAEvJ,mBAAmB,EAAE;IAC9C,IAAI,IAAI,CAACkR,WAAW,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;IAClE;IACA,IAAI,CAAC5B,eAAe,GAAGhG,cAAc;IACrC,IAAIqI,MAAM;IACV,IAAIhI,YAAY,GAAG,IAAI,CAACsF,SAAS,CAAC9D,eAAe,CAAC7B,cAAc,CAAC;IACjE,IAAIK,YAAY,EAAE;MACdgI,MAAM,GAAG,IAAI,CAACvC,SAAS,GAAGzF,YAAY,CAACa,GAAG;MAC1C,MAAMoH,KAAK,GAAGjI,YAAY,CAACsD,SAAS;MACpC,IAAI2E,KAAK,EAAE;QACP;QACA;QACA,MAAMb,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjCG,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,GAAGI,KAAK;MACxC;MACA;MACA,IAAI,CAACC,yBAAyB,CAACF,MAAM,CAAC/P,QAAQ,EAAE0H,cAAc,CAAC;IACnE,CAAC,MACI;MACD,MAAM+D,QAAQ,GAAG/D,cAAc,CAACwI,eAAe;MAC/C;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,aAAa,GAAG,IAAI,CAACpC,cAAc,CAACqC,kBAAkB,CAAC,IAAI,CAAClc,IAAI,CAAC,CAAC0b,QAAQ;MAChF;MACA;MACA,MAAMS,UAAU,GAAG,IAAI5d,eAAe,CAAC,IAAI,CAAC;MAC5C,MAAM6d,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACF,UAAU,EAAE3I,cAAc,CAAC;MACtF,MAAMtJ,QAAQ,GAAG,IAAIoS,cAAc,CAACF,mBAAmB,EAAEH,aAAa,EAAE,IAAI,CAAC7X,QAAQ,CAAC8F,QAAQ,CAAC;MAC/F;MACA,MAAMW,SAAS,GAAG0M,QAAQ,CAACgF,WAAW,CAAC1R,SAAS,IAAI0M,QAAQ,CAAC1M,SAAS;MACtE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYgR,MAAM,GAAG,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACkD,aAAa,CAAClgB,eAAe,CAACuO,SAAS,EAAE;QACpEyI,KAAK,EAAE,IAAI,CAACkJ,aAAa,CAACpI,MAAM;QAChClK,QAAQ;QACRD,mBAAmB,EAAEA,mBAAmB,IAAI,IAAI,CAACA;MACrD,CAAC,CAAC;MACF;MACAkS,UAAU,CAAC/V,IAAI,CAACyV,MAAM,CAAC/P,QAAQ,CAAC;MAChC;MACA;MACA;AACZ;AACA;AACA;MACY;MACA+H,YAAY,GAAG,IAAI,CAACsF,SAAS,CAAC/D,UAAU,CAAC,IAAI,CAACkE,SAAS,EAAE9F,cAAc,CAAC;MACxE;MACA,IAAI,CAAC4F,QAAQ,CAACpM,GAAG,CAAC6O,MAAM,CAAC/P,QAAQ,EAAEsQ,mBAAmB,CAAC;MACvD,IAAI,CAAC/C,sBAAsB,CAACjT,IAAI,CAAC;QAAEyE,SAAS,EAAEgR,MAAM,CAAC/P,QAAQ;QAAE0H;MAAe,CAAC,CAAC;IACpF;IACA,IAAI,CAACsG,WAAW,EAAE2C,mCAAmC,CAAC,IAAI,CAAC;IAC3D,IAAI,CAACxD,aAAa,GAAGpF,YAAY;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiB,OAAO,CAAC3M,YAAY,CAAC,IAAI,CAAC;IAC/B,MAAM2L,WAAW,GAAG,IAAI,CAACqF,SAAS,CAACpB,aAAa,CAAC,CAAC;IAClD,IAAI,CAAC0B,eAAe,CAACmC,IAAI,CAAC;MACtB/H,YAAY;MACZgC,SAAS,EAAEjC,WAAW,CAACC,YAAY,EAAEC,WAAW;IACpD,CAAC,CAAC;IACF,IAAI,CAACqF,SAAS,CAACxD,SAAS,CAAC9B,YAAY,CAAC,CAACgD,IAAI,CAAEnN,IAAI,IAAK;MAClD,IAAI,CAACiQ,cAAc,CAACiC,IAAI,CAACC,MAAM,CAAC/P,QAAQ,CAAC;MACzC,IAAI,CAAC4N,cAAc,CAACkC,IAAI,CAAClS,IAAI,CAAC;IAClC,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIkN,SAASA,CAACG,IAAI,GAAG,CAAC,EAAE3D,OAAO,EAAE;IACzB,OAAO,IAAI,CAAC+F,SAAS,CAACvC,SAAS,CAACG,IAAI,EAAE3D,OAAO,CAAC;EAClD;EACA;AACJ;AACA;EACI/L,GAAGA,CAAC0P,IAAI,GAAG,CAAC,EAAE3D,OAAO,EAAE;IACnB,OAAO,IAAI,CAAC+F,SAAS,CAAC9R,GAAG,CAAC0P,IAAI,EAAE3D,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;EACIyE,UAAUA,CAACzE,OAAO,EAAE;IAChB,MAAMsJ,MAAM,GAAG,IAAI,CAACvD,SAAS,CAACtB,UAAU,CAACzE,OAAO,CAAC;IACjD,OAAOsJ,MAAM,GAAGA,MAAM,CAAC1X,GAAG,GAAGO,SAAS;EAC1C;EACA;AACJ;AACA;AACA;EACIoX,gBAAgBA,CAACvJ,OAAO,EAAE;IACtB,OAAO,IAAI,CAAC+F,SAAS,CAACtB,UAAU,CAACzE,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIwJ,WAAWA,CAACxJ,OAAO,EAAE;IACjB,OAAO,IAAI,CAAC+F,SAAS,CAACrB,UAAU,CAAC1E,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;EACI4D,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACmC,SAAS,CAACnC,gBAAgB,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIqF,yBAAyBA,CAACF,UAAU,EAAE3I,cAAc,EAAE;IAClD,MAAMqJ,KAAK,GAAG,IAAIpf,cAAc,CAAC,CAAC;IAClCof,KAAK,CAACb,eAAe,GAAGxI,cAAc,CAACwI,eAAe;IACtDa,KAAK,CAACvF,YAAY,GAAG9D,cAAc,CAAC8D,YAAY;IAChDuF,KAAK,CAACtF,QAAQ,GAAG/D,cAAc,CAAC+D,QAAQ;IACxCsF,KAAK,CAAC7U,MAAM,GAAGwL,cAAc,CAACxL,MAAM;IACpC6U,KAAK,CAAChS,SAAS,GAAG2I,cAAc,CAAC3I,SAAS;IAC1C;IACAgS,KAAK,CAACC,SAAS,GAAG,IAAI,CAACC,eAAe,CAACZ,UAAU,EAAE,UAAU,CAAC;IAC9DU,KAAK,CAACG,cAAc,GAAG,IAAI,CAACD,eAAe,CAACZ,UAAU,EAAE,eAAe,CAAC;IACxEU,KAAK,CAAC7X,GAAG,GAAG,IAAI,CAAC+X,eAAe,CAACZ,UAAU,EAAE,KAAK,CAAC;IACnDU,KAAK,CAAC/R,MAAM,GAAG,IAAI,CAACiS,eAAe,CAACZ,UAAU,EAAE,QAAQ,CAAC;IACzDU,KAAK,CAACnU,WAAW,GAAG,IAAI,CAACqU,eAAe,CAACZ,UAAU,EAAE,aAAa,CAAC;IACnEU,KAAK,CAAClU,QAAQ,GAAG,IAAI,CAACoU,eAAe,CAACZ,UAAU,EAAE,UAAU,CAAC;IAC7DU,KAAK,CAACnT,IAAI,GAAG,IAAI,CAACqT,eAAe,CAACZ,UAAU,EAAE,MAAM,CAAC;IACrD,OAAOU,KAAK;EAChB;EACA;AACJ;AACA;EACIE,eAAeA,CAACZ,UAAU,EAAE9H,IAAI,EAAE;IAC9B,OAAO8H,UAAU,CAACc,IAAI;IACtB;IACAte,MAAM,CAAEkM,SAAS,IAAK,CAAC,CAACA,SAAS,CAAC,EAAEjM,SAAS,CAAEiM,SAAS,IAAK,IAAI,CAACwO,sBAAsB,CAAC4D,IAAI,CAACte,MAAM,CAAEue,OAAO,IAAKA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACrS,SAAS,KAAKA,SAAS,CAAC,EAAEjM,SAAS,CAAEse,OAAO,IAAKA,OAAO,IAAIA,OAAO,CAAC1J,cAAc,CAACa,IAAI,CAAC,CAAC,EAAExV,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;EACtQ;EACA;AACJ;AACA;EACIkd,yBAAyBA,CAAClR,SAAS,EAAE2I,cAAc,EAAE;IACjD,MAAMqJ,KAAK,GAAG,IAAI,CAACzD,QAAQ,CAACxZ,GAAG,CAACiL,SAAS,CAAC;IAC1C,IAAI,CAACgS,KAAK,EAAE;MACR,MAAM,IAAIzB,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACAyB,KAAK,CAACb,eAAe,GAAGxI,cAAc,CAACwI,eAAe;IACtDa,KAAK,CAACvF,YAAY,GAAG9D,cAAc,CAAC8D,YAAY;IAChDuF,KAAK,CAACtF,QAAQ,GAAG/D,cAAc,CAAC+D,QAAQ;IACxCsF,KAAK,CAAC7U,MAAM,GAAGwL,cAAc,CAACxL,MAAM;IACpC6U,KAAK,CAAChS,SAAS,GAAG2I,cAAc,CAAC3I,SAAS;IAC1C,IAAI,CAACwO,sBAAsB,CAACjT,IAAI,CAAC;MAAEyE,SAAS;MAAE2I;IAAe,CAAC,CAAC;EACnE;EACA;EAAmB,OAAOtS,IAAI,YAAAic,wBAAA/b,iBAAA;IAAA,YAAAA,iBAAA,IAAyF2X,eAAe,EAhoDzBld,EAAE,CAAAuhB,iBAAA,CAgoDyC,MAAM,GAhoDjDvhB,EAAE,CAAAuhB,iBAAA,CAgoD6E,MAAM,GAhoDrFvhB,EAAE,CAAA+T,iBAAA,CAgoDiIjS,EAAE,CAACmL,QAAQ,GAhoD9IjN,EAAE,CAAA+T,iBAAA,CAgoDyJ/T,EAAE,CAACsB,UAAU,GAhoDxKtB,EAAE,CAAA+T,iBAAA,CAgoDmLvS,EAAE,CAACK,MAAM,GAhoD9L7B,EAAE,CAAA+T,iBAAA,CAgoDyM/T,EAAE,CAACM,MAAM,GAhoDpNN,EAAE,CAAA+T,iBAAA,CAgoD+NvS,EAAE,CAACI,cAAc,GAhoDlP5B,EAAE,CAAA+T,iBAAA,CAgoD6PmJ,eAAe;EAAA;EAC3X;EAAmB,OAAOjJ,IAAI,kBAjoD+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAioDJ0Y,eAAe;IAAA/I,SAAA;IAAA/B,MAAA;MAAArN,QAAA;MAAAX,SAAA;MAAAoR,IAAA;MAAA5R,YAAA;MAAAO,IAAA;IAAA;IAAAqd,OAAA;MAAA5D,eAAA;MAAAC,cAAA;MAAAC,cAAA;MAAAC,gBAAA;IAAA;IAAA0D,QAAA;IAAAzL,UAAA;EAAA;AAC9H;AACA;EAAA,QAAAnQ,SAAA,oBAAAA,SAAA,KAnoDiH7F,EAAE,CAAA8F,iBAAA,CAmoDvBoX,eAAe,EAAc,CAAC;IAC9G1Y,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE,mBAAmB;MAC7BwL,QAAQ,EAAE,QAAQ;MAClB;MACArP,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc;IAC5D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE5N,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DnF,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCnF,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAE1C,EAAE,CAACmL;IAAS,CAAC,EAAE;MAAEzI,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAEhD,EAAE,CAACI;IAAe,CAAC,EAAE;MAAE4C,IAAI,EAAE0Y,eAAe;MAAEvT,UAAU,EAAE,CAAC;QAC/JnF,IAAI,EAAEvD;MACV,CAAC,EAAE;QACCuD,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgE,IAAI,EAAE,CAAC;MACnCK,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE0c,eAAe,EAAE,CAAC;MAClBpZ,IAAI,EAAErD;IACV,CAAC,CAAC;IAAE0c,cAAc,EAAE,CAAC;MACjBrZ,IAAI,EAAErD;IACV,CAAC,CAAC;IAAE2c,cAAc,EAAE,CAAC;MACjBtZ,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEgY,gBAAgB,EAAE,CAAC;MACnBvZ,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0a,cAAc,CAAC;EACjBjF,KAAK;EACL4E,aAAa;EACbrQ,MAAM;EACN1M,WAAWA,CAACmY,KAAK,EAAE4E,aAAa,EAAErQ,MAAM,EAAE;IACtC,IAAI,CAACyL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrQ,MAAM,GAAGA,MAAM;EACxB;EACAhM,GAAGA,CAAC2B,KAAK,EAAEgc,aAAa,EAAE;IACtB,IAAIhc,KAAK,KAAK9D,cAAc,EAAE;MAC1B,OAAO,IAAI,CAAC4Z,KAAK;IACrB;IACA,IAAI9V,KAAK,KAAK/D,sBAAsB,EAAE;MAClC,OAAO,IAAI,CAACye,aAAa;IAC7B;IACA,OAAO,IAAI,CAACrQ,MAAM,CAAChM,GAAG,CAAC2B,KAAK,EAAEgc,aAAa,CAAC;EAChD;AACJ;AACA;AACA,MAAMxD,YAAY,GAAG,IAAI9d,cAAc,CAAC,EAAE,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuhB,0BAA0B,CAAC;EAC7BC,uBAAuB,GAAG,IAAIhC,GAAG,CAAC,CAAC;EACnCgB,mCAAmCA,CAACzU,MAAM,EAAE;IACxC,IAAI,CAAC6S,wBAAwB,CAAC7S,MAAM,CAAC;IACrC,IAAI,CAAC0V,oBAAoB,CAAC1V,MAAM,CAAC;EACrC;EACA6S,wBAAwBA,CAAC7S,MAAM,EAAE;IAC7B,IAAI,CAACyV,uBAAuB,CAAC7d,GAAG,CAACoI,MAAM,CAAC,EAAE2V,WAAW,CAAC,CAAC;IACvD,IAAI,CAACF,uBAAuB,CAAClS,MAAM,CAACvD,MAAM,CAAC;EAC/C;EACA0V,oBAAoBA,CAAC1V,MAAM,EAAE;IACzB,MAAM;MAAEwL;IAAe,CAAC,GAAGxL,MAAM;IACjC,MAAM4V,gBAAgB,GAAGpf,aAAa,CAAC,CAACgV,cAAc,CAAC9K,WAAW,EAAE8K,cAAc,CAAC1I,MAAM,EAAE0I,cAAc,CAAC9J,IAAI,CAAC,CAAC,CAC3GuT,IAAI,CAACre,SAAS,CAAC,CAAC,CAAC8J,WAAW,EAAEoC,MAAM,EAAEpB,IAAI,CAAC,EAAE4J,KAAK,KAAK;MACxD5J,IAAI,GAAG;QAAE,GAAGhB,WAAW;QAAE,GAAGoC,MAAM;QAAE,GAAGpB;MAAK,CAAC;MAC7C;MACA;MACA,IAAI4J,KAAK,KAAK,CAAC,EAAE;QACb,OAAO7U,EAAE,CAACiL,IAAI,CAAC;MACnB;MACA;MACA;MACA;MACA,OAAOnG,OAAO,CAACyH,OAAO,CAACtB,IAAI,CAAC;IAChC,CAAC,CAAC,CAAC,CACEzG,SAAS,CAAEyG,IAAI,IAAK;MACrB;MACA;MACA,IAAI,CAAC1B,MAAM,CAACmT,WAAW,IACnB,CAACnT,MAAM,CAACuR,qBAAqB,IAC7BvR,MAAM,CAACwL,cAAc,KAAKA,cAAc,IACxCA,cAAc,CAAC3I,SAAS,KAAK,IAAI,EAAE;QACnC,IAAI,CAACgQ,wBAAwB,CAAC7S,MAAM,CAAC;QACrC;MACJ;MACA,MAAM6V,MAAM,GAAG5gB,oBAAoB,CAACuW,cAAc,CAAC3I,SAAS,CAAC;MAC7D,IAAI,CAACgT,MAAM,EAAE;QACT,IAAI,CAAChD,wBAAwB,CAAC7S,MAAM,CAAC;QACrC;MACJ;MACA,KAAK,MAAM;QAAE8V;MAAa,CAAC,IAAID,MAAM,CAAC5P,MAAM,EAAE;QAC1CjG,MAAM,CAACuR,qBAAqB,CAACnN,QAAQ,CAAC0R,YAAY,EAAEpU,IAAI,CAACoU,YAAY,CAAC,CAAC;MAC3E;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,uBAAuB,CAACzQ,GAAG,CAAChF,MAAM,EAAE4V,gBAAgB,CAAC;EAC9D;EACA;EAAmB,OAAO1c,IAAI,YAAA6c,mCAAA3c,iBAAA;IAAA,YAAAA,iBAAA,IAAyFoc,0BAA0B;EAAA;EACjJ;EAAmB,OAAOnc,KAAK,kBAtvD8ExF,EAAE,CAAAyF,kBAAA;IAAAC,KAAA,EAsvDYic,0BAA0B;IAAAhc,OAAA,EAA1Bgc,0BAA0B,CAAAtc;EAAA;AACzJ;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAxvDiH7F,EAAE,CAAA8F,iBAAA,CAwvDvB6b,0BAA0B,EAAc,CAAC;IACzHnd,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAMkiB,4BAA4B,GAAGA,CAAA,KAAM;EACvC,OAAO;IACHtQ,OAAO,EAAEqM,YAAY;IACrBnM,UAAU,EAAEqQ,4BAA4B;IACxCnQ,IAAI,EAAE,CAACpQ,MAAM;EACjB,CAAC;AACL,CAAC;AACD,SAASugB,4BAA4BA,CAAC1X,MAAM,EAAE;EAC1C;AACJ;AACA;AACA;EACI,IAAIA,MAAM,EAAE2X,4BAA4B,EAAE;IACtC,OAAO,IAAIV,0BAA0B,CAAC,CAAC;EAC3C;EACA,OAAO,IAAI;AACf;AAEA,MAAMW,kBAAkB,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;AAClH,IAAIC,aAAa,GAAG,MAAMA,aAAa,CAAC;EACpCC,YAAY;EACZvJ,OAAO;EACPtL,MAAM;EACNiG,CAAC;EACDlB,CAAC;EACDvI,EAAE;EACF9G,WAAWA,CAACmf,YAAY,EAAEvJ,OAAO,EAAEtL,MAAM,EAAEiG,CAAC,EAAElB,CAAC,EAAErF,CAAC,EAAE;IAChD,IAAI,CAACmV,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACvJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACtL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiG,CAAC,GAAGA,CAAC;IACV,IAAI,CAAClB,CAAC,GAAGA,CAAC;IACVrF,CAAC,CAACwN,MAAM,CAAC,CAAC;IACV,IAAI,CAAC1Q,EAAE,GAAG,IAAI,CAACyJ,CAAC,CAACzD,aAAa;EAClC;EACA;AACJ;AACA;EACIsS,OAAOA,CAACpb,EAAE,EAAE;IACR,MAAMqb,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC/U,MAAM,CAAC5J,GAAG,CAAC,uBAAuB,CAAC;IAChF,IAAI,IAAI,CAACye,YAAY,EAAEzH,SAAS,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC9B,OAAO,CAACtN,YAAY,CAAC,MAAM,EAAEjC,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACiZ,eAAe,CAAC;MAC7E,IAAI,CAACH,YAAY,CAAChX,GAAG,CAAC,CAAC;MACvBnE,EAAE,CAACub,cAAc,CAAC,CAAC;IACvB,CAAC,MACI,IAAIF,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACzJ,OAAO,CAACnN,YAAY,CAAC4W,WAAW,EAAE;QAAEte,SAAS,EAAE,IAAI,CAACue;MAAgB,CAAC,CAAC;MAC3Etb,EAAE,CAACub,cAAc,CAAC,CAAC;IACvB;EACJ;EACA;EAAmB,OAAOvd,IAAI,YAAAwd,sBAAAtd,iBAAA;IAAA,YAAAA,iBAAA,IAAyFgd,aAAa,EA7yDvBviB,EAAE,CAAA+T,iBAAA,CA6yDuCmJ,eAAe,MA7yDxDld,EAAE,CAAA+T,iBAAA,CA6yDmFvJ,aAAa,GA7yDlGxK,EAAE,CAAA+T,iBAAA,CA6yD6G5G,MAAM,GA7yDrHnN,EAAE,CAAA+T,iBAAA,CA6yDgI/T,EAAE,CAACsB,UAAU,GA7yD/ItB,EAAE,CAAA+T,iBAAA,CA6yD0J/T,EAAE,CAACM,MAAM,GA7yDrKN,EAAE,CAAA+T,iBAAA,CA6yDgL/T,EAAE,CAACgU,iBAAiB;EAAA;EACnT;EAAmB,OAAOC,IAAI,kBA9yD+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EA8yDJ+d,aAAa;IAAAO,YAAA,WAAAC,2BAAAzO,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9yDXtU,EAAE,CAAAgjB,UAAA,mBAAAC,uCAAAC,MAAA;UAAA,OA8yDJ3O,GAAA,CAAAkO,OAAA,CAAAS,MAAc,CAAC;QAAA,CAAH,CAAC;MAAA;IAAA;IAAA9Q,MAAA;MAAA+Q,KAAA;MAAAT,WAAA;MAAAU,QAAA;MAAAC,IAAA;MAAA7N,IAAA;MAAAmN,eAAA;MAAAW,IAAA;MAAA9e,IAAA;IAAA;IAAAwR,UAAA;EAAA;AAC5H,CAAC;AACDuM,aAAa,GAAG1f,UAAU,CAAC,CACvBqQ,QAAQ,CAAC;EACLd,MAAM,EAAEkQ;AACZ,CAAC,CAAC,CACL,EAAEC,aAAa,CAAC;AACjB;EAAA,QAAA1c,SAAA,oBAAAA,SAAA,KArzDiH7F,EAAE,CAAA8F,iBAAA,CAqzDvByc,aAAa,EAAc,CAAC;IAC5G/d,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACAqM,MAAM,EAAEkQ;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9d,IAAI,EAAE0Y,eAAe;MAAEvT,UAAU,EAAE,CAAC;QACpEnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEqE,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAE2I;IAAO,CAAC,EAAE;MAAE3I,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACgU;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyO,OAAO,EAAE,CAAC;MAC/Jje,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwd,2BAA2B,CAAC;EAC9BC,gBAAgB;EAChBvK,OAAO;EACP4F,UAAU;EACVnU,MAAM;EACN+Y,UAAU;EACVC,eAAe,GAAG,SAAS;EAC3Bf,eAAe;EACftf,WAAWA,CAACmgB,gBAAgB,EAAEvK,OAAO,EAAE4F,UAAU,EAAEnU,MAAM,EAAE+Y,UAAU,EAAE;IACnE,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACvK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4F,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnU,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+Y,UAAU,GAAGA,UAAU;EAChC;EACAvE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb;IACA,MAAME,eAAe,GAAG,CACpB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,CACnB;IACD,MAAM5T,WAAW,GAAG,IAAI,CAAC2O,UAAU,CAAC1O,aAAa;IACjD,IAAI2T,eAAe,CAACpJ,QAAQ,CAACxK,WAAW,CAACG,OAAO,CAAC,EAAE;MAC/C,IAAIH,WAAW,CAAC6T,YAAY,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;QAC9C7T,WAAW,CAAC8T,eAAe,CAAC,UAAU,CAAC;MAC3C;IACJ;EACJ;EACAL,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,UAAU,EAAE/W,OAAO,EAAE;MAC1B,MAAMlE,IAAI,GAAG,IAAI,CAACgb,gBAAgB,CAACS,kBAAkB,CAAC,IAAI,CAACvZ,MAAM,CAACoN,YAAY,CAAC,IAAI,CAAC2L,UAAU,CAAC/W,OAAO,CAAC,CAAC;MACxG,IAAI,CAACmS,UAAU,CAAC1O,aAAa,CAAC3H,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACIia,OAAOA,CAACpb,EAAE,EAAE;IACR,IAAI,CAAC4R,OAAO,CAACtN,YAAY,CAAC,IAAI,CAAC+X,eAAe,EAAEha,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACiZ,eAAe,CAAC;IAC3F;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQtb,EAAE,CAACub,cAAc,CAAC,CAAC;EACvB;EACA;EAAmB,OAAOvd,IAAI,YAAA6e,oCAAA3e,iBAAA;IAAA,YAAAA,iBAAA,IAAyFge,2BAA2B,EAj5DrCvjB,EAAE,CAAA+T,iBAAA,CAi5DqDjS,EAAE,CAACqiB,gBAAgB,GAj5D1EnkB,EAAE,CAAA+T,iBAAA,CAi5DqFvJ,aAAa,GAj5DpGxK,EAAE,CAAA+T,iBAAA,CAi5D+G/T,EAAE,CAACsB,UAAU,GAj5D9HtB,EAAE,CAAA+T,iBAAA,CAi5DyIvS,EAAE,CAACK,MAAM,GAj5DpJ7B,EAAE,CAAA+T,iBAAA,CAi5D+JvS,EAAE,CAAC4iB,UAAU;EAAA;EAC3R;EAAmB,OAAOnQ,IAAI,kBAl5D+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAk5DJ+e,2BAA2B;IAAApP,SAAA;IAAA2O,YAAA,WAAAuB,yCAAA/P,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAl5DzBtU,EAAE,CAAAgjB,UAAA,mBAAAsB,qDAAApB,MAAA;UAAA,OAk5DJ3O,GAAA,CAAAkO,OAAA,CAAAS,MAAc,CAAC;QAAA,CAAW,CAAC;MAAA;IAAA;IAAA9Q,MAAA;MAAAsR,eAAA;MAAAf,eAAA;IAAA;IAAA3M,UAAA;IAAAuO,QAAA,GAl5DzBvkB,EAAE,CAAAwkB,oBAAA;EAAA;AAm5DnH;AACA;EAAA,QAAA3e,SAAA,oBAAAA,SAAA,KAp5DiH7F,EAAE,CAAA8F,iBAAA,CAo5DvByd,2BAA2B,EAAc,CAAC;IAC1H/e,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzR,IAAI,EAAE1C,EAAE,CAACqiB;IAAiB,CAAC,EAAE;MAAE3f,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAAC4iB,UAAU;MAAEza,UAAU,EAAE,CAAC;QACxKnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEujB,eAAe,EAAE,CAAC;MAC9Clf,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEyhB,eAAe,EAAE,CAAC;MAClBne,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEuhB,OAAO,EAAE,CAAC;MACVje,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0e,mCAAmC,CAAC;EACtCjB,gBAAgB;EAChBvK,OAAO;EACP4F,UAAU;EACVnU,MAAM;EACN+Y,UAAU;EACVC,eAAe,GAAG,SAAS;EAC3Bf,eAAe;EACftf,WAAWA,CAACmgB,gBAAgB,EAAEvK,OAAO,EAAE4F,UAAU,EAAEnU,MAAM,EAAE+Y,UAAU,EAAE;IACnE,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACvK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4F,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnU,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+Y,UAAU,GAAGA,UAAU;EAChC;EACAvE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,sBAAsB,CAAC,CAAC;EACjC;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,UAAU,EAAE/W,OAAO,EAAE;MAC1B,MAAMlE,IAAI,GAAG,IAAI,CAACgb,gBAAgB,CAACS,kBAAkB,CAAC,IAAI,CAACvZ,MAAM,CAACoN,YAAY,CAAC,IAAI,CAAC2L,UAAU,CAAC/W,OAAO,CAAC,CAAC;MACxG,IAAI,CAACmS,UAAU,CAAC1O,aAAa,CAAC3H,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACIia,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxJ,OAAO,CAACtN,YAAY,CAAC,IAAI,CAAC+X,eAAe,EAAEha,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACiZ,eAAe,CAAC;EAC/F;EACA;EAAmB,OAAOtd,IAAI,YAAAqf,4CAAAnf,iBAAA;IAAA,YAAAA,iBAAA,IAAyFkf,mCAAmC,EAp8D7CzkB,EAAE,CAAA+T,iBAAA,CAo8D6DjS,EAAE,CAACqiB,gBAAgB,GAp8DlFnkB,EAAE,CAAA+T,iBAAA,CAo8D6FvJ,aAAa,GAp8D5GxK,EAAE,CAAA+T,iBAAA,CAo8DuH/T,EAAE,CAACsB,UAAU,GAp8DtItB,EAAE,CAAA+T,iBAAA,CAo8DiJvS,EAAE,CAACK,MAAM,GAp8D5J7B,EAAE,CAAA+T,iBAAA,CAo8DuKvS,EAAE,CAAC4iB,UAAU;EAAA;EACnS;EAAmB,OAAOnQ,IAAI,kBAr8D+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAq8DJigB,mCAAmC;IAAAtQ,SAAA;IAAA2O,YAAA,WAAA6B,iDAAArQ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAr8DjCtU,EAAE,CAAAgjB,UAAA,mBAAA4B,6DAAA;UAAA,OAq8DJrQ,GAAA,CAAAkO,OAAA,CAAQ,CAAC;QAAA,CAAyB,CAAC;MAAA;IAAA;IAAArQ,MAAA;MAAAsR,eAAA;MAAAf,eAAA;IAAA;IAAA3M,UAAA;IAAAuO,QAAA,GAr8DjCvkB,EAAE,CAAAwkB,oBAAA;EAAA;AAs8DnH;AACA;EAAA,QAAA3e,SAAA,oBAAAA,SAAA,KAv8DiH7F,EAAE,CAAA8F,iBAAA,CAu8DvB2e,mCAAmC,EAAc,CAAC;IAClIjgB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzR,IAAI,EAAE1C,EAAE,CAACqiB;IAAiB,CAAC,EAAE;MAAE3f,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAAC4iB,UAAU;MAAEza,UAAU,EAAE,CAAC;QACxKnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEujB,eAAe,EAAE,CAAC;MAC9Clf,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEyhB,eAAe,EAAE,CAAC;MAClBne,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEuhB,OAAO,EAAE,CAAC;MACVje,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8e,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC;AAClF,MAAMC,WAAW,GAAG,CAChB,MAAM,EACN,QAAQ,EACR,aAAa,EACb,KAAK,EACL,OAAO,EACP,WAAW,EACX,aAAa,EACb,SAAS,EACT,UAAU,EACV,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,CAChB;AACD,IAAIC,MAAM,GAAG,MAAMA,MAAM,CAAC;EACtBrS,CAAC;EACDvI,EAAE;EACF9G,WAAWA,CAACwV,GAAG,EAAEzK,mBAAmB,EAAEC,QAAQ,EAAE2W,eAAe,EAAEtS,CAAC,EAAErF,CAAC,EAAE;IACnE,IAAI,CAACqF,CAAC,GAAGA,CAAC;IACVrF,CAAC,CAACwN,MAAM,CAAC,CAAC;IACV,IAAI,CAAC1Q,EAAE,GAAG0O,GAAG,CAAC1I,aAAa;IAC3B0I,GAAG,CAAC1I,aAAa,CAAC8U,QAAQ,GAAGD,eAAe,CAAC7W,MAAM,CAACC,mBAAmB,EAAEC,QAAQ,CAAC;IAClF4E,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC9I,EAAE,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;EACxE;EACA;EAAmB,OAAO9E,IAAI,YAAA6f,eAAA3f,iBAAA;IAAA,YAAAA,iBAAA,IAAyFwf,MAAM,EAj/DhB/kB,EAAE,CAAA+T,iBAAA,CAi/DgC/T,EAAE,CAACsB,UAAU,GAj/D/CtB,EAAE,CAAA+T,iBAAA,CAi/D0D/T,EAAE,CAACe,mBAAmB,GAj/DlFf,EAAE,CAAA+T,iBAAA,CAi/D6F/T,EAAE,CAACQ,QAAQ,GAj/D1GR,EAAE,CAAA+T,iBAAA,CAi/DqH9F,eAAe,GAj/DtIjO,EAAE,CAAA+T,iBAAA,CAi/DiJ/T,EAAE,CAACM,MAAM,GAj/D5JN,EAAE,CAAA+T,iBAAA,CAi/DuK/T,EAAE,CAACgU,iBAAiB;EAAA;EAC1S;EAAmB,OAAOC,IAAI,kBAl/D+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAk/DJugB,MAAM;IAAA3S,MAAA;MAAArN,QAAA;MAAAX,SAAA;MAAA+gB,IAAA;MAAAC,UAAA;MAAAxhB,YAAA;IAAA;IAAAoS,UAAA;EAAA;AACrH,CAAC;AACD+O,MAAM,GAAGliB,UAAU,CAAC,CAChBqQ,QAAQ,CAAC;EACLd,MAAM,EAAEyS,UAAU;EAClBhS,OAAO,EAAEiS;AACb,CAAC,CAAC,CACL,EAAEC,MAAM,CAAC;AACV;EAAA,QAAAlf,SAAA,oBAAAA,SAAA,KA1/DiH7F,EAAE,CAAA8F,iBAAA,CA0/DvBif,MAAM,EAAc,CAAC;IACrGvgB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACAqM,MAAM,EAAEyS;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErgB,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACe;IAAoB,CAAC,EAAE;MAAEyD,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAEyJ;IAAgB,CAAC,EAAE;MAAEzJ,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACgU;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEtN;AACA,MAAMqR,OAAO,CAAC;EACVpM,OAAO;EACPqM,SAAS;EACT;AACJ;AACA;EACIC,iBAAiB,GAAG,IAAI1kB,YAAY,CAAC,CAAC;EACtC;AACJ;AACA;EACI2kB,gBAAgB,GAAG,IAAI3kB,YAAY,CAAC,CAAC;EACrC4kB,UAAU,GAAG,QAAQ;EACrBC,MAAM,GAAG,KAAK;EACdC,WAAW;EACXC,UAAU;EACVviB,WAAWA,CAAC4V,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA4M,eAAeA,CAAA,EAAG;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,QAAQ,GAAG,IAAI,CAACnH,IAAI,CAACpG,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoG,IAAI,CAAC9J,KAAK,GAAGnL,SAAS;IACnE,IAAIoc,QAAQ,EAAE;MACV,IAAI,CAACJ,MAAM,GAAG,IAAI;MAClB,IAAI,CAACK,YAAY,CAACD,QAAQ,CAACE,GAAG,CAAC;MAC/B,IAAI,CAAChM,SAAS,CAAC,CAAC;IACpB;EACJ;EACAiM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIE,iBAAiBA,CAAC;IAAEpO,YAAY;IAAEgC;EAAU,CAAC,EAAE;IAC3C,MAAMzC,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAIyC,SAAS,IAAIzC,OAAO,KAAK7N,SAAS,EAAE;MACpC,IAAI,CAAC6b,iBAAiB,CAACxF,IAAI,CAAC;QAAEiG,GAAG,EAAEzO;MAAQ,CAAC,CAAC;IACjD;EACJ;EACA;AACJ;AACA;EACI8O,gBAAgBA,CAAC;IAAErO,YAAY;IAAEgC;EAAU,CAAC,EAAE;IAC1C,MAAMzC,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAIyC,SAAS,IAAIzC,OAAO,KAAK7N,SAAS,EAAE;MACpC,IAAI,IAAI,CAAC4c,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACX,WAAW,GAAGpO,OAAO;MACrC;MACA,IAAI,CAACiO,gBAAgB,CAACzF,IAAI,CAAC;QAAEiG,GAAG,EAAEzO;MAAQ,CAAC,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgP,MAAMA,CAACC,UAAU,EAAE;IACf,MAAMC,WAAW,GAAG,OAAOD,UAAU,KAAK,QAAQ;IAClD,MAAMR,GAAG,GAAGS,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAAClc,MAAM,CAAC0b,GAAG;IAC5D;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACN,MAAM,EAAE;MACb,IAAI,CAACK,YAAY,CAACC,GAAG,CAAC;MACtB,IAAI,CAAChM,SAAS,CAAC,CAAC;MAChB;IACJ;IACA,MAAM0M,eAAe,GAAG,IAAI,CAACva,MAAM,CAACgP,gBAAgB,CAAC,CAAC,KAAK6K,GAAG;IAC9D,MAAMW,UAAU,GAAG,GAAG,IAAI,CAACxa,MAAM,CAACiN,UAAU,IAAI4M,GAAG,EAAE;IACrD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACS,WAAW,EAAE;MACdD,UAAU,CAACI,eAAe,CAAC,CAAC;IAChC;IACA,IAAIF,eAAe,EAAE;MACjB,MAAMG,aAAa,GAAG,IAAI,CAAC1a,MAAM,CAACgP,gBAAgB,CAAC,CAAC;MACpD,MAAM9B,UAAU,GAAG,IAAI,CAAClN,MAAM,CAAC2U,gBAAgB,CAAC+F,aAAa,CAAC;MAC9D;MACA,IAAIxN,UAAU,EAAElQ,GAAG,KAAKwd,UAAU,EAAE;QAChC;MACJ;MACA,MAAMG,QAAQ,GAAG,IAAI,CAAC3a,MAAM,CAAC4U,WAAW,CAACiF,GAAG,CAAC;MAC7C,MAAMe,gBAAgB,GAAGD,QAAQ,IAAIH,UAAU,KAAKG,QAAQ,CAAC3d,GAAG,IAAI2d,QAAQ,CAACnL,WAAW;MACxF,OAAO,IAAI,CAAC1C,OAAO,CAAClN,YAAY,CAAC4a,UAAU,EAAE;QACzC,GAAGI,gBAAgB;QACnBhiB,QAAQ,EAAE,IAAI;QACd6G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAMob,SAAS,GAAG,IAAI,CAAC7a,MAAM,CAAC2U,gBAAgB,CAACkF,GAAG,CAAC;MACnD;AACZ;AACA;AACA;MACY,MAAM7c,GAAG,GAAG6d,SAAS,EAAE7d,GAAG,IAAIwd,UAAU;MACxC,MAAMI,gBAAgB,GAAGC,SAAS,EAAErL,WAAW;MAC/C,OAAO,IAAI,CAAC1C,OAAO,CAAClN,YAAY,CAAC5C,GAAG,EAAE;QAClC,GAAG4d,gBAAgB;QACnBhiB,QAAQ,EAAE,IAAI;QACd6G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;EACJ;EACAma,YAAYA,CAACC,GAAG,EAAE;IACd,MAAMrH,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMgH,WAAW,GAAGhH,IAAI,CAACjF,IAAI,CAAEuN,CAAC,IAAKA,CAAC,CAACjB,GAAG,KAAKA,GAAG,CAAC;IACnD,IAAI,CAACL,WAAW,EAAE;MACd7X,OAAO,CAACsC,KAAK,CAAC,gCAAgC4V,GAAG,kBAAkB,CAAC;MACpE;IACJ;IACA,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACD,WAAW;IAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACJ,iBAAiB,CAACxF,IAAI,CAAC;MAAEiG;IAAI,CAAC,CAAC;IACpCL,WAAW,CAACxb,EAAE,CAAC0W,MAAM,GAAG,IAAI;EAChC;EACA7G,SAASA,CAAA,EAAG;IACR,MAAM;MAAE2L,WAAW;MAAEC;IAAW,CAAC,GAAG,IAAI;IACxC,IAAI,IAAI,CAACU,MAAM,IAAIX,WAAW,EAAE;MAC5B,IAAI,CAACW,MAAM,CAACX,WAAW,GAAGA,WAAW,CAACK,GAAG;IAC7C;IACA,IAAIJ,UAAU,EAAEI,GAAG,KAAKL,WAAW,EAAEK,GAAG,EAAE;MACtC,IAAIJ,UAAU,EAAEzb,EAAE,EAAE;QAChByb,UAAU,CAACzb,EAAE,CAAC0W,MAAM,GAAG,KAAK;MAChC;IACJ;IACA,IAAI8E,WAAW,EAAE;MACb,IAAI,CAACH,gBAAgB,CAACzF,IAAI,CAAC;QAAEiG,GAAG,EAAEL,WAAW,CAACK;MAAI,CAAC,CAAC;IACxD;EACJ;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxB,MAAM,EAAE;MACb,OAAO,IAAI,CAACC,WAAW,EAAEK,GAAG;IAChC;IACA,OAAO,IAAI,CAAC7Z,MAAM,CAACgP,gBAAgB,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+K,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACiB,OAAO,CAACzV,OAAO,CAAE4U,MAAM,IAAK;MAC7B;MACA,MAAMc,WAAW,GAAGd,MAAM,CAACnc,EAAE,CAAC4Z,YAAY,CAAC,MAAM,CAAC;MAClD,IAAIqD,WAAW,KAAK,IAAI,CAAC3B,UAAU,EAAE;QACjC,IAAI,CAACA,UAAU,GAAG2B,WAAW;QAC7B,IAAI,CAACC,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIA,cAAcA,CAAA,EAAG;IACb;AACR;AACA;AACA;AACA;IACQ,MAAMf,MAAM,GAAG,IAAI,CAACA,MAAM,CAACnc,EAAE;IAC7B,IAAI,IAAI,CAACsb,UAAU,KAAK,KAAK,EAAE;MAC3B;AACZ;AACA;AACA;MACY,IAAI,CAACH,SAAS,CAACnV,aAAa,CAACmX,MAAM,CAAChB,MAAM,CAAC;IAC/C,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,IAAI,CAAChB,SAAS,CAACnV,aAAa,CAACoX,KAAK,CAACjB,MAAM,CAAC;IAC9C;EACJ;EACA;EAAmB,OAAOjhB,IAAI,YAAAmiB,gBAAAjiB,iBAAA;IAAA,YAAAA,iBAAA,IAAyF8f,OAAO,EAltEjBrlB,EAAE,CAAA+T,iBAAA,CAktEiCvJ,aAAa;EAAA;EAC7J;EAAmB,OAAOyJ,IAAI,kBAntE+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAmtEJ6gB,OAAO;IAAAlR,SAAA;IAAAsT,SAAA,WAAAC,cAAApT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAntELtU,EAAE,CAAA2nB,WAAA,CAAAzkB,GAAA,KAmtEiS5B,UAAU;MAAA;MAAA,IAAAgT,EAAA;QAAA,IAAAI,EAAA;QAntE7S1U,EAAE,CAAA2U,cAAA,CAAAD,EAAA,GAAF1U,EAAE,CAAA4U,WAAA,QAAAL,GAAA,CAAA+Q,SAAA,GAAA5Q,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAiO,YAAA,WAAA8E,qBAAAtT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFtU,EAAE,CAAAgjB,UAAA,+BAAA6E,6CAAA3E,MAAA;UAAA,OAmtEJ3O,GAAA,CAAAgS,MAAA,CAAArD,MAAa,CAAC;QAAA,CAAR,CAAC;MAAA;IAAA;IAAA1B,OAAA;MAAA+D,iBAAA;MAAAC,gBAAA;IAAA;IAAAxP,UAAA;EAAA;AACtH;AACA;EAAA,QAAAnQ,SAAA,oBAAAA,SAAA,KArtEiH7F,EAAE,CAAA8F,iBAAA,CAqtEvBuf,OAAO,EAAc,CAAC;IACtG7gB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCkQ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzR,IAAI,EAAEgG;IAAc,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8a,SAAS,EAAE,CAAC;MAC7F9gB,IAAI,EAAEjD,SAAS;MACfwE,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEd,IAAI,EAAE3D,UAAU;QAAE4U,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC,CAAC;IAAEqP,iBAAiB,EAAE,CAAC;MACpB/gB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEqkB,gBAAgB,EAAE,CAAC;MACnBhhB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEolB,MAAM,EAAE,CAAC;MACT/hB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+hB,GAAG,GAAIC,CAAC,IAAK;EACf,IAAI,OAAOC,oCAAoC,KAAK,UAAU,EAAE;IAC5D,OAAOA,oCAAoC,CAACD,CAAC,CAAC;EAClD;EACA,IAAI,OAAO3hB,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAOA,qBAAqB,CAAC2hB,CAAC,CAAC;EACnC;EACA,OAAOE,UAAU,CAACF,CAAC,CAAC;AACxB,CAAC;;AAED;AACA,MAAMG,aAAa,CAAC;EAChB7Z,QAAQ;EACRwQ,UAAU;EACVsJ,QAAQ,GAAGA,CAAA,KAAM;IACb;EAAA,CACH;EACDC,SAAS,GAAGA,CAAA,KAAM;IACd;EAAA,CACH;EACDC,SAAS;EACTC,aAAa;EACbjlB,WAAWA,CAACgL,QAAQ,EAAEwQ,UAAU,EAAE;IAC9B,IAAI,CAACxQ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwQ,UAAU,GAAGA,UAAU;EAChC;EACA0J,UAAUA,CAACle,KAAK,EAAE;IACd,IAAI,CAACwU,UAAU,CAAC1O,aAAa,CAAC9F,KAAK,GAAG,IAAI,CAACge,SAAS,GAAGhe,KAAK;IAC5Dme,eAAe,CAAC,IAAI,CAAC3J,UAAU,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4J,iBAAiBA,CAACte,EAAE,EAAEE,KAAK,EAAE;IACzB,IAAIF,EAAE,KAAK,IAAI,CAAC0U,UAAU,CAAC1O,aAAa,EAAE;MACtC,IAAI9F,KAAK,KAAK,IAAI,CAACge,SAAS,EAAE;QAC1B,IAAI,CAACA,SAAS,GAAGhe,KAAK;QACtB,IAAI,CAAC8d,QAAQ,CAAC9d,KAAK,CAAC;MACxB;MACAme,eAAe,CAAC,IAAI,CAAC3J,UAAU,CAAC;IACpC;EACJ;EACA6J,gBAAgBA,CAACve,EAAE,EAAE;IACjB,IAAIA,EAAE,KAAK,IAAI,CAAC0U,UAAU,CAAC1O,aAAa,EAAE;MACtC,IAAI,CAACiY,SAAS,CAAC,CAAC;MAChBI,eAAe,CAAC,IAAI,CAAC3J,UAAU,CAAC;MAChC;MACA;MACA;MACA;IACJ,CAAC,MACI,IAAI1U,EAAE,CAACwe,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC9J,UAAU,CAAC1O,aAAa,EAAE;MACtE,IAAI,CAACiY,SAAS,CAAC,CAAC;IACpB;EACJ;EACAQ,gBAAgBA,CAACjX,EAAE,EAAE;IACjB,IAAI,CAACwW,QAAQ,GAAGxW,EAAE;EACtB;EACAkX,iBAAiBA,CAAClX,EAAE,EAAE;IAClB,IAAI,CAACyW,SAAS,GAAGzW,EAAE;EACvB;EACAmX,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAClK,UAAU,CAAC1O,aAAa,CAACiT,QAAQ,GAAG2F,UAAU;EACvD;EACAhK,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACuJ,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACxG,WAAW,CAAC,CAAC;IACpC;EACJ;EACA+D,eAAeA,CAAA,EAAG;IACd,IAAImD,SAAS;IACb,IAAI;MACAA,SAAS,GAAG,IAAI,CAAC3a,QAAQ,CAACtK,GAAG,CAACd,SAAS,CAAC;IAC5C,CAAC,CACD,MAAM;MACF;IAAA;IAEJ,IAAI,CAAC+lB,SAAS,EAAE;MACZ;IACJ;IACA;IACA,IAAIA,SAAS,CAACV,aAAa,EAAE;MACzB,IAAI,CAACA,aAAa,GAAGU,SAAS,CAACV,aAAa,CAAClhB,SAAS,CAAC,MAAMohB,eAAe,CAAC,IAAI,CAAC3J,UAAU,CAAC,CAAC;IAClG;IACA;AACR;AACA;AACA;IACQ,MAAMoK,WAAW,GAAGD,SAAS,CAACE,OAAO;IACrC,IAAID,WAAW,EAAE;MACb,MAAME,cAAc,GAAG,CAAC,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC;MAChHA,cAAc,CAACzX,OAAO,CAAE0X,MAAM,IAAK;QAC/B,IAAI,OAAOH,WAAW,CAACG,MAAM,CAAC,KAAK,WAAW,EAAE;UAC5C,MAAMC,KAAK,GAAGJ,WAAW,CAACG,MAAM,CAAC,CAACE,IAAI,CAACL,WAAW,CAAC;UACnDA,WAAW,CAACG,MAAM,CAAC,GAAG,CAAC,GAAGna,MAAM,KAAK;YACjCoa,KAAK,CAAC,GAAGpa,MAAM,CAAC;YAChBuZ,eAAe,CAAC,IAAI,CAAC3J,UAAU,CAAC;UACpC,CAAC;QACL;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EAAmB,OAAOxZ,IAAI,YAAAkkB,sBAAAhkB,iBAAA;IAAA,YAAAA,iBAAA,IAAyF2iB,aAAa,EAt1EvBloB,EAAE,CAAA+T,iBAAA,CAs1EuC/T,EAAE,CAACQ,QAAQ,GAt1EpDR,EAAE,CAAA+T,iBAAA,CAs1E+D/T,EAAE,CAACsB,UAAU;EAAA;EAC3L;EAAmB,OAAO2S,IAAI,kBAv1E+EjU,EAAE,CAAAkU,iBAAA;IAAA1P,IAAA,EAu1EJ0jB,aAAa;IAAApF,YAAA,WAAA0G,2BAAAlV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAv1EXtU,EAAE,CAAAgjB,UAAA,qBAAAyG,yCAAAvG,MAAA;UAAA,OAu1EJ3O,GAAA,CAAAmU,gBAAA,CAAAxF,MAAA,CAAAwG,MAA8B,CAAC;QAAA,CAAnB,CAAC;MAAA;IAAA;IAAA1T,UAAA;EAAA;AAC5H;AACA;EAAA,QAAAnQ,SAAA,oBAAAA,SAAA,KAz1EiH7F,EAAE,CAAA8F,iBAAA,CAy1EvBoiB,aAAa,EAAc,CAAC;IAC5G1jB,IAAI,EAAE7D;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6D,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEonB,gBAAgB,EAAE,CAAC;MAC3HlkB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC;IACvC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyiB,eAAe,GAAInX,OAAO,IAAK;EACjCyW,GAAG,CAAC,MAAM;IACN,MAAM6B,KAAK,GAAGtY,OAAO,CAAClB,aAAa;IACnC,MAAMyZ,QAAQ,GAAGD,KAAK,CAACtf,KAAK,IAAI,IAAI,IAAIsf,KAAK,CAACtf,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAAC2L,MAAM,GAAG,CAAC;IACzE,MAAMsR,OAAO,GAAGC,UAAU,CAACH,KAAK,CAAC;IACjCI,UAAU,CAACJ,KAAK,EAAEE,OAAO,CAAC;IAC1B,MAAMtX,IAAI,GAAGoX,KAAK,CAAChB,OAAO,CAAC,UAAU,CAAC;IACtC,IAAIpW,IAAI,EAAE;MACN,IAAIqX,QAAQ,EAAE;QACVG,UAAU,CAACxX,IAAI,EAAE,CAAC,GAAGsX,OAAO,EAAE,gBAAgB,CAAC,CAAC;MACpD,CAAC,MACI;QACDE,UAAU,CAACxX,IAAI,EAAEsX,OAAO,CAAC;MAC7B;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,UAAU,GAAIzY,OAAO,IAAK;EAC5B,MAAMP,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnC,MAAM+Y,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIvR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxH,SAAS,CAACyH,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAM/F,IAAI,GAAGzB,SAAS,CAACyB,IAAI,CAAC+F,CAAC,CAAC;IAC9B,IAAI/F,IAAI,KAAK,IAAI,IAAIyX,UAAU,CAACzX,IAAI,EAAE,KAAK,CAAC,EAAE;MAC1CsX,OAAO,CAACrS,IAAI,CAAC,OAAOjF,IAAI,CAAC0X,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C;EACJ;EACA,OAAOJ,OAAO;AAClB,CAAC;AACD,MAAME,UAAU,GAAGA,CAAC1Y,OAAO,EAAEwY,OAAO,KAAK;EACrC,MAAM/Y,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnCA,SAAS,CAACoZ,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,cAAc,CAAC;EACzGpZ,SAAS,CAACC,GAAG,CAAC,GAAG8Y,OAAO,CAAC;AAC7B,CAAC;AACD,MAAMG,UAAU,GAAGA,CAACL,KAAK,EAAEQ,MAAM,KAAK;EAClC,OAAOR,KAAK,CAACM,SAAS,CAAC,CAAC,EAAEE,MAAM,CAAC5R,MAAM,CAAC,KAAK4R,MAAM;AACvD,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;AACJ;AACA;EACIC,YAAYA,CAACC,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIC,YAAYA,CAACD,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIE,KAAKA,CAACF,MAAM,EAAEG,aAAa,EAAE;IACzB;EACJ;EACA;AACJ;AACA;EACIC,QAAQA,CAACJ,MAAM,EAAE;IACb,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIK,gBAAgBA,CAACC,MAAM,EAAEC,IAAI,EAAE;IAC3B,IAAID,MAAM,CAAClK,WAAW,KAAKmK,IAAI,CAACnK,WAAW,EAAE;MACzC,OAAO,KAAK;IAChB;IACA;IACA,MAAMoK,YAAY,GAAGF,MAAM,CAAC3b,MAAM;IAClC,MAAM8b,aAAa,GAAGF,IAAI,CAAC5b,MAAM;IACjC,MAAM+b,KAAK,GAAGra,MAAM,CAACsa,IAAI,CAACH,YAAY,CAAC;IACvC,MAAMI,KAAK,GAAGva,MAAM,CAACsa,IAAI,CAACF,aAAa,CAAC;IACxC,IAAIC,KAAK,CAACzS,MAAM,KAAK2S,KAAK,CAAC3S,MAAM,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA;IACA,KAAK,MAAMlQ,GAAG,IAAI2iB,KAAK,EAAE;MACrB,IAAID,aAAa,CAAC1iB,GAAG,CAAC,KAAKyiB,YAAY,CAACziB,GAAG,CAAC,EAAE;QAC1C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAM8iB,qBAAqB,CAAC;EACxBC,IAAI;EACJ/nB,WAAWA,CAAC+nB,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACIjd,MAAMA,CAACgF,IAAI,EAAE;IACT,OAAO,IAAI,CAACiY,IAAI,CAACjd,MAAM,CAAEgF,IAAI,IAAI,CAAC,CAAE,CAAC;EACzC;EACA;AACJ;AACA;EACIkY,OAAOA,CAACxd,IAAI,EAAEyd,IAAI,EAAEjgB,EAAE,EAAE;IACpB,OAAO,IAAI,CAAC+f,IAAI,CAACC,OAAO,CAACxd,IAAI,EAAEyd,IAAI,EAAEjgB,EAAE,CAAC;EAC5C;EACA;AACJ;AACA;EACIkgB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACH,IAAI,CAACG,MAAM,CAAC,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;;AAEA,SAAStd,eAAe,EAAEd,MAAM,EAAEO,WAAW,EAAE1I,aAAa,EAAEud,aAAa,EAAElM,QAAQ,EAAE0O,MAAM,EAAEtR,UAAU,EAAEyJ,eAAe,EAAEmI,OAAO,EAAE+E,kBAAkB,EAAEjnB,cAAc,EAAEqH,aAAa,EAAEoD,SAAS,EAAEud,qBAAqB,EAAE9kB,QAAQ,EAAE6M,QAAQ,EAAEqQ,2BAA2B,EAAEkB,mCAAmC,EAAEyD,aAAa,EAAElX,mBAAmB,EAAEmR,4BAA4B,EAAE2F,GAAG,EAAEU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}