{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, l as config, j as Host, k as getElement, a as isPlatform, m as printIonWarning, d as createEvent, f as readTask, n as forceUpdate, w as writeTask, o as printIonError } from './index-B_U9CtaY.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { i as inheritAriaAttributes, h as hasLazyBuild, c as componentOnReady, e as clamp, s as shallowEqualStringMap } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { c as createKeyboardController } from './keyboard-controller-BaaVITYt.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { t as transition } from './index-DfBA5ztX.js';\nimport './index-ZjP4CjeZ.js';\nimport './keyboard-CUw4ekVy.js';\nimport './capacitor-CFERIeaU.js';\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\nconst App = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  componentDidLoad() {\n    var _this = this;\n    {\n      rIC(/*#__PURE__*/_asyncToGenerator(function* () {\n        const isHybrid = isPlatform(window, 'hybrid');\n        if (!config.getBoolean('_testing')) {\n          import('./index-CWbP1eJz.js').then(module => module.startTapClick(config));\n        }\n        if (config.getBoolean('statusTap', isHybrid)) {\n          import('./status-tap-7t9T91bG.js').then(module => module.startStatusTap());\n        }\n        if (config.getBoolean('inputShims', needInputShims())) {\n          /**\n           * needInputShims() ensures that only iOS and Android\n           * platforms proceed into this block.\n           */\n          const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n          import('./input-shims-C3lNp93k.js').then(module => module.startInputShims(config, platform));\n        }\n        const hardwareBackButtonModule = yield import('./hardware-back-button-DcH0BbDp.js');\n        const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n        if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n          hardwareBackButtonModule.startHardwareBackButton();\n        } else {\n          /**\n           * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n           * then the close watcher will not be used.\n           */\n          if (shouldUseCloseWatcher()) {\n            printIonWarning('[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n          }\n          hardwareBackButtonModule.blockHardwareBackButton();\n        }\n        if (typeof window !== 'undefined') {\n          import('./keyboard-ywgs5efA.js').then(module => module.startKeyboardAssist(window));\n        }\n        import('./focus-visible-BmVRXR1y.js').then(module => _this.focusVisible = module.startFocusVisible());\n      }));\n    }\n  }\n  /**\n   * Used to set focus on an element that uses `ion-focusable`.\n   * Do not use this if focusing the element as a result of a keyboard\n   * event as the focus utility should handle this for us. This method\n   * should be used when we want to programmatically focus an element as\n   * a result of another user action. (Ex: We focus the first element\n   * inside of a popover when the user presents it, but the popover is not always\n   * presented as a result of keyboard action.)\n   *\n   * @param elements An array of HTML elements to set focus on.\n   */\n  setFocus(elements) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.focusVisible) {\n        _this2.focusVisible.setFocus(elements);\n      }\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '9be440c65819e4fa67c2c3c6477ab40b3ad3eed3',\n      class: {\n        [mode]: true,\n        'ion-page': true,\n        'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding')\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst needInputShims = () => {\n  /**\n   * iOS always needs input shims\n   */\n  const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n  if (needsShimsIOS) {\n    return true;\n  }\n  /**\n   * Android only needs input shims when running\n   * in the browser and only if the browser is using the\n   * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n   */\n  const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n  if (isAndroidMobileWeb) {\n    return true;\n  }\n  return false;\n};\nconst rIC = callback => {\n  if ('requestIdleCallback' in window) {\n    window.requestIdleCallback(callback);\n  } else {\n    setTimeout(callback, 32);\n  }\n};\nApp.style = appCss;\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\nconst Buttons = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * If true, buttons will disappear when its\n     * parent toolbar has fully collapsed if the toolbar\n     * is not the first toolbar. If the toolbar is the\n     * first toolbar, the buttons will be hidden and will\n     * only be shown once all toolbars have fully collapsed.\n     *\n     * Only applies in `ios` mode with `collapse` set to\n     * `true` on `ion-header`.\n     *\n     * Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)\n     */\n    this.collapse = false;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b',\n      class: {\n        [mode]: true,\n        ['buttons-collapse']: this.collapse\n      }\n    }, h(\"slot\", {\n      key: '0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8'\n    }));\n  }\n};\nButtons.style = {\n  ios: buttonsIosCss,\n  md: buttonsMdCss\n};\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\nconst Content = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n    this.ionScroll = createEvent(this, \"ionScroll\", 7);\n    this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n    this.watchDog = null;\n    this.isScrolling = false;\n    this.lastScroll = 0;\n    this.queued = false;\n    this.cTop = -1;\n    this.cBottom = -1;\n    this.isMainContent = true;\n    this.resizeTimeout = null;\n    this.inheritedAttributes = {};\n    this.tabsElement = null;\n    // Detail is used in a hot loop in the scroll event, by allocating it here\n    // V8 will be able to inline any read/write to it since it's a monomorphic class.\n    // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n    this.detail = {\n      scrollTop: 0,\n      scrollLeft: 0,\n      type: 'scroll',\n      event: undefined,\n      startX: 0,\n      startY: 0,\n      startTime: 0,\n      currentX: 0,\n      currentY: 0,\n      velocityX: 0,\n      velocityY: 0,\n      deltaX: 0,\n      deltaY: 0,\n      currentTime: 0,\n      data: undefined,\n      isScrolling: true\n    };\n    /**\n     * If `true`, the content will scroll behind the headers\n     * and footers. This effect can easily be seen by setting the toolbar\n     * to transparent.\n     */\n    this.fullscreen = false;\n    /**\n     * Controls where the fixed content is placed relative to the main content\n     * in the DOM. This can be used to control the order in which fixed elements\n     * receive keyboard focus.\n     * For example, if a FAB in the fixed slot should receive keyboard focus before\n     * the main page content, set this property to `'before'`.\n     */\n    this.fixedSlotPlacement = 'after';\n    /**\n     * If you want to enable the content scrolling in the X axis, set this property to `true`.\n     */\n    this.scrollX = false;\n    /**\n     * If you want to disable the content scrolling in the Y axis, set this property to `false`.\n     */\n    this.scrollY = true;\n    /**\n     * Because of performance reasons, ionScroll events are disabled by default, in order to enable them\n     * and start listening from (ionScroll), set this property to `true`.\n     */\n    this.scrollEvents = false;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  connectedCallback() {\n    this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n    /**\n     * The fullscreen content offsets need to be\n     * computed after the tab bar has loaded. Since\n     * lazy evaluation means components are not hydrated\n     * at the same time, we need to wait for the ionTabBarLoaded\n     * event to fire. This does not impact dist-custom-elements\n     * because there is no hydration there.\n     */\n    if (hasLazyBuild(this.el)) {\n      /**\n       * We need to cache the reference to the tabs.\n       * If just the content is unmounted then we won't\n       * be able to query for the closest tabs on disconnectedCallback\n       * since the content has been removed from the DOM tree.\n       */\n      const closestTabs = this.tabsElement = this.el.closest('ion-tabs');\n      if (closestTabs !== null) {\n        /**\n         * When adding and removing the event listener\n         * we need to make sure we pass the same function reference\n         * otherwise the event listener will not be removed properly.\n         * We can't only pass `this.resize` because \"this\" in the function\n         * context becomes a reference to IonTabs instead of IonContent.\n         *\n         * Additionally, we listen for ionTabBarLoaded on the IonTabs\n         * instance rather than the IonTabBar instance. It's possible for\n         * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n         * bubbles, we can catch any instances of child tab bars loading by listening\n         * on IonTabs.\n         */\n        this.tabsLoadCallback = () => this.resize();\n        closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n      }\n    }\n  }\n  disconnectedCallback() {\n    this.onScrollEnd();\n    if (hasLazyBuild(this.el)) {\n      /**\n       * The event listener and tabs caches need to\n       * be cleared otherwise this will create a memory\n       * leak where the IonTabs instance can never be\n       * garbage collected.\n       */\n      const {\n        tabsElement,\n        tabsLoadCallback\n      } = this;\n      if (tabsElement !== null && tabsLoadCallback !== undefined) {\n        tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n      }\n      this.tabsElement = null;\n      this.tabsLoadCallback = undefined;\n    }\n  }\n  /**\n   * Rotating certain devices can update\n   * the safe area insets. As a result,\n   * the fullscreen feature on ion-content\n   * needs to be recalculated.\n   *\n   * We listen for \"resize\" because we\n   * do not care what the orientation of\n   * the device is. Other APIs\n   * such as ScreenOrientation or\n   * the deviceorientation event must have\n   * permission from the user first whereas\n   * the \"resize\" event does not.\n   *\n   * We also throttle the callback to minimize\n   * thrashing when quickly resizing a window.\n   */\n  onResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n      this.resizeTimeout = null;\n    }\n    this.resizeTimeout = setTimeout(() => {\n      /**\n       * Resize should only happen\n       * if the content is visible.\n       * When the content is hidden\n       * then offsetParent will be null.\n       */\n      if (this.el.offsetParent === null) {\n        return;\n      }\n      this.resize();\n    }, 100);\n  }\n  shouldForceOverscroll() {\n    const {\n      forceOverscroll\n    } = this;\n    const mode = getIonMode(this);\n    return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n  }\n  resize() {\n    /**\n     * Only force update if the component is rendered in a browser context.\n     * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n     * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n     * `forceUpdate` will trigger another render, locking up the server.\n     *\n     * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n     */\n    {\n      if (this.fullscreen) {\n        readTask(() => this.readDimensions());\n      } else if (this.cTop !== 0 || this.cBottom !== 0) {\n        this.cTop = this.cBottom = 0;\n        forceUpdate(this);\n      }\n    }\n  }\n  readDimensions() {\n    const page = getPageElement(this.el);\n    const top = Math.max(this.el.offsetTop, 0);\n    const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n    const dirty = top !== this.cTop || bottom !== this.cBottom;\n    if (dirty) {\n      this.cTop = top;\n      this.cBottom = bottom;\n      forceUpdate(this);\n    }\n  }\n  onScroll(ev) {\n    const timeStamp = Date.now();\n    const shouldStart = !this.isScrolling;\n    this.lastScroll = timeStamp;\n    if (shouldStart) {\n      this.onScrollStart();\n    }\n    if (!this.queued && this.scrollEvents) {\n      this.queued = true;\n      readTask(ts => {\n        this.queued = false;\n        this.detail.event = ev;\n        updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n        this.ionScroll.emit(this.detail);\n      });\n    }\n  }\n  /**\n   * Get the element where the actual scrolling takes place.\n   * This element can be used to subscribe to `scroll` events or manually modify\n   * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n   *\n   * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n   * and `scrollToPoint()` to scroll the content into a certain point.\n   */\n  getScrollElement() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this3.scrollEl) {\n        yield new Promise(resolve => componentOnReady(_this3.el, resolve));\n      }\n      return Promise.resolve(_this3.scrollEl);\n    })();\n  }\n  /**\n   * Returns the background content element.\n   * @internal\n   */\n  getBackgroundElement() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.backgroundContentEl) {\n        yield new Promise(resolve => componentOnReady(_this4.el, resolve));\n      }\n      return Promise.resolve(_this4.backgroundContentEl);\n    })();\n  }\n  /**\n   * Scroll to the top of the component.\n   *\n   * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n   */\n  scrollToTop(duration = 0) {\n    return this.scrollToPoint(undefined, 0, duration);\n  }\n  /**\n   * Scroll to the bottom of the component.\n   *\n   * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n   */\n  scrollToBottom() {\n    var _this5 = this;\n    return _asyncToGenerator(function* (duration = 0) {\n      const scrollEl = yield _this5.getScrollElement();\n      const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n      return _this5.scrollToPoint(undefined, y, duration);\n    }).apply(this, arguments);\n  }\n  /**\n   * Scroll by a specified X/Y distance in the component.\n   *\n   * @param x The amount to scroll by on the horizontal axis.\n   * @param y The amount to scroll by on the vertical axis.\n   * @param duration The amount of time to take scrolling by that amount.\n   */\n  scrollByPoint(x, y, duration) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const scrollEl = yield _this6.getScrollElement();\n      return _this6.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n    })();\n  }\n  /**\n   * Scroll to a specified X/Y location in the component.\n   *\n   * @param x The point to scroll to on the horizontal axis.\n   * @param y The point to scroll to on the vertical axis.\n   * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n   */\n  scrollToPoint(_x, _x2) {\n    var _this7 = this;\n    return _asyncToGenerator(function* (x, y, duration = 0) {\n      const el = yield _this7.getScrollElement();\n      if (duration < 32) {\n        if (y != null) {\n          el.scrollTop = y;\n        }\n        if (x != null) {\n          el.scrollLeft = x;\n        }\n        return;\n      }\n      let resolve;\n      let startTime = 0;\n      const promise = new Promise(r => resolve = r);\n      const fromY = el.scrollTop;\n      const fromX = el.scrollLeft;\n      const deltaY = y != null ? y - fromY : 0;\n      const deltaX = x != null ? x - fromX : 0;\n      // scroll loop\n      const step = timeStamp => {\n        const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n        const easedT = Math.pow(linearTime, 3) + 1;\n        if (deltaY !== 0) {\n          el.scrollTop = Math.floor(easedT * deltaY + fromY);\n        }\n        if (deltaX !== 0) {\n          el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n        }\n        if (easedT < 1) {\n          // do not use DomController here\n          // must use nativeRaf in order to fire in the next frame\n          requestAnimationFrame(step);\n        } else {\n          resolve();\n        }\n      };\n      // chill out for a frame first\n      requestAnimationFrame(ts => {\n        startTime = ts;\n        step(ts);\n      });\n      return promise;\n    }).apply(this, arguments);\n  }\n  onScrollStart() {\n    this.isScrolling = true;\n    this.ionScrollStart.emit({\n      isScrolling: true\n    });\n    if (this.watchDog) {\n      clearInterval(this.watchDog);\n    }\n    // watchdog\n    this.watchDog = setInterval(() => {\n      if (this.lastScroll < Date.now() - 120) {\n        this.onScrollEnd();\n      }\n    }, 100);\n  }\n  onScrollEnd() {\n    if (this.watchDog) clearInterval(this.watchDog);\n    this.watchDog = null;\n    if (this.isScrolling) {\n      this.isScrolling = false;\n      this.ionScrollEnd.emit({\n        isScrolling: false\n      });\n    }\n  }\n  render() {\n    const {\n      fixedSlotPlacement,\n      inheritedAttributes,\n      isMainContent,\n      scrollX,\n      scrollY,\n      el\n    } = this;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const mode = getIonMode(this);\n    const forceOverscroll = this.shouldForceOverscroll();\n    const transitionShadow = mode === 'ios';\n    this.resize();\n    return h(Host, Object.assign({\n      key: 'f2a24aa66dbf5c76f9d4b06f708eb73cadc239df',\n      role: isMainContent ? 'main' : undefined,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'content-sizing': hostContext('ion-popover', this.el),\n        overscroll: forceOverscroll,\n        [`content-${rtl}`]: true\n      }),\n      style: {\n        '--offset-top': `${this.cTop}px`,\n        '--offset-bottom': `${this.cBottom}px`\n      }\n    }, inheritedAttributes), h(\"div\", {\n      key: '6480ca7648b278abb36477b3838bccbcd4995e2a',\n      ref: el => this.backgroundContentEl = el,\n      id: \"background-content\",\n      part: \"background\"\n    }), fixedSlotPlacement === 'before' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null, h(\"div\", {\n      key: '29a23b663f5f0215bb000820c01e1814c0d55985',\n      class: {\n        'inner-scroll': true,\n        'scroll-x': scrollX,\n        'scroll-y': scrollY,\n        overscroll: (scrollX || scrollY) && forceOverscroll\n      },\n      ref: scrollEl => this.scrollEl = scrollEl,\n      onScroll: this.scrollEvents ? ev => this.onScroll(ev) : undefined,\n      part: \"scroll\"\n    }, h(\"slot\", {\n      key: '0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0'\n    })), transitionShadow ? h(\"div\", {\n      class: \"transition-effect\"\n    }, h(\"div\", {\n      class: \"transition-cover\"\n    }), h(\"div\", {\n      class: \"transition-shadow\"\n    })) : null, fixedSlotPlacement === 'after' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null);\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getParentElement = el => {\n  var _a;\n  if (el.parentElement) {\n    // normal element with a parent element\n    return el.parentElement;\n  }\n  if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n    // shadow dom's document fragment\n    return el.parentNode.host;\n  }\n  return null;\n};\nconst getPageElement = el => {\n  const tabs = el.closest('ion-tabs');\n  if (tabs) {\n    return tabs;\n  }\n  /**\n   * If we're in a popover, we need to use its wrapper so we can account for space\n   * between the popover and the edges of the screen. But if the popover contains\n   * its own page element, we should use that instead.\n   */\n  const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n  if (page) {\n    return page;\n  }\n  return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  const currentX = el.scrollLeft;\n  const currentY = el.scrollTop;\n  const timeDelta = timestamp - prevT;\n  if (shouldStart) {\n    // remember the start positions\n    detail.startTime = timestamp;\n    detail.startX = currentX;\n    detail.startY = currentY;\n    detail.velocityX = detail.velocityY = 0;\n  }\n  detail.currentTime = timestamp;\n  detail.currentX = detail.scrollLeft = currentX;\n  detail.currentY = detail.scrollTop = currentY;\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n};\nContent.style = contentCss;\nconst handleFooterFade = (scrollEl, baseEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n    /**\n     * Toolbar background will fade\n     * out over fadeDuration in pixels.\n     */\n    const fadeDuration = 10;\n    /**\n     * Begin fading out maxScroll - 30px\n     * from the bottom of the content.\n     * Also determine how close we are\n     * to starting the fade. If we are\n     * before the starting point, the\n     * scale value will get clamped to 0.\n     * If we are after the maxScroll (rubber\n     * band scrolling), the scale value will\n     * get clamped to 1.\n     */\n    const fadeStart = maxScroll - fadeDuration;\n    const distanceToStart = scrollTop - fadeStart;\n    const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst Footer = class {\n  constructor(hostRef) {\n    var _this8 = this;\n    registerInstance(this, hostRef);\n    this.keyboardCtrl = null;\n    this.keyboardVisible = false;\n    /**\n     * If `true`, the footer will be translucent.\n     * Only applies when the mode is `\"ios\"` and the device supports\n     * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n     *\n     * Note: In order to scroll content behind the footer, the `fullscreen`\n     * attribute needs to be set on the content.\n     */\n    this.translucent = false;\n    this.checkCollapsibleFooter = () => {\n      const mode = getIonMode(this);\n      if (mode !== 'ios') {\n        return;\n      }\n      const {\n        collapse\n      } = this;\n      const hasFade = collapse === 'fade';\n      this.destroyCollapsibleFooter();\n      if (hasFade) {\n        const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n        const contentEl = pageEl ? findIonContent(pageEl) : null;\n        if (!contentEl) {\n          printIonContentErrorMsg(this.el);\n          return;\n        }\n        this.setupFadeFooter(contentEl);\n      }\n    };\n    this.setupFadeFooter = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (contentEl) {\n        const scrollEl = _this8.scrollEl = yield getScrollElement(contentEl);\n        /**\n         * Handle fading of toolbars on scroll\n         */\n        _this8.contentScrollCallback = () => {\n          handleFooterFade(scrollEl, _this8.el);\n        };\n        scrollEl.addEventListener('scroll', _this8.contentScrollCallback);\n        handleFooterFade(scrollEl, _this8.el);\n      });\n      return function (_x3) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n  }\n  componentDidLoad() {\n    this.checkCollapsibleFooter();\n  }\n  componentDidUpdate() {\n    this.checkCollapsibleFooter();\n  }\n  connectedCallback() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      _this9.keyboardCtrl = yield createKeyboardController(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (keyboardOpen, waitForResize) {\n          /**\n           * If the keyboard is hiding, then we need to wait\n           * for the webview to resize. Otherwise, the footer\n           * will flicker before the webview resizes.\n           */\n          if (keyboardOpen === false && waitForResize !== undefined) {\n            yield waitForResize;\n          }\n          _this9.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n        });\n        return function (_x4, _x5) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  disconnectedCallback() {\n    if (this.keyboardCtrl) {\n      this.keyboardCtrl.destroy();\n    }\n  }\n  destroyCollapsibleFooter() {\n    if (this.scrollEl && this.contentScrollCallback) {\n      this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n      this.contentScrollCallback = undefined;\n    }\n  }\n  render() {\n    const {\n      translucent,\n      collapse\n    } = this;\n    const mode = getIonMode(this);\n    const tabs = this.el.closest('ion-tabs');\n    const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n    return h(Host, {\n      key: 'ddc228f1a1e7fa4f707dccf74db2490ca3241137',\n      role: \"contentinfo\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`footer-${mode}`]: true,\n        [`footer-translucent`]: translucent,\n        [`footer-translucent-${mode}`]: translucent,\n        ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n        [`footer-collapse-${collapse}`]: collapse !== undefined\n      }\n    }, mode === 'ios' && translucent && h(\"div\", {\n      key: 'e16ed4963ff94e06de77eb8038201820af73937c',\n      class: \"footer-background\"\n    }), h(\"slot\", {\n      key: 'f186934febf85d37133d9351a96c1a64b0a4b203'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nFooter.style = {\n  ios: footerIosCss,\n  md: footerMdCss\n};\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = tagName => {\n  const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n  if (getCachedEl !== null) {\n    return getCachedEl;\n  }\n  const clonedEl = document.createElement(tagName);\n  clonedEl.classList.add('ion-cloned-element');\n  clonedEl.style.setProperty('display', 'none');\n  document.body.appendChild(clonedEl);\n  return clonedEl;\n};\nconst createHeaderIndex = headerEl => {\n  if (!headerEl) {\n    return;\n  }\n  const toolbars = headerEl.querySelectorAll('ion-toolbar');\n  return {\n    el: headerEl,\n    toolbars: Array.from(toolbars).map(toolbar => {\n      const ionTitleEl = toolbar.querySelector('ion-title');\n      return {\n        el: toolbar,\n        background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n        ionTitleEl,\n        innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n        ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons'))\n      };\n    })\n  };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n    // Native refresher should not cause titles to scale\n    const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n    if (nativeRefresher === null) {\n      writeTask(() => {\n        scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n      });\n    }\n  });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n  /**\n   * Fading in the backdrop opacity\n   * should happen after the large title\n   * has collapsed, so it is handled\n   * by handleHeaderFade()\n   */\n  if (headerEl.collapse === 'fade') {\n    return;\n  }\n  if (opacity === undefined) {\n    headerEl.style.removeProperty('--opacity-scale');\n  } else {\n    headerEl.style.setProperty('--opacity-scale', opacity.toString());\n  }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n  if (!ev[0].isIntersecting) {\n    return;\n  }\n  /**\n   * There is a bug in Safari where overflow scrolling on a non-body element\n   * does not always reset the scrollTop position to 0 when letting go. It will\n   * set to 1 once the rubber band effect has ended. This causes the background to\n   * appear slightly on certain app setups.\n   *\n   * Additionally, we check if user is rubber banding (scrolling is negative)\n   * as this can mean they are using pull to refresh. Once the refresher starts,\n   * the content is transformed which can cause the intersection observer to erroneously\n   * fire here as well.\n   */\n  const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : (1 - ev[0].intersectionRatio) * 100 / 75;\n  setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev,\n// TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n  writeTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n    const event = ev[0];\n    const intersection = event.intersectionRect;\n    const intersectionArea = intersection.width * intersection.height;\n    const rootArea = event.rootBounds.width * event.rootBounds.height;\n    const isPageHidden = intersectionArea === 0 && rootArea === 0;\n    const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n    const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n    const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n    if (isPageHidden || isPageTransitioning) {\n      return;\n    }\n    if (event.isIntersecting) {\n      setHeaderActive(mainHeaderIndex, false);\n      setHeaderActive(scrollHeaderIndex);\n    } else {\n      /**\n       * There is a bug with IntersectionObserver on Safari\n       * where `event.isIntersecting === false` when cancelling\n       * a swipe to go back gesture. Checking the intersection\n       * x, y, width, and height provides a workaround. This bug\n       * does not happen when using Safari + Web Animations,\n       * only Safari + CSS Animations.\n       */\n      const hasValidIntersection = intersection.x === 0 && intersection.y === 0 || intersection.width !== 0 && intersection.height !== 0;\n      if (hasValidIntersection && scrollTop > 0) {\n        setHeaderActive(mainHeaderIndex);\n        setHeaderActive(scrollHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el);\n      }\n    }\n  });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n  const headerEl = headerIndex.el;\n  const toolbars = headerIndex.toolbars;\n  const ionTitles = toolbars.map(toolbar => toolbar.ionTitleEl);\n  if (active) {\n    headerEl.classList.remove('header-collapse-condense-inactive');\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.removeAttribute('aria-hidden');\n      }\n    });\n  } else {\n    headerEl.classList.add('header-collapse-condense-inactive');\n    /**\n     * The small title should only be accessed by screen readers\n     * when the large title collapses into the small title due\n     * to scrolling.\n     *\n     * Originally, the header was given `aria-hidden=\"true\"`\n     * but this caused issues with screen readers not being\n     * able to access any focusable elements within the header.\n     */\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.setAttribute('aria-hidden', 'true');\n      }\n    });\n  }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n  toolbars.forEach(toolbar => {\n    const ionTitle = toolbar.ionTitleEl;\n    const titleDiv = toolbar.innerTitleEl;\n    if (!ionTitle || ionTitle.size !== 'large') {\n      return;\n    }\n    titleDiv.style.transition = transition ? TRANSITION : '';\n    titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n  });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const baseElHeight = baseEl.clientHeight;\n    const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n    /**\n     * If we are using fade header with a condense\n     * header, then the toolbar backgrounds should\n     * not begin to fade in until the condense\n     * header has fully collapsed.\n     *\n     * Additionally, the main content should not\n     * overflow out of the container until the\n     * condense header has fully collapsed. When\n     * using just the condense header the content\n     * should overflow out of the container.\n     */\n    if (condenseHeader !== null && scrollTop < fadeStart) {\n      baseEl.style.setProperty('--opacity-scale', '0');\n      scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n      return;\n    }\n    const distanceToStart = scrollTop - fadeStart;\n    const fadeDuration = 10;\n    const scale = clamp(0, distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      scrollEl.style.removeProperty('clip-path');\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst Header = class {\n  constructor(hostRef) {\n    var _this0 = this;\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    /**\n     * If `true`, the header will be translucent.\n     * Only applies when the mode is `\"ios\"` and the device supports\n     * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n     *\n     * Note: In order to scroll content behind the header, the `fullscreen`\n     * attribute needs to be set on the content.\n     */\n    this.translucent = false;\n    this.setupFadeHeader = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(function* (contentEl, condenseHeader) {\n        const scrollEl = _this0.scrollEl = yield getScrollElement(contentEl);\n        /**\n         * Handle fading of toolbars on scroll\n         */\n        _this0.contentScrollCallback = () => {\n          handleHeaderFade(_this0.scrollEl, _this0.el, condenseHeader);\n        };\n        scrollEl.addEventListener('scroll', _this0.contentScrollCallback);\n        handleHeaderFade(_this0.scrollEl, _this0.el, condenseHeader);\n      });\n      return function (_x6, _x7) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.checkCollapsibleHeader();\n  }\n  componentDidUpdate() {\n    this.checkCollapsibleHeader();\n  }\n  disconnectedCallback() {\n    this.destroyCollapsibleHeader();\n  }\n  checkCollapsibleHeader() {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      const mode = getIonMode(_this1);\n      if (mode !== 'ios') {\n        return;\n      }\n      const {\n        collapse\n      } = _this1;\n      const hasCondense = collapse === 'condense';\n      const hasFade = collapse === 'fade';\n      _this1.destroyCollapsibleHeader();\n      if (hasCondense) {\n        const pageEl = _this1.el.closest('ion-app,ion-page,.ion-page,page-inner');\n        const contentEl = pageEl ? findIonContent(pageEl) : null;\n        // Cloned elements are always needed in iOS transition\n        writeTask(() => {\n          const title = cloneElement('ion-title');\n          title.size = 'large';\n          cloneElement('ion-back-button');\n        });\n        yield _this1.setupCondenseHeader(contentEl, pageEl);\n      } else if (hasFade) {\n        const pageEl = _this1.el.closest('ion-app,ion-page,.ion-page,page-inner');\n        const contentEl = pageEl ? findIonContent(pageEl) : null;\n        if (!contentEl) {\n          printIonContentErrorMsg(_this1.el);\n          return;\n        }\n        const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n        yield _this1.setupFadeHeader(contentEl, condenseHeader);\n      }\n    })();\n  }\n  destroyCollapsibleHeader() {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n      this.intersectionObserver = undefined;\n    }\n    if (this.scrollEl && this.contentScrollCallback) {\n      this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n      this.contentScrollCallback = undefined;\n    }\n    if (this.collapsibleMainHeader) {\n      this.collapsibleMainHeader.classList.remove('header-collapse-main');\n      this.collapsibleMainHeader = undefined;\n    }\n  }\n  setupCondenseHeader(contentEl, pageEl) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      if (!contentEl || !pageEl) {\n        printIonContentErrorMsg(_this10.el);\n        return;\n      }\n      if (typeof IntersectionObserver === 'undefined') {\n        return;\n      }\n      _this10.scrollEl = yield getScrollElement(contentEl);\n      const headers = pageEl.querySelectorAll('ion-header');\n      _this10.collapsibleMainHeader = Array.from(headers).find(header => header.collapse !== 'condense');\n      if (!_this10.collapsibleMainHeader) {\n        return;\n      }\n      const mainHeaderIndex = createHeaderIndex(_this10.collapsibleMainHeader);\n      const scrollHeaderIndex = createHeaderIndex(_this10.el);\n      if (!mainHeaderIndex || !scrollHeaderIndex) {\n        return;\n      }\n      setHeaderActive(mainHeaderIndex, false);\n      setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n      /**\n       * Handle interaction between toolbar collapse and\n       * showing/hiding content in the primary ion-header\n       * as well as progressively showing/hiding the main header\n       * border as the top-most toolbar collapses or expands.\n       */\n      const toolbarIntersection = ev => {\n        handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, _this10.scrollEl);\n      };\n      _this10.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n        root: contentEl,\n        threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]\n      });\n      _this10.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n      /**\n       * Handle scaling of large iOS titles and\n       * showing/hiding border on last toolbar\n       * in primary header\n       */\n      _this10.contentScrollCallback = () => {\n        handleContentScroll(_this10.scrollEl, scrollHeaderIndex, contentEl);\n      };\n      _this10.scrollEl.addEventListener('scroll', _this10.contentScrollCallback);\n      writeTask(() => {\n        if (_this10.collapsibleMainHeader !== undefined) {\n          _this10.collapsibleMainHeader.classList.add('header-collapse-main');\n        }\n      });\n    })();\n  }\n  render() {\n    const {\n      translucent,\n      inheritedAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const collapse = this.collapse || 'none';\n    // banner role must be at top level, so remove role if inside a menu\n    const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n    return h(Host, Object.assign({\n      key: 'b6cc27f0b08afc9fcc889683525da765d80ba672',\n      role: roleType,\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`header-${mode}`]: true,\n        [`header-translucent`]: this.translucent,\n        [`header-collapse-${collapse}`]: true,\n        [`header-translucent-${mode}`]: this.translucent\n      }\n    }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", {\n      key: '395766d4dcee3398bc91960db21f922095292f14',\n      class: \"header-background\"\n    }), h(\"slot\", {\n      key: '09a67ece27b258ff1248805d43d92a49b2c6859a'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nHeader.style = {\n  ios: headerIosCss,\n  md: headerMdCss\n};\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst RouterOutlet = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n    this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n    this.lockController = createLockController();\n    this.gestureOrAnimationInProgress = false;\n    /**\n     * The mode determines which platform styles to use.\n     */\n    this.mode = getIonMode(this);\n    /**\n     * If `true`, the router-outlet should animate the transition of components.\n     */\n    this.animated = true;\n  }\n  swipeHandlerChanged() {\n    if (this.gesture) {\n      this.gesture.enable(this.swipeHandler !== undefined);\n    }\n  }\n  connectedCallback() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      const onStart = () => {\n        _this11.gestureOrAnimationInProgress = true;\n        if (_this11.swipeHandler) {\n          _this11.swipeHandler.onStart();\n        }\n      };\n      _this11.gesture = (yield import('./swipe-back-VdaUzLWy.js')).createSwipeBackGesture(_this11.el, () => !_this11.gestureOrAnimationInProgress && !!_this11.swipeHandler && _this11.swipeHandler.canStart(), () => onStart(), step => {\n        var _a;\n        return (_a = _this11.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step);\n      }, (shouldComplete, step, dur) => {\n        if (_this11.ani) {\n          _this11.ani.onFinish(() => {\n            _this11.gestureOrAnimationInProgress = false;\n            if (_this11.swipeHandler) {\n              _this11.swipeHandler.onEnd(shouldComplete);\n            }\n          }, {\n            oneTimeCallback: true\n          });\n          // Account for rounding errors in JS\n          let newStepValue = shouldComplete ? -1e-3 : 0.001;\n          /**\n           * Animation will be reversed here, so need to\n           * reverse the easing curve as well\n           *\n           * Additionally, we need to account for the time relative\n           * to the new easing curve, as `stepValue` is going to be given\n           * in terms of a linear curve.\n           */\n          if (!shouldComplete) {\n            _this11.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n            newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n          } else {\n            newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n          }\n          _this11.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n        } else {\n          _this11.gestureOrAnimationInProgress = false;\n        }\n      });\n      _this11.swipeHandlerChanged();\n    })();\n  }\n  componentWillLoad() {\n    this.ionNavWillLoad.emit();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /** @internal */\n  commit(enteringEl, leavingEl, opts) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this12.lockController.lock();\n      let changed = false;\n      try {\n        changed = yield _this12.transition(enteringEl, leavingEl, opts);\n      } catch (e) {\n        printIonError('[ion-router-outlet] - Exception in commit:', e);\n      }\n      unlock();\n      return changed;\n    })();\n  }\n  /** @internal */\n  setRouteId(id, params, direction, animation) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      const changed = yield _this13.setRoot(id, params, {\n        duration: direction === 'root' ? 0 : undefined,\n        direction: direction === 'back' ? 'back' : 'forward',\n        animationBuilder: animation\n      });\n      return {\n        changed,\n        element: _this13.activeEl\n      };\n    })();\n  }\n  /** @internal */\n  getRouteId() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      const active = _this14.activeEl;\n      return active ? {\n        id: active.tagName,\n        element: active,\n        params: _this14.activeParams\n      } : undefined;\n    })();\n  }\n  setRoot(component, params, opts) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      if (_this15.activeComponent === component && shallowEqualStringMap(params, _this15.activeParams)) {\n        return false;\n      }\n      // attach entering view to DOM\n      const leavingEl = _this15.activeEl;\n      const enteringEl = yield attachComponent(_this15.delegate, _this15.el, component, ['ion-page', 'ion-page-invisible'], params);\n      _this15.activeComponent = component;\n      _this15.activeEl = enteringEl;\n      _this15.activeParams = params;\n      // commit animation\n      yield _this15.commit(enteringEl, leavingEl, opts);\n      yield detachComponent(_this15.delegate, leavingEl);\n      return true;\n    })();\n  }\n  transition(_x8, _x9) {\n    var _this16 = this;\n    return _asyncToGenerator(function* (enteringEl, leavingEl, opts = {}) {\n      if (leavingEl === enteringEl) {\n        return false;\n      }\n      // emit nav will change event\n      _this16.ionNavWillChange.emit();\n      const {\n        el,\n        mode\n      } = _this16;\n      const animated = _this16.animated && config.getBoolean('animated', true);\n      const animationBuilder = opts.animationBuilder || _this16.animation || config.get('navAnimation');\n      yield transition(Object.assign(Object.assign({\n        mode,\n        animated,\n        enteringEl,\n        leavingEl,\n        baseEl: el,\n        /**\n         * We need to wait for all Stencil components\n         * to be ready only when using the lazy\n         * loaded bundle.\n         */\n        deepWait: hasLazyBuild(el),\n        progressCallback: opts.progressAnimation ? ani => {\n          /**\n           * Because this progress callback is called asynchronously\n           * it is possible for the gesture to start and end before\n           * the animation is ever set. In that scenario, we should\n           * immediately call progressEnd so that the transition promise\n           * resolves and the gesture does not get locked up.\n           */\n          if (ani !== undefined && !_this16.gestureOrAnimationInProgress) {\n            _this16.gestureOrAnimationInProgress = true;\n            ani.onFinish(() => {\n              _this16.gestureOrAnimationInProgress = false;\n              if (_this16.swipeHandler) {\n                _this16.swipeHandler.onEnd(false);\n              }\n            }, {\n              oneTimeCallback: true\n            });\n            /**\n             * Playing animation to beginning\n             * with a duration of 0 prevents\n             * any flickering when the animation\n             * is later cleaned up.\n             */\n            ani.progressEnd(0, 0, 0);\n          } else {\n            _this16.ani = ani;\n          }\n        } : undefined\n      }, opts), {\n        animationBuilder\n      }));\n      // emit nav changed event\n      _this16.ionNavDidChange.emit();\n      return true;\n    }).apply(this, arguments);\n  }\n  render() {\n    return h(\"slot\", {\n      key: '84b50f1155b0d780dff802ee13223287259fd525'\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"swipeHandler\": [\"swipeHandlerChanged\"]\n    };\n  }\n};\nRouterOutlet.style = routerOutletCss;\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\nconst ToolbarTitle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n  }\n  sizeChanged() {\n    this.emitStyle();\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    const size = this.getSize();\n    this.ionStyle.emit({\n      [`title-${size}`]: true\n    });\n  }\n  getSize() {\n    return this.size !== undefined ? this.size : 'default';\n  }\n  render() {\n    const mode = getIonMode(this);\n    const size = this.getSize();\n    return h(Host, {\n      key: 'e599c0bf1b0817df3fa8360bdcd6d787f751c371',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        [`title-${size}`]: true,\n        'title-rtl': document.dir === 'rtl'\n      })\n    }, h(\"div\", {\n      key: '6e7eee9047d6759876bb31d7305b76efc7c4338c',\n      class: \"toolbar-title\"\n    }, h(\"slot\", {\n      key: 'bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"size\": [\"sizeChanged\"]\n    };\n  }\n};\nToolbarTitle.style = {\n  ios: titleIosCss,\n  md: titleMdCss\n};\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\nconst Toolbar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.childrenStyles = new Map();\n  }\n  componentWillLoad() {\n    const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n    const firstButtons = buttons.find(button => {\n      return button.slot === 'start';\n    });\n    if (firstButtons) {\n      firstButtons.classList.add('buttons-first-slot');\n    }\n    const buttonsReversed = buttons.reverse();\n    const lastButtons = buttonsReversed.find(button => button.slot === 'end') || buttonsReversed.find(button => button.slot === 'primary') || buttonsReversed.find(button => button.slot === 'secondary');\n    if (lastButtons) {\n      lastButtons.classList.add('buttons-last-slot');\n    }\n  }\n  childrenStyle(ev) {\n    ev.stopPropagation();\n    const tagName = ev.target.tagName;\n    const updatedStyles = ev.detail;\n    const newStyles = {};\n    const childStyles = this.childrenStyles.get(tagName) || {};\n    let hasStyleChange = false;\n    Object.keys(updatedStyles).forEach(key => {\n      const childKey = `toolbar-${key}`;\n      const newValue = updatedStyles[key];\n      if (newValue !== childStyles[childKey]) {\n        hasStyleChange = true;\n      }\n      if (newValue) {\n        newStyles[childKey] = true;\n      }\n    });\n    if (hasStyleChange) {\n      this.childrenStyles.set(tagName, newStyles);\n      forceUpdate(this);\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const childStyles = {};\n    this.childrenStyles.forEach(value => {\n      Object.assign(childStyles, value);\n    });\n    return h(Host, {\n      key: 'f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46',\n      class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el)\n      }))\n    }, h(\"div\", {\n      key: '9c81742ffa02de9ba7417025b077d05e67305074',\n      class: \"toolbar-background\",\n      part: \"background\"\n    }), h(\"div\", {\n      key: '5fc96d166fa47894a062e41541a9beee38078a36',\n      class: \"toolbar-container\",\n      part: \"container\"\n    }, h(\"slot\", {\n      key: 'b62c0d9d59a70176bdbf769aec6090d7a166853b',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: 'd01d3cc2c50e5aaa49c345b209fe8dbdf3d48131',\n      name: \"secondary\"\n    }), h(\"div\", {\n      key: '3aaa3a2810aedd38c37eb616158ec7b9638528fc',\n      class: \"toolbar-content\",\n      part: \"content\"\n    }, h(\"slot\", {\n      key: '357246690f8d5e1cc3ca369611d4845a79edf610'\n    })), h(\"slot\", {\n      key: '06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75',\n      name: \"primary\"\n    }), h(\"slot\", {\n      key: 'e453d43d14a26b0d72f41e1b81a554bab8ece811',\n      name: \"end\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nToolbar.style = {\n  ios: toolbarIosCss,\n  md: toolbarMdCss\n};\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };", "map": {"version": 3, "names": ["r", "registerInstance", "e", "getIonMode", "h", "l", "config", "j", "Host", "k", "getElement", "a", "isPlatform", "m", "printIonWarning", "d", "createEvent", "f", "readTask", "n", "forceUpdate", "w", "writeTask", "o", "printIonError", "shouldUseCloseWatcher", "i", "inheritAriaAttributes", "hasLazyBuild", "c", "componentOnReady", "clamp", "s", "shallowEqualStringMap", "isRTL", "createColorClasses", "hostContext", "find<PERSON><PERSON><PERSON><PERSON>nt", "p", "printIonContentErrorMsg", "g", "getScrollElement", "createKeyboardController", "getTimeGivenProgression", "attachComponent", "detachComponent", "createLockController", "t", "transition", "appCss", "App", "constructor", "hostRef", "componentDidLoad", "_this", "rIC", "_asyncToGenerator", "isHybrid", "window", "getBoolean", "then", "module", "startTapClick", "startStatusTap", "needInputShims", "platform", "startInputShims", "hardwareBackButtonModule", "supportsHardwareBackButtonEvents", "startHardwareBackButton", "blockHardwareBackButton", "startKeyboardAssist", "focusVisible", "startFocusVisible", "setFocus", "elements", "_this2", "render", "mode", "key", "class", "el", "needsShimsIOS", "isAndroid<PERSON><PERSON><PERSON>eb", "callback", "requestIdleCallback", "setTimeout", "style", "buttonsIosCss", "buttonsMdCss", "Buttons", "collapse", "ios", "md", "contentCss", "Content", "ionScrollStart", "ionScroll", "ionScrollEnd", "watchDog", "isScrolling", "lastScroll", "queued", "cTop", "cBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resizeTimeout", "inheritedAttributes", "tabsElement", "detail", "scrollTop", "scrollLeft", "type", "event", "undefined", "startX", "startY", "startTime", "currentX", "currentY", "velocityX", "velocityY", "deltaX", "deltaY", "currentTime", "data", "fullscreen", "fixedSlotPlacement", "scrollX", "scrollY", "scrollEvents", "componentWillLoad", "connectedCallback", "closest", "closestTabs", "tabsLoadCallback", "resize", "addEventListener", "disconnectedCallback", "onScrollEnd", "removeEventListener", "onResize", "clearTimeout", "offsetParent", "shouldForceOverscroll", "forceOverscroll", "readDimensions", "page", "getPageElement", "top", "Math", "max", "offsetTop", "bottom", "offsetHeight", "dirty", "onScroll", "ev", "timeStamp", "Date", "now", "shouldStart", "onScrollStart", "ts", "updateScrollDetail", "scrollEl", "emit", "_this3", "Promise", "resolve", "getBackgroundElement", "_this4", "backgroundContentEl", "scrollToTop", "duration", "scrollToPoint", "scrollToBottom", "_this5", "y", "scrollHeight", "clientHeight", "apply", "arguments", "scrollByPoint", "x", "_this6", "_x", "_x2", "_this7", "promise", "fromY", "fromX", "step", "linearTime", "min", "easedT", "pow", "floor", "requestAnimationFrame", "clearInterval", "setInterval", "rtl", "transitionShadow", "Object", "assign", "role", "color", "overscroll", "ref", "id", "part", "name", "getParentElement", "_a", "parentElement", "parentNode", "host", "tabs", "timestamp", "prevX", "prevY", "prevT", "<PERSON><PERSON><PERSON><PERSON>", "handleFooterFade", "baseEl", "maxScroll", "fadeDuration", "fadeStart", "distanceToStart", "scale", "setProperty", "toString", "footerIosCss", "footerMdCss", "Footer", "_this8", "keyboardCtrl", "keyboardVisible", "translucent", "checkCollap<PERSON><PERSON><PERSON>er", "hasFade", "destroyCollapsibleFooter", "pageEl", "contentEl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "contentScrollCallback", "_x3", "componentDidUpdate", "_this9", "_ref3", "keyboardOpen", "waitForResize", "_x4", "_x5", "destroy", "tabBar", "querySelector", "slot", "TRANSITION", "cloneElement", "tagName", "getCachedEl", "document", "clonedEl", "createElement", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "createHeaderIndex", "headerEl", "toolbars", "querySelectorAll", "Array", "from", "map", "toolbar", "ionTitleEl", "background", "shadowRoot", "innerTitleEl", "ionButtonsEl", "handleContentScroll", "scrollHeaderIndex", "nativeRefresher", "scaleLargeTitles", "setToolbarBackgroundOpacity", "opacity", "removeProperty", "handleToolbarBorderIntersection", "mainHeaderIndex", "isIntersecting", "intersectionRatio", "handleToolbarIntersection", "intersection", "intersectionRect", "intersectionArea", "width", "height", "rootArea", "rootBounds", "isPageHidden", "leftDiff", "abs", "left", "boundingClientRect", "rightDiff", "right", "isPageTransitioning", "setHeaderActive", "hasValidIntersection", "headerIndex", "active", "ionTitles", "remove", "for<PERSON>ach", "ionTitle", "removeAttribute", "setAttribute", "titleDiv", "size", "transform", "handleHeaderFade", "condense<PERSON><PERSON>er", "baseElHeight", "headerIosCss", "headerMdCss", "Header", "_this0", "setupFadeHeader", "_ref4", "_x6", "_x7", "checkCollapsible<PERSON><PERSON>er", "destroyCollapsibleHeader", "_this1", "hasCondense", "title", "setupCondenseHeader", "intersectionObserver", "disconnect", "collapsibleMainHeader", "_this10", "IntersectionObserver", "headers", "find", "header", "toolbarIntersection", "root", "threshold", "observe", "length", "roleType", "routerOutletCss", "RouterOutlet", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "lockController", "gestureOrAnimationInProgress", "animated", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesture", "enable", "swi<PERSON><PERSON><PERSON><PERSON>", "_this11", "onStart", "createSwipeBackGesture", "canStart", "ani", "progressStep", "shouldComplete", "dur", "onFinish", "onEnd", "oneTimeCallback", "newStepValue", "easing", "progressEnd", "commit", "enteringEl", "leavingEl", "opts", "_this12", "unlock", "lock", "changed", "setRouteId", "params", "direction", "animation", "_this13", "setRoot", "animationBuilder", "element", "activeEl", "getRouteId", "_this14", "activeParams", "component", "_this15", "activeComponent", "delegate", "_x8", "_x9", "_this16", "get", "deepWait", "progressCallback", "progressAnimation", "watchers", "titleIosCss", "titleMdCss", "ToolbarTitle", "ionStyle", "sizeChanged", "emitStyle", "getSize", "dir", "toolbarIosCss", "toolbarMdCss", "<PERSON><PERSON><PERSON>", "childrenStyles", "Map", "buttons", "firstButtons", "button", "buttonsReversed", "reverse", "lastButtons", "childrenStyle", "stopPropagation", "target", "updatedStyles", "newStyles", "childStyles", "hasStyleChange", "keys", "<PERSON><PERSON><PERSON>", "newValue", "set", "value", "ion_app", "ion_buttons", "ion_content", "ion_footer", "ion_header", "ion_router_outlet", "ion_title", "ion_toolbar"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/ion-app_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, l as config, j as Host, k as getElement, a as isPlatform, m as printIonWarning, d as createEvent, f as readTask, n as forceUpdate, w as writeTask, o as printIonError } from './index-B_U9CtaY.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { i as inheritAriaAttributes, h as hasLazyBuild, c as componentOnReady, e as clamp, s as shallowEqualStringMap } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { c as createKeyboardController } from './keyboard-controller-BaaVITYt.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { t as transition } from './index-DfBA5ztX.js';\nimport './index-ZjP4CjeZ.js';\nimport './keyboard-CUw4ekVy.js';\nimport './capacitor-CFERIeaU.js';\n\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\n\nconst App = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    componentDidLoad() {\n        {\n            rIC(async () => {\n                const isHybrid = isPlatform(window, 'hybrid');\n                if (!config.getBoolean('_testing')) {\n                    import('./index-CWbP1eJz.js').then((module) => module.startTapClick(config));\n                }\n                if (config.getBoolean('statusTap', isHybrid)) {\n                    import('./status-tap-7t9T91bG.js').then((module) => module.startStatusTap());\n                }\n                if (config.getBoolean('inputShims', needInputShims())) {\n                    /**\n                     * needInputShims() ensures that only iOS and Android\n                     * platforms proceed into this block.\n                     */\n                    const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n                    import('./input-shims-C3lNp93k.js').then((module) => module.startInputShims(config, platform));\n                }\n                const hardwareBackButtonModule = await import('./hardware-back-button-DcH0BbDp.js');\n                const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n                if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n                    hardwareBackButtonModule.startHardwareBackButton();\n                }\n                else {\n                    /**\n                     * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n                     * then the close watcher will not be used.\n                     */\n                    if (shouldUseCloseWatcher()) {\n                        printIonWarning('[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n                    }\n                    hardwareBackButtonModule.blockHardwareBackButton();\n                }\n                if (typeof window !== 'undefined') {\n                    import('./keyboard-ywgs5efA.js').then((module) => module.startKeyboardAssist(window));\n                }\n                import('./focus-visible-BmVRXR1y.js').then((module) => (this.focusVisible = module.startFocusVisible()));\n            });\n        }\n    }\n    /**\n     * Used to set focus on an element that uses `ion-focusable`.\n     * Do not use this if focusing the element as a result of a keyboard\n     * event as the focus utility should handle this for us. This method\n     * should be used when we want to programmatically focus an element as\n     * a result of another user action. (Ex: We focus the first element\n     * inside of a popover when the user presents it, but the popover is not always\n     * presented as a result of keyboard action.)\n     *\n     * @param elements An array of HTML elements to set focus on.\n     */\n    async setFocus(elements) {\n        if (this.focusVisible) {\n            this.focusVisible.setFocus(elements);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '9be440c65819e4fa67c2c3c6477ab40b3ad3eed3', class: {\n                [mode]: true,\n                'ion-page': true,\n                'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding'),\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nconst needInputShims = () => {\n    /**\n     * iOS always needs input shims\n     */\n    const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n    if (needsShimsIOS) {\n        return true;\n    }\n    /**\n     * Android only needs input shims when running\n     * in the browser and only if the browser is using the\n     * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n     */\n    const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n    if (isAndroidMobileWeb) {\n        return true;\n    }\n    return false;\n};\nconst rIC = (callback) => {\n    if ('requestIdleCallback' in window) {\n        window.requestIdleCallback(callback);\n    }\n    else {\n        setTimeout(callback, 32);\n    }\n};\nApp.style = appCss;\n\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\n\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\n\nconst Buttons = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If true, buttons will disappear when its\n         * parent toolbar has fully collapsed if the toolbar\n         * is not the first toolbar. If the toolbar is the\n         * first toolbar, the buttons will be hidden and will\n         * only be shown once all toolbars have fully collapsed.\n         *\n         * Only applies in `ios` mode with `collapse` set to\n         * `true` on `ion-header`.\n         *\n         * Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)\n         */\n        this.collapse = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b', class: {\n                [mode]: true,\n                ['buttons-collapse']: this.collapse,\n            } }, h(\"slot\", { key: '0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8' })));\n    }\n};\nButtons.style = {\n    ios: buttonsIosCss,\n    md: buttonsMdCss\n};\n\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\n\nconst Content = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n        this.ionScroll = createEvent(this, \"ionScroll\", 7);\n        this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n        this.watchDog = null;\n        this.isScrolling = false;\n        this.lastScroll = 0;\n        this.queued = false;\n        this.cTop = -1;\n        this.cBottom = -1;\n        this.isMainContent = true;\n        this.resizeTimeout = null;\n        this.inheritedAttributes = {};\n        this.tabsElement = null;\n        // Detail is used in a hot loop in the scroll event, by allocating it here\n        // V8 will be able to inline any read/write to it since it's a monomorphic class.\n        // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n        this.detail = {\n            scrollTop: 0,\n            scrollLeft: 0,\n            type: 'scroll',\n            event: undefined,\n            startX: 0,\n            startY: 0,\n            startTime: 0,\n            currentX: 0,\n            currentY: 0,\n            velocityX: 0,\n            velocityY: 0,\n            deltaX: 0,\n            deltaY: 0,\n            currentTime: 0,\n            data: undefined,\n            isScrolling: true,\n        };\n        /**\n         * If `true`, the content will scroll behind the headers\n         * and footers. This effect can easily be seen by setting the toolbar\n         * to transparent.\n         */\n        this.fullscreen = false;\n        /**\n         * Controls where the fixed content is placed relative to the main content\n         * in the DOM. This can be used to control the order in which fixed elements\n         * receive keyboard focus.\n         * For example, if a FAB in the fixed slot should receive keyboard focus before\n         * the main page content, set this property to `'before'`.\n         */\n        this.fixedSlotPlacement = 'after';\n        /**\n         * If you want to enable the content scrolling in the X axis, set this property to `true`.\n         */\n        this.scrollX = false;\n        /**\n         * If you want to disable the content scrolling in the Y axis, set this property to `false`.\n         */\n        this.scrollY = true;\n        /**\n         * Because of performance reasons, ionScroll events are disabled by default, in order to enable them\n         * and start listening from (ionScroll), set this property to `true`.\n         */\n        this.scrollEvents = false;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    connectedCallback() {\n        this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n        /**\n         * The fullscreen content offsets need to be\n         * computed after the tab bar has loaded. Since\n         * lazy evaluation means components are not hydrated\n         * at the same time, we need to wait for the ionTabBarLoaded\n         * event to fire. This does not impact dist-custom-elements\n         * because there is no hydration there.\n         */\n        if (hasLazyBuild(this.el)) {\n            /**\n             * We need to cache the reference to the tabs.\n             * If just the content is unmounted then we won't\n             * be able to query for the closest tabs on disconnectedCallback\n             * since the content has been removed from the DOM tree.\n             */\n            const closestTabs = (this.tabsElement = this.el.closest('ion-tabs'));\n            if (closestTabs !== null) {\n                /**\n                 * When adding and removing the event listener\n                 * we need to make sure we pass the same function reference\n                 * otherwise the event listener will not be removed properly.\n                 * We can't only pass `this.resize` because \"this\" in the function\n                 * context becomes a reference to IonTabs instead of IonContent.\n                 *\n                 * Additionally, we listen for ionTabBarLoaded on the IonTabs\n                 * instance rather than the IonTabBar instance. It's possible for\n                 * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n                 * bubbles, we can catch any instances of child tab bars loading by listening\n                 * on IonTabs.\n                 */\n                this.tabsLoadCallback = () => this.resize();\n                closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n            }\n        }\n    }\n    disconnectedCallback() {\n        this.onScrollEnd();\n        if (hasLazyBuild(this.el)) {\n            /**\n             * The event listener and tabs caches need to\n             * be cleared otherwise this will create a memory\n             * leak where the IonTabs instance can never be\n             * garbage collected.\n             */\n            const { tabsElement, tabsLoadCallback } = this;\n            if (tabsElement !== null && tabsLoadCallback !== undefined) {\n                tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n            }\n            this.tabsElement = null;\n            this.tabsLoadCallback = undefined;\n        }\n    }\n    /**\n     * Rotating certain devices can update\n     * the safe area insets. As a result,\n     * the fullscreen feature on ion-content\n     * needs to be recalculated.\n     *\n     * We listen for \"resize\" because we\n     * do not care what the orientation of\n     * the device is. Other APIs\n     * such as ScreenOrientation or\n     * the deviceorientation event must have\n     * permission from the user first whereas\n     * the \"resize\" event does not.\n     *\n     * We also throttle the callback to minimize\n     * thrashing when quickly resizing a window.\n     */\n    onResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            this.resizeTimeout = null;\n        }\n        this.resizeTimeout = setTimeout(() => {\n            /**\n             * Resize should only happen\n             * if the content is visible.\n             * When the content is hidden\n             * then offsetParent will be null.\n             */\n            if (this.el.offsetParent === null) {\n                return;\n            }\n            this.resize();\n        }, 100);\n    }\n    shouldForceOverscroll() {\n        const { forceOverscroll } = this;\n        const mode = getIonMode(this);\n        return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n    }\n    resize() {\n        /**\n         * Only force update if the component is rendered in a browser context.\n         * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n         * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n         * `forceUpdate` will trigger another render, locking up the server.\n         *\n         * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n         */\n        {\n            if (this.fullscreen) {\n                readTask(() => this.readDimensions());\n            }\n            else if (this.cTop !== 0 || this.cBottom !== 0) {\n                this.cTop = this.cBottom = 0;\n                forceUpdate(this);\n            }\n        }\n    }\n    readDimensions() {\n        const page = getPageElement(this.el);\n        const top = Math.max(this.el.offsetTop, 0);\n        const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n        const dirty = top !== this.cTop || bottom !== this.cBottom;\n        if (dirty) {\n            this.cTop = top;\n            this.cBottom = bottom;\n            forceUpdate(this);\n        }\n    }\n    onScroll(ev) {\n        const timeStamp = Date.now();\n        const shouldStart = !this.isScrolling;\n        this.lastScroll = timeStamp;\n        if (shouldStart) {\n            this.onScrollStart();\n        }\n        if (!this.queued && this.scrollEvents) {\n            this.queued = true;\n            readTask((ts) => {\n                this.queued = false;\n                this.detail.event = ev;\n                updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n                this.ionScroll.emit(this.detail);\n            });\n        }\n    }\n    /**\n     * Get the element where the actual scrolling takes place.\n     * This element can be used to subscribe to `scroll` events or manually modify\n     * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n     *\n     * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n     * and `scrollToPoint()` to scroll the content into a certain point.\n     */\n    async getScrollElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.scrollEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.scrollEl);\n    }\n    /**\n     * Returns the background content element.\n     * @internal\n     */\n    async getBackgroundElement() {\n        if (!this.backgroundContentEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.backgroundContentEl);\n    }\n    /**\n     * Scroll to the top of the component.\n     *\n     * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n     */\n    scrollToTop(duration = 0) {\n        return this.scrollToPoint(undefined, 0, duration);\n    }\n    /**\n     * Scroll to the bottom of the component.\n     *\n     * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n     */\n    async scrollToBottom(duration = 0) {\n        const scrollEl = await this.getScrollElement();\n        const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n        return this.scrollToPoint(undefined, y, duration);\n    }\n    /**\n     * Scroll by a specified X/Y distance in the component.\n     *\n     * @param x The amount to scroll by on the horizontal axis.\n     * @param y The amount to scroll by on the vertical axis.\n     * @param duration The amount of time to take scrolling by that amount.\n     */\n    async scrollByPoint(x, y, duration) {\n        const scrollEl = await this.getScrollElement();\n        return this.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n    }\n    /**\n     * Scroll to a specified X/Y location in the component.\n     *\n     * @param x The point to scroll to on the horizontal axis.\n     * @param y The point to scroll to on the vertical axis.\n     * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n     */\n    async scrollToPoint(x, y, duration = 0) {\n        const el = await this.getScrollElement();\n        if (duration < 32) {\n            if (y != null) {\n                el.scrollTop = y;\n            }\n            if (x != null) {\n                el.scrollLeft = x;\n            }\n            return;\n        }\n        let resolve;\n        let startTime = 0;\n        const promise = new Promise((r) => (resolve = r));\n        const fromY = el.scrollTop;\n        const fromX = el.scrollLeft;\n        const deltaY = y != null ? y - fromY : 0;\n        const deltaX = x != null ? x - fromX : 0;\n        // scroll loop\n        const step = (timeStamp) => {\n            const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n            const easedT = Math.pow(linearTime, 3) + 1;\n            if (deltaY !== 0) {\n                el.scrollTop = Math.floor(easedT * deltaY + fromY);\n            }\n            if (deltaX !== 0) {\n                el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n            }\n            if (easedT < 1) {\n                // do not use DomController here\n                // must use nativeRaf in order to fire in the next frame\n                requestAnimationFrame(step);\n            }\n            else {\n                resolve();\n            }\n        };\n        // chill out for a frame first\n        requestAnimationFrame((ts) => {\n            startTime = ts;\n            step(ts);\n        });\n        return promise;\n    }\n    onScrollStart() {\n        this.isScrolling = true;\n        this.ionScrollStart.emit({\n            isScrolling: true,\n        });\n        if (this.watchDog) {\n            clearInterval(this.watchDog);\n        }\n        // watchdog\n        this.watchDog = setInterval(() => {\n            if (this.lastScroll < Date.now() - 120) {\n                this.onScrollEnd();\n            }\n        }, 100);\n    }\n    onScrollEnd() {\n        if (this.watchDog)\n            clearInterval(this.watchDog);\n        this.watchDog = null;\n        if (this.isScrolling) {\n            this.isScrolling = false;\n            this.ionScrollEnd.emit({\n                isScrolling: false,\n            });\n        }\n    }\n    render() {\n        const { fixedSlotPlacement, inheritedAttributes, isMainContent, scrollX, scrollY, el } = this;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const mode = getIonMode(this);\n        const forceOverscroll = this.shouldForceOverscroll();\n        const transitionShadow = mode === 'ios';\n        this.resize();\n        return (h(Host, Object.assign({ key: 'f2a24aa66dbf5c76f9d4b06f708eb73cadc239df', role: isMainContent ? 'main' : undefined, class: createColorClasses(this.color, {\n                [mode]: true,\n                'content-sizing': hostContext('ion-popover', this.el),\n                overscroll: forceOverscroll,\n                [`content-${rtl}`]: true,\n            }), style: {\n                '--offset-top': `${this.cTop}px`,\n                '--offset-bottom': `${this.cBottom}px`,\n            } }, inheritedAttributes), h(\"div\", { key: '6480ca7648b278abb36477b3838bccbcd4995e2a', ref: (el) => (this.backgroundContentEl = el), id: \"background-content\", part: \"background\" }), fixedSlotPlacement === 'before' ? h(\"slot\", { name: \"fixed\" }) : null, h(\"div\", { key: '29a23b663f5f0215bb000820c01e1814c0d55985', class: {\n                'inner-scroll': true,\n                'scroll-x': scrollX,\n                'scroll-y': scrollY,\n                overscroll: (scrollX || scrollY) && forceOverscroll,\n            }, ref: (scrollEl) => (this.scrollEl = scrollEl), onScroll: this.scrollEvents ? (ev) => this.onScroll(ev) : undefined, part: \"scroll\" }, h(\"slot\", { key: '0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0' })), transitionShadow ? (h(\"div\", { class: \"transition-effect\" }, h(\"div\", { class: \"transition-cover\" }), h(\"div\", { class: \"transition-shadow\" }))) : null, fixedSlotPlacement === 'after' ? h(\"slot\", { name: \"fixed\" }) : null));\n    }\n    get el() { return getElement(this); }\n};\nconst getParentElement = (el) => {\n    var _a;\n    if (el.parentElement) {\n        // normal element with a parent element\n        return el.parentElement;\n    }\n    if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n        // shadow dom's document fragment\n        return el.parentNode.host;\n    }\n    return null;\n};\nconst getPageElement = (el) => {\n    const tabs = el.closest('ion-tabs');\n    if (tabs) {\n        return tabs;\n    }\n    /**\n     * If we're in a popover, we need to use its wrapper so we can account for space\n     * between the popover and the edges of the screen. But if the popover contains\n     * its own page element, we should use that instead.\n     */\n    const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n    if (page) {\n        return page;\n    }\n    return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    const currentX = el.scrollLeft;\n    const currentY = el.scrollTop;\n    const timeDelta = timestamp - prevT;\n    if (shouldStart) {\n        // remember the start positions\n        detail.startTime = timestamp;\n        detail.startX = currentX;\n        detail.startY = currentY;\n        detail.velocityX = detail.velocityY = 0;\n    }\n    detail.currentTime = timestamp;\n    detail.currentX = detail.scrollLeft = currentX;\n    detail.currentY = detail.scrollTop = currentY;\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n};\nContent.style = contentCss;\n\nconst handleFooterFade = (scrollEl, baseEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n        /**\n         * Toolbar background will fade\n         * out over fadeDuration in pixels.\n         */\n        const fadeDuration = 10;\n        /**\n         * Begin fading out maxScroll - 30px\n         * from the bottom of the content.\n         * Also determine how close we are\n         * to starting the fade. If we are\n         * before the starting point, the\n         * scale value will get clamped to 0.\n         * If we are after the maxScroll (rubber\n         * band scrolling), the scale value will\n         * get clamped to 1.\n         */\n        const fadeStart = maxScroll - fadeDuration;\n        const distanceToStart = scrollTop - fadeStart;\n        const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\n\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\n\nconst Footer = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.keyboardCtrl = null;\n        this.keyboardVisible = false;\n        /**\n         * If `true`, the footer will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         *\n         * Note: In order to scroll content behind the footer, the `fullscreen`\n         * attribute needs to be set on the content.\n         */\n        this.translucent = false;\n        this.checkCollapsibleFooter = () => {\n            const mode = getIonMode(this);\n            if (mode !== 'ios') {\n                return;\n            }\n            const { collapse } = this;\n            const hasFade = collapse === 'fade';\n            this.destroyCollapsibleFooter();\n            if (hasFade) {\n                const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n                const contentEl = pageEl ? findIonContent(pageEl) : null;\n                if (!contentEl) {\n                    printIonContentErrorMsg(this.el);\n                    return;\n                }\n                this.setupFadeFooter(contentEl);\n            }\n        };\n        this.setupFadeFooter = async (contentEl) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleFooterFade(scrollEl, this.el);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleFooterFade(scrollEl, this.el);\n        };\n    }\n    componentDidLoad() {\n        this.checkCollapsibleFooter();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleFooter();\n    }\n    async connectedCallback() {\n        this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n            /**\n             * If the keyboard is hiding, then we need to wait\n             * for the webview to resize. Otherwise, the footer\n             * will flicker before the webview resizes.\n             */\n            if (keyboardOpen === false && waitForResize !== undefined) {\n                await waitForResize;\n            }\n            this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n        });\n    }\n    disconnectedCallback() {\n        if (this.keyboardCtrl) {\n            this.keyboardCtrl.destroy();\n        }\n    }\n    destroyCollapsibleFooter() {\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n    }\n    render() {\n        const { translucent, collapse } = this;\n        const mode = getIonMode(this);\n        const tabs = this.el.closest('ion-tabs');\n        const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n        return (h(Host, { key: 'ddc228f1a1e7fa4f707dccf74db2490ca3241137', role: \"contentinfo\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`footer-${mode}`]: true,\n                [`footer-translucent`]: translucent,\n                [`footer-translucent-${mode}`]: translucent,\n                ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n                [`footer-collapse-${collapse}`]: collapse !== undefined,\n            } }, mode === 'ios' && translucent && h(\"div\", { key: 'e16ed4963ff94e06de77eb8038201820af73937c', class: \"footer-background\" }), h(\"slot\", { key: 'f186934febf85d37133d9351a96c1a64b0a4b203' })));\n    }\n    get el() { return getElement(this); }\n};\nFooter.style = {\n    ios: footerIosCss,\n    md: footerMdCss\n};\n\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = (tagName) => {\n    const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n    if (getCachedEl !== null) {\n        return getCachedEl;\n    }\n    const clonedEl = document.createElement(tagName);\n    clonedEl.classList.add('ion-cloned-element');\n    clonedEl.style.setProperty('display', 'none');\n    document.body.appendChild(clonedEl);\n    return clonedEl;\n};\nconst createHeaderIndex = (headerEl) => {\n    if (!headerEl) {\n        return;\n    }\n    const toolbars = headerEl.querySelectorAll('ion-toolbar');\n    return {\n        el: headerEl,\n        toolbars: Array.from(toolbars).map((toolbar) => {\n            const ionTitleEl = toolbar.querySelector('ion-title');\n            return {\n                el: toolbar,\n                background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n                ionTitleEl,\n                innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n                ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons')),\n            };\n        }),\n    };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n        // Native refresher should not cause titles to scale\n        const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n        if (nativeRefresher === null) {\n            writeTask(() => {\n                scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n            });\n        }\n    });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n    /**\n     * Fading in the backdrop opacity\n     * should happen after the large title\n     * has collapsed, so it is handled\n     * by handleHeaderFade()\n     */\n    if (headerEl.collapse === 'fade') {\n        return;\n    }\n    if (opacity === undefined) {\n        headerEl.style.removeProperty('--opacity-scale');\n    }\n    else {\n        headerEl.style.setProperty('--opacity-scale', opacity.toString());\n    }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n    if (!ev[0].isIntersecting) {\n        return;\n    }\n    /**\n     * There is a bug in Safari where overflow scrolling on a non-body element\n     * does not always reset the scrollTop position to 0 when letting go. It will\n     * set to 1 once the rubber band effect has ended. This causes the background to\n     * appear slightly on certain app setups.\n     *\n     * Additionally, we check if user is rubber banding (scrolling is negative)\n     * as this can mean they are using pull to refresh. Once the refresher starts,\n     * the content is transformed which can cause the intersection observer to erroneously\n     * fire here as well.\n     */\n    const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : ((1 - ev[0].intersectionRatio) * 100) / 75;\n    setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev, // TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n    writeTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n        const event = ev[0];\n        const intersection = event.intersectionRect;\n        const intersectionArea = intersection.width * intersection.height;\n        const rootArea = event.rootBounds.width * event.rootBounds.height;\n        const isPageHidden = intersectionArea === 0 && rootArea === 0;\n        const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n        const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n        const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n        if (isPageHidden || isPageTransitioning) {\n            return;\n        }\n        if (event.isIntersecting) {\n            setHeaderActive(mainHeaderIndex, false);\n            setHeaderActive(scrollHeaderIndex);\n        }\n        else {\n            /**\n             * There is a bug with IntersectionObserver on Safari\n             * where `event.isIntersecting === false` when cancelling\n             * a swipe to go back gesture. Checking the intersection\n             * x, y, width, and height provides a workaround. This bug\n             * does not happen when using Safari + Web Animations,\n             * only Safari + CSS Animations.\n             */\n            const hasValidIntersection = (intersection.x === 0 && intersection.y === 0) || (intersection.width !== 0 && intersection.height !== 0);\n            if (hasValidIntersection && scrollTop > 0) {\n                setHeaderActive(mainHeaderIndex);\n                setHeaderActive(scrollHeaderIndex, false);\n                setToolbarBackgroundOpacity(mainHeaderIndex.el);\n            }\n        }\n    });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n    const headerEl = headerIndex.el;\n    const toolbars = headerIndex.toolbars;\n    const ionTitles = toolbars.map((toolbar) => toolbar.ionTitleEl);\n    if (active) {\n        headerEl.classList.remove('header-collapse-condense-inactive');\n        ionTitles.forEach((ionTitle) => {\n            if (ionTitle) {\n                ionTitle.removeAttribute('aria-hidden');\n            }\n        });\n    }\n    else {\n        headerEl.classList.add('header-collapse-condense-inactive');\n        /**\n         * The small title should only be accessed by screen readers\n         * when the large title collapses into the small title due\n         * to scrolling.\n         *\n         * Originally, the header was given `aria-hidden=\"true\"`\n         * but this caused issues with screen readers not being\n         * able to access any focusable elements within the header.\n         */\n        ionTitles.forEach((ionTitle) => {\n            if (ionTitle) {\n                ionTitle.setAttribute('aria-hidden', 'true');\n            }\n        });\n    }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n    toolbars.forEach((toolbar) => {\n        const ionTitle = toolbar.ionTitleEl;\n        const titleDiv = toolbar.innerTitleEl;\n        if (!ionTitle || ionTitle.size !== 'large') {\n            return;\n        }\n        titleDiv.style.transition = transition ? TRANSITION : '';\n        titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n    });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const baseElHeight = baseEl.clientHeight;\n        const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n        /**\n         * If we are using fade header with a condense\n         * header, then the toolbar backgrounds should\n         * not begin to fade in until the condense\n         * header has fully collapsed.\n         *\n         * Additionally, the main content should not\n         * overflow out of the container until the\n         * condense header has fully collapsed. When\n         * using just the condense header the content\n         * should overflow out of the container.\n         */\n        if (condenseHeader !== null && scrollTop < fadeStart) {\n            baseEl.style.setProperty('--opacity-scale', '0');\n            scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n            return;\n        }\n        const distanceToStart = scrollTop - fadeStart;\n        const fadeDuration = 10;\n        const scale = clamp(0, distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            scrollEl.style.removeProperty('clip-path');\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\n\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\n\nconst Header = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        /**\n         * If `true`, the header will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         *\n         * Note: In order to scroll content behind the header, the `fullscreen`\n         * attribute needs to be set on the content.\n         */\n        this.translucent = false;\n        this.setupFadeHeader = async (contentEl, condenseHeader) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.checkCollapsibleHeader();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleHeader();\n    }\n    disconnectedCallback() {\n        this.destroyCollapsibleHeader();\n    }\n    async checkCollapsibleHeader() {\n        const mode = getIonMode(this);\n        if (mode !== 'ios') {\n            return;\n        }\n        const { collapse } = this;\n        const hasCondense = collapse === 'condense';\n        const hasFade = collapse === 'fade';\n        this.destroyCollapsibleHeader();\n        if (hasCondense) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            // Cloned elements are always needed in iOS transition\n            writeTask(() => {\n                const title = cloneElement('ion-title');\n                title.size = 'large';\n                cloneElement('ion-back-button');\n            });\n            await this.setupCondenseHeader(contentEl, pageEl);\n        }\n        else if (hasFade) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            if (!contentEl) {\n                printIonContentErrorMsg(this.el);\n                return;\n            }\n            const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n            await this.setupFadeHeader(contentEl, condenseHeader);\n        }\n    }\n    destroyCollapsibleHeader() {\n        if (this.intersectionObserver) {\n            this.intersectionObserver.disconnect();\n            this.intersectionObserver = undefined;\n        }\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n        if (this.collapsibleMainHeader) {\n            this.collapsibleMainHeader.classList.remove('header-collapse-main');\n            this.collapsibleMainHeader = undefined;\n        }\n    }\n    async setupCondenseHeader(contentEl, pageEl) {\n        if (!contentEl || !pageEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        if (typeof IntersectionObserver === 'undefined') {\n            return;\n        }\n        this.scrollEl = await getScrollElement(contentEl);\n        const headers = pageEl.querySelectorAll('ion-header');\n        this.collapsibleMainHeader = Array.from(headers).find((header) => header.collapse !== 'condense');\n        if (!this.collapsibleMainHeader) {\n            return;\n        }\n        const mainHeaderIndex = createHeaderIndex(this.collapsibleMainHeader);\n        const scrollHeaderIndex = createHeaderIndex(this.el);\n        if (!mainHeaderIndex || !scrollHeaderIndex) {\n            return;\n        }\n        setHeaderActive(mainHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n        /**\n         * Handle interaction between toolbar collapse and\n         * showing/hiding content in the primary ion-header\n         * as well as progressively showing/hiding the main header\n         * border as the top-most toolbar collapses or expands.\n         */\n        const toolbarIntersection = (ev) => {\n            handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, this.scrollEl);\n        };\n        this.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n            root: contentEl,\n            threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],\n        });\n        this.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n        /**\n         * Handle scaling of large iOS titles and\n         * showing/hiding border on last toolbar\n         * in primary header\n         */\n        this.contentScrollCallback = () => {\n            handleContentScroll(this.scrollEl, scrollHeaderIndex, contentEl);\n        };\n        this.scrollEl.addEventListener('scroll', this.contentScrollCallback);\n        writeTask(() => {\n            if (this.collapsibleMainHeader !== undefined) {\n                this.collapsibleMainHeader.classList.add('header-collapse-main');\n            }\n        });\n    }\n    render() {\n        const { translucent, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const collapse = this.collapse || 'none';\n        // banner role must be at top level, so remove role if inside a menu\n        const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n        return (h(Host, Object.assign({ key: 'b6cc27f0b08afc9fcc889683525da765d80ba672', role: roleType, class: {\n                [mode]: true,\n                // Used internally for styling\n                [`header-${mode}`]: true,\n                [`header-translucent`]: this.translucent,\n                [`header-collapse-${collapse}`]: true,\n                [`header-translucent-${mode}`]: this.translucent,\n            } }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", { key: '395766d4dcee3398bc91960db21f922095292f14', class: \"header-background\" }), h(\"slot\", { key: '09a67ece27b258ff1248805d43d92a49b2c6859a' })));\n    }\n    get el() { return getElement(this); }\n};\nHeader.style = {\n    ios: headerIosCss,\n    md: headerMdCss\n};\n\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\n\nconst RouterOutlet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.lockController = createLockController();\n        this.gestureOrAnimationInProgress = false;\n        /**\n         * The mode determines which platform styles to use.\n         */\n        this.mode = getIonMode(this);\n        /**\n         * If `true`, the router-outlet should animate the transition of components.\n         */\n        this.animated = true;\n    }\n    swipeHandlerChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeHandler !== undefined);\n        }\n    }\n    async connectedCallback() {\n        const onStart = () => {\n            this.gestureOrAnimationInProgress = true;\n            if (this.swipeHandler) {\n                this.swipeHandler.onStart();\n            }\n        };\n        this.gesture = (await import('./swipe-back-VdaUzLWy.js')).createSwipeBackGesture(this.el, () => !this.gestureOrAnimationInProgress && !!this.swipeHandler && this.swipeHandler.canStart(), () => onStart(), (step) => { var _a; return (_a = this.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step); }, (shouldComplete, step, dur) => {\n            if (this.ani) {\n                this.ani.onFinish(() => {\n                    this.gestureOrAnimationInProgress = false;\n                    if (this.swipeHandler) {\n                        this.swipeHandler.onEnd(shouldComplete);\n                    }\n                }, { oneTimeCallback: true });\n                // Account for rounding errors in JS\n                let newStepValue = shouldComplete ? -1e-3 : 0.001;\n                /**\n                 * Animation will be reversed here, so need to\n                 * reverse the easing curve as well\n                 *\n                 * Additionally, we need to account for the time relative\n                 * to the new easing curve, as `stepValue` is going to be given\n                 * in terms of a linear curve.\n                 */\n                if (!shouldComplete) {\n                    this.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                    newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n                }\n                else {\n                    newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n                }\n                this.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n            }\n            else {\n                this.gestureOrAnimationInProgress = false;\n            }\n        });\n        this.swipeHandlerChanged();\n    }\n    componentWillLoad() {\n        this.ionNavWillLoad.emit();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /** @internal */\n    async commit(enteringEl, leavingEl, opts) {\n        const unlock = await this.lockController.lock();\n        let changed = false;\n        try {\n            changed = await this.transition(enteringEl, leavingEl, opts);\n        }\n        catch (e) {\n            printIonError('[ion-router-outlet] - Exception in commit:', e);\n        }\n        unlock();\n        return changed;\n    }\n    /** @internal */\n    async setRouteId(id, params, direction, animation) {\n        const changed = await this.setRoot(id, params, {\n            duration: direction === 'root' ? 0 : undefined,\n            direction: direction === 'back' ? 'back' : 'forward',\n            animationBuilder: animation,\n        });\n        return {\n            changed,\n            element: this.activeEl,\n        };\n    }\n    /** @internal */\n    async getRouteId() {\n        const active = this.activeEl;\n        return active\n            ? {\n                id: active.tagName,\n                element: active,\n                params: this.activeParams,\n            }\n            : undefined;\n    }\n    async setRoot(component, params, opts) {\n        if (this.activeComponent === component && shallowEqualStringMap(params, this.activeParams)) {\n            return false;\n        }\n        // attach entering view to DOM\n        const leavingEl = this.activeEl;\n        const enteringEl = await attachComponent(this.delegate, this.el, component, ['ion-page', 'ion-page-invisible'], params);\n        this.activeComponent = component;\n        this.activeEl = enteringEl;\n        this.activeParams = params;\n        // commit animation\n        await this.commit(enteringEl, leavingEl, opts);\n        await detachComponent(this.delegate, leavingEl);\n        return true;\n    }\n    async transition(enteringEl, leavingEl, opts = {}) {\n        if (leavingEl === enteringEl) {\n            return false;\n        }\n        // emit nav will change event\n        this.ionNavWillChange.emit();\n        const { el, mode } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const animationBuilder = opts.animationBuilder || this.animation || config.get('navAnimation');\n        await transition(Object.assign(Object.assign({ mode,\n            animated,\n            enteringEl,\n            leavingEl, baseEl: el,\n            /**\n             * We need to wait for all Stencil components\n             * to be ready only when using the lazy\n             * loaded bundle.\n             */\n            deepWait: hasLazyBuild(el), progressCallback: opts.progressAnimation\n                ? (ani) => {\n                    /**\n                     * Because this progress callback is called asynchronously\n                     * it is possible for the gesture to start and end before\n                     * the animation is ever set. In that scenario, we should\n                     * immediately call progressEnd so that the transition promise\n                     * resolves and the gesture does not get locked up.\n                     */\n                    if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                        this.gestureOrAnimationInProgress = true;\n                        ani.onFinish(() => {\n                            this.gestureOrAnimationInProgress = false;\n                            if (this.swipeHandler) {\n                                this.swipeHandler.onEnd(false);\n                            }\n                        }, { oneTimeCallback: true });\n                        /**\n                         * Playing animation to beginning\n                         * with a duration of 0 prevents\n                         * any flickering when the animation\n                         * is later cleaned up.\n                         */\n                        ani.progressEnd(0, 0, 0);\n                    }\n                    else {\n                        this.ani = ani;\n                    }\n                }\n                : undefined }, opts), { animationBuilder }));\n        // emit nav changed event\n        this.ionNavDidChange.emit();\n        return true;\n    }\n    render() {\n        return h(\"slot\", { key: '84b50f1155b0d780dff802ee13223287259fd525' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeHandler\": [\"swipeHandlerChanged\"]\n    }; }\n};\nRouterOutlet.style = routerOutletCss;\n\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\n\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\n\nconst ToolbarTitle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    }\n    sizeChanged() {\n        this.emitStyle();\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const size = this.getSize();\n        this.ionStyle.emit({\n            [`title-${size}`]: true,\n        });\n    }\n    getSize() {\n        return this.size !== undefined ? this.size : 'default';\n    }\n    render() {\n        const mode = getIonMode(this);\n        const size = this.getSize();\n        return (h(Host, { key: 'e599c0bf1b0817df3fa8360bdcd6d787f751c371', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`title-${size}`]: true,\n                'title-rtl': document.dir === 'rtl',\n            }) }, h(\"div\", { key: '6e7eee9047d6759876bb31d7305b76efc7c4338c', class: \"toolbar-title\" }, h(\"slot\", { key: 'bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"size\": [\"sizeChanged\"]\n    }; }\n};\nToolbarTitle.style = {\n    ios: titleIosCss,\n    md: titleMdCss\n};\n\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\n\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\n\nconst Toolbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.childrenStyles = new Map();\n    }\n    componentWillLoad() {\n        const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n        const firstButtons = buttons.find((button) => {\n            return button.slot === 'start';\n        });\n        if (firstButtons) {\n            firstButtons.classList.add('buttons-first-slot');\n        }\n        const buttonsReversed = buttons.reverse();\n        const lastButtons = buttonsReversed.find((button) => button.slot === 'end') ||\n            buttonsReversed.find((button) => button.slot === 'primary') ||\n            buttonsReversed.find((button) => button.slot === 'secondary');\n        if (lastButtons) {\n            lastButtons.classList.add('buttons-last-slot');\n        }\n    }\n    childrenStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.childrenStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            const childKey = `toolbar-${key}`;\n            const newValue = updatedStyles[key];\n            if (newValue !== childStyles[childKey]) {\n                hasStyleChange = true;\n            }\n            if (newValue) {\n                newStyles[childKey] = true;\n            }\n        });\n        if (hasStyleChange) {\n            this.childrenStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const childStyles = {};\n        this.childrenStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        return (h(Host, { key: 'f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46', class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n            })) }, h(\"div\", { key: '9c81742ffa02de9ba7417025b077d05e67305074', class: \"toolbar-background\", part: \"background\" }), h(\"div\", { key: '5fc96d166fa47894a062e41541a9beee38078a36', class: \"toolbar-container\", part: \"container\" }, h(\"slot\", { key: 'b62c0d9d59a70176bdbf769aec6090d7a166853b', name: \"start\" }), h(\"slot\", { key: 'd01d3cc2c50e5aaa49c345b209fe8dbdf3d48131', name: \"secondary\" }), h(\"div\", { key: '3aaa3a2810aedd38c37eb616158ec7b9638528fc', class: \"toolbar-content\", part: \"content\" }, h(\"slot\", { key: '357246690f8d5e1cc3ca369611d4845a79edf610' })), h(\"slot\", { key: '06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75', name: \"primary\" }), h(\"slot\", { key: 'e453d43d14a26b0d72f41e1b81a554bab8ece811', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nToolbar.style = {\n    ios: toolbarIosCss,\n    md: toolbarMdCss\n};\n\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AACtP,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,CAAC,IAAIC,qBAAqB,EAAEvB,CAAC,IAAIwB,YAAY,EAAEC,CAAC,IAAIC,gBAAgB,EAAE5B,CAAC,IAAI6B,KAAK,EAAEC,CAAC,IAAIC,qBAAqB,QAAQ,uBAAuB;AACpJ,SAASP,CAAC,IAAIQ,KAAK,QAAQ,mBAAmB;AAC9C,SAASL,CAAC,IAAIM,kBAAkB,EAAE/B,CAAC,IAAIgC,WAAW,QAAQ,qBAAqB;AAC/E,SAASzB,CAAC,IAAI0B,cAAc,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,qBAAqB;AAC9G,SAASZ,CAAC,IAAIa,wBAAwB,QAAQ,mCAAmC;AACjF,SAASF,CAAC,IAAIG,uBAAuB,QAAQ,4BAA4B;AACzE,SAAShC,CAAC,IAAIiC,eAAe,EAAE7B,CAAC,IAAI8B,eAAe,QAAQ,kCAAkC;AAC7F,SAAShB,CAAC,IAAIiB,oBAAoB,QAAQ,+BAA+B;AACzE,SAASC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AACrD,OAAO,qBAAqB;AAC5B,OAAO,wBAAwB;AAC/B,OAAO,yBAAyB;AAEhC,MAAMC,MAAM,GAAG,uSAAuS;AAEtT,MAAMC,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;EACnC;EACAC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACf;MACIC,GAAG,cAAAC,iBAAA,CAAC,aAAY;QACZ,MAAMC,QAAQ,GAAG7C,UAAU,CAAC8C,MAAM,EAAE,QAAQ,CAAC;QAC7C,IAAI,CAACpD,MAAM,CAACqD,UAAU,CAAC,UAAU,CAAC,EAAE;UAChC,MAAM,CAAC,qBAAqB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,aAAa,CAACxD,MAAM,CAAC,CAAC;QAChF;QACA,IAAIA,MAAM,CAACqD,UAAU,CAAC,WAAW,EAAEF,QAAQ,CAAC,EAAE;UAC1C,MAAM,CAAC,0BAA0B,CAAC,CAACG,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACE,cAAc,CAAC,CAAC,CAAC;QAChF;QACA,IAAIzD,MAAM,CAACqD,UAAU,CAAC,YAAY,EAAEK,cAAc,CAAC,CAAC,CAAC,EAAE;UACnD;AACpB;AACA;AACA;UACoB,MAAMC,QAAQ,GAAGrD,UAAU,CAAC8C,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;UAC9D,MAAM,CAAC,2BAA2B,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACK,eAAe,CAAC5D,MAAM,EAAE2D,QAAQ,CAAC,CAAC;QAClG;QACA,MAAME,wBAAwB,SAAS,MAAM,CAAC,oCAAoC,CAAC;QACnF,MAAMC,gCAAgC,GAAGX,QAAQ,IAAIhC,qBAAqB,CAAC,CAAC;QAC5E,IAAInB,MAAM,CAACqD,UAAU,CAAC,oBAAoB,EAAES,gCAAgC,CAAC,EAAE;UAC3ED,wBAAwB,CAACE,uBAAuB,CAAC,CAAC;QACtD,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB,IAAI5C,qBAAqB,CAAC,CAAC,EAAE;YACzBX,eAAe,CAAC,6KAA6K,CAAC;UAClM;UACAqD,wBAAwB,CAACG,uBAAuB,CAAC,CAAC;QACtD;QACA,IAAI,OAAOZ,MAAM,KAAK,WAAW,EAAE;UAC/B,MAAM,CAAC,wBAAwB,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACU,mBAAmB,CAACb,MAAM,CAAC,CAAC;QACzF;QACA,MAAM,CAAC,6BAA6B,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAMP,KAAI,CAACkB,YAAY,GAAGX,MAAM,CAACY,iBAAiB,CAAC,CAAE,CAAC;MAC5G,CAAC,EAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MACrB,IAAIoB,MAAI,CAACJ,YAAY,EAAE;QACnBI,MAAI,CAACJ,YAAY,CAACE,QAAQ,CAACC,QAAQ,CAAC;MACxC;IAAC;EACL;EACAE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACI,IAAI,EAAE;MAAEuE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,UAAU,EAAE,IAAI;QAChB,yBAAyB,EAAExE,MAAM,CAACqD,UAAU,CAAC,wBAAwB;MACzE;IAAE,CAAC,CAAC;EACZ;EACA,IAAIsB,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAMsD,cAAc,GAAGA,CAAA,KAAM;EACzB;AACJ;AACA;EACI,MAAMkB,aAAa,GAAGtE,UAAU,CAAC8C,MAAM,EAAE,KAAK,CAAC,IAAI9C,UAAU,CAAC8C,MAAM,EAAE,QAAQ,CAAC;EAC/E,IAAIwB,aAAa,EAAE;IACf,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMC,kBAAkB,GAAGvE,UAAU,CAAC8C,MAAM,EAAE,SAAS,CAAC,IAAI9C,UAAU,CAAC8C,MAAM,EAAE,WAAW,CAAC;EAC3F,IAAIyB,kBAAkB,EAAE;IACpB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAM5B,GAAG,GAAI6B,QAAQ,IAAK;EACtB,IAAI,qBAAqB,IAAI1B,MAAM,EAAE;IACjCA,MAAM,CAAC2B,mBAAmB,CAACD,QAAQ,CAAC;EACxC,CAAC,MACI;IACDE,UAAU,CAACF,QAAQ,EAAE,EAAE,CAAC;EAC5B;AACJ,CAAC;AACDlC,GAAG,CAACqC,KAAK,GAAGtC,MAAM;AAElB,MAAMuC,aAAa,GAAG,8pFAA8pF;AAEprF,MAAMC,YAAY,GAAG,y9FAAy9F;AAE9+F,MAAMC,OAAO,GAAG,MAAM;EAClBvC,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACuC,QAAQ,GAAG,KAAK;EACzB;EACAd,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACI,IAAI,EAAE;MAAEuE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,CAAC,kBAAkB,GAAG,IAAI,CAACa;MAC/B;IAAE,CAAC,EAAEvF,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACDW,OAAO,CAACH,KAAK,GAAG;EACZK,GAAG,EAAEJ,aAAa;EAClBK,EAAE,EAAEJ;AACR,CAAC;AAED,MAAMK,UAAU,GAAG,wmFAAwmF;AAE3nF,MAAMC,OAAO,GAAG,MAAM;EAClB5C,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAAC4C,cAAc,GAAGhF,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACiF,SAAS,GAAGjF,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACkF,YAAY,GAAGlF,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAACmF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG;MACVC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAEC,SAAS;MAChBC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,IAAI,EAAEX,SAAS;MACfd,WAAW,EAAE;IACjB,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0B,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,OAAO;IACjC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxB,mBAAmB,GAAGhF,qBAAqB,CAAC,IAAI,CAACsD,EAAE,CAAC;EAC7D;EACAmD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC3B,aAAa,GAAG,IAAI,CAACxB,EAAE,CAACoD,OAAO,CAAC,kCAAkC,CAAC,KAAK,IAAI;IACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIzG,YAAY,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMqD,WAAW,GAAI,IAAI,CAAC1B,WAAW,GAAG,IAAI,CAAC3B,EAAE,CAACoD,OAAO,CAAC,UAAU,CAAE;MACpE,IAAIC,WAAW,KAAK,IAAI,EAAE;QACtB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,CAACC,gBAAgB,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;QAC3CF,WAAW,CAACG,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAACF,gBAAgB,CAAC;MAC1E;IACJ;EACJ;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI/G,YAAY,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAM;QAAE2B,WAAW;QAAE2B;MAAiB,CAAC,GAAG,IAAI;MAC9C,IAAI3B,WAAW,KAAK,IAAI,IAAI2B,gBAAgB,KAAKrB,SAAS,EAAE;QACxDN,WAAW,CAACgC,mBAAmB,CAAC,iBAAiB,EAAEL,gBAAgB,CAAC;MACxE;MACA,IAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC2B,gBAAgB,GAAGrB,SAAS;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2B,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACnC,aAAa,EAAE;MACpBoC,YAAY,CAAC,IAAI,CAACpC,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACA,aAAa,GAAGpB,UAAU,CAAC,MAAM;MAClC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACL,EAAE,CAAC8D,YAAY,KAAK,IAAI,EAAE;QAC/B;MACJ;MACA,IAAI,CAACP,MAAM,CAAC,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;EACX;EACAQ,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEC;IAAgB,CAAC,GAAG,IAAI;IAChC,MAAMnE,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAO8I,eAAe,KAAK/B,SAAS,GAAGpC,IAAI,KAAK,KAAK,IAAIlE,UAAU,CAAC,KAAK,CAAC,GAAGqI,eAAe;EAChG;EACAT,MAAMA,CAAA,EAAG;IACL;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ;MACI,IAAI,IAAI,CAACV,UAAU,EAAE;QACjB5G,QAAQ,CAAC,MAAM,IAAI,CAACgI,cAAc,CAAC,CAAC,CAAC;MACzC,CAAC,MACI,IAAI,IAAI,CAAC3C,IAAI,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACD,IAAI,GAAG,IAAI,CAACC,OAAO,GAAG,CAAC;QAC5BpF,WAAW,CAAC,IAAI,CAAC;MACrB;IACJ;EACJ;EACA8H,cAAcA,CAAA,EAAG;IACb,MAAMC,IAAI,GAAGC,cAAc,CAAC,IAAI,CAACnE,EAAE,CAAC;IACpC,MAAMoE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACtE,EAAE,CAACuE,SAAS,EAAE,CAAC,CAAC;IAC1C,MAAMC,MAAM,GAAGH,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACO,YAAY,GAAGL,GAAG,GAAG,IAAI,CAACpE,EAAE,CAACyE,YAAY,EAAE,CAAC,CAAC;IAC1E,MAAMC,KAAK,GAAGN,GAAG,KAAK,IAAI,CAAC9C,IAAI,IAAIkD,MAAM,KAAK,IAAI,CAACjD,OAAO;IAC1D,IAAImD,KAAK,EAAE;MACP,IAAI,CAACpD,IAAI,GAAG8C,GAAG;MACf,IAAI,CAAC7C,OAAO,GAAGiD,MAAM;MACrBrI,WAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACAwI,QAAQA,CAACC,EAAE,EAAE;IACT,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAC7D,WAAW;IACrC,IAAI,CAACC,UAAU,GAAGyD,SAAS;IAC3B,IAAIG,WAAW,EAAE;MACb,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA,IAAI,CAAC,IAAI,CAAC5D,MAAM,IAAI,IAAI,CAAC4B,YAAY,EAAE;MACnC,IAAI,CAAC5B,MAAM,GAAG,IAAI;MAClBpF,QAAQ,CAAEiJ,EAAE,IAAK;QACb,IAAI,CAAC7D,MAAM,GAAG,KAAK;QACnB,IAAI,CAACO,MAAM,CAACI,KAAK,GAAG4C,EAAE;QACtBO,kBAAkB,CAAC,IAAI,CAACvD,MAAM,EAAE,IAAI,CAACwD,QAAQ,EAAEF,EAAE,EAAEF,WAAW,CAAC;QAC/D,IAAI,CAAChE,SAAS,CAACqE,IAAI,CAAC,IAAI,CAACzD,MAAM,CAAC;MACpC,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUpE,gBAAgBA,CAAA,EAAG;IAAA,IAAA8H,MAAA;IAAA,OAAA/G,iBAAA;MACrB;AACR;AACA;AACA;MACQ,IAAI,CAAC+G,MAAI,CAACF,QAAQ,EAAE;QAChB,MAAM,IAAIG,OAAO,CAAEC,OAAO,IAAK3I,gBAAgB,CAACyI,MAAI,CAACtF,EAAE,EAAEwF,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAACF,QAAQ,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACUK,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnH,iBAAA;MACzB,IAAI,CAACmH,MAAI,CAACC,mBAAmB,EAAE;QAC3B,MAAM,IAAIJ,OAAO,CAAEC,OAAO,IAAK3I,gBAAgB,CAAC6I,MAAI,CAAC1F,EAAE,EAAEwF,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACE,MAAI,CAACC,mBAAmB,CAAC;IAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,GAAG,CAAC,EAAE;IACtB,OAAO,IAAI,CAACC,aAAa,CAAC7D,SAAS,EAAE,CAAC,EAAE4D,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACUE,cAAcA,CAAA,EAAe;IAAA,IAAAC,MAAA;IAAA,OAAAzH,iBAAA,YAAdsH,QAAQ,GAAG,CAAC;MAC7B,MAAMT,QAAQ,SAASY,MAAI,CAACxI,gBAAgB,CAAC,CAAC;MAC9C,MAAMyI,CAAC,GAAGb,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;MACvD,OAAOH,MAAI,CAACF,aAAa,CAAC7D,SAAS,EAAEgE,CAAC,EAAEJ,QAAQ,CAAC;IAAC,GAAAO,KAAA,OAAAC,SAAA;EACtD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,aAAaA,CAACC,CAAC,EAAEN,CAAC,EAAEJ,QAAQ,EAAE;IAAA,IAAAW,MAAA;IAAA,OAAAjI,iBAAA;MAChC,MAAM6G,QAAQ,SAASoB,MAAI,CAAChJ,gBAAgB,CAAC,CAAC;MAC9C,OAAOgJ,MAAI,CAACV,aAAa,CAACS,CAAC,GAAGnB,QAAQ,CAACtD,UAAU,EAAEmE,CAAC,GAAGb,QAAQ,CAACvD,SAAS,EAAEgE,QAAQ,CAAC;IAAC;EACzF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,aAAaA,CAAAW,EAAA,EAAAC,GAAA,EAAqB;IAAA,IAAAC,MAAA;IAAA,OAAApI,iBAAA,YAApBgI,CAAC,EAAEN,CAAC,EAAEJ,QAAQ,GAAG,CAAC;MAClC,MAAM7F,EAAE,SAAS2G,MAAI,CAACnJ,gBAAgB,CAAC,CAAC;MACxC,IAAIqI,QAAQ,GAAG,EAAE,EAAE;QACf,IAAII,CAAC,IAAI,IAAI,EAAE;UACXjG,EAAE,CAAC6B,SAAS,GAAGoE,CAAC;QACpB;QACA,IAAIM,CAAC,IAAI,IAAI,EAAE;UACXvG,EAAE,CAAC8B,UAAU,GAAGyE,CAAC;QACrB;QACA;MACJ;MACA,IAAIf,OAAO;MACX,IAAIpD,SAAS,GAAG,CAAC;MACjB,MAAMwE,OAAO,GAAG,IAAIrB,OAAO,CAAExK,CAAC,IAAMyK,OAAO,GAAGzK,CAAE,CAAC;MACjD,MAAM8L,KAAK,GAAG7G,EAAE,CAAC6B,SAAS;MAC1B,MAAMiF,KAAK,GAAG9G,EAAE,CAAC8B,UAAU;MAC3B,MAAMY,MAAM,GAAGuD,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGY,KAAK,GAAG,CAAC;MACxC,MAAMpE,MAAM,GAAG8D,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGO,KAAK,GAAG,CAAC;MACxC;MACA,MAAMC,IAAI,GAAIlC,SAAS,IAAK;QACxB,MAAMmC,UAAU,GAAG3C,IAAI,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAACpC,SAAS,GAAGzC,SAAS,IAAIyD,QAAQ,CAAC,GAAG,CAAC;QACtE,MAAMqB,MAAM,GAAG7C,IAAI,CAAC8C,GAAG,CAACH,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC;QAC1C,IAAItE,MAAM,KAAK,CAAC,EAAE;UACd1C,EAAE,CAAC6B,SAAS,GAAGwC,IAAI,CAAC+C,KAAK,CAACF,MAAM,GAAGxE,MAAM,GAAGmE,KAAK,CAAC;QACtD;QACA,IAAIpE,MAAM,KAAK,CAAC,EAAE;UACdzC,EAAE,CAAC8B,UAAU,GAAGuC,IAAI,CAAC+C,KAAK,CAACF,MAAM,GAAGzE,MAAM,GAAGqE,KAAK,CAAC;QACvD;QACA,IAAII,MAAM,GAAG,CAAC,EAAE;UACZ;UACA;UACAG,qBAAqB,CAACN,IAAI,CAAC;QAC/B,CAAC,MACI;UACDvB,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MACD;MACA6B,qBAAqB,CAAEnC,EAAE,IAAK;QAC1B9C,SAAS,GAAG8C,EAAE;QACd6B,IAAI,CAAC7B,EAAE,CAAC;MACZ,CAAC,CAAC;MACF,OAAO0B,OAAO;IAAC,GAAAR,KAAA,OAAAC,SAAA;EACnB;EACApB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC9D,WAAW,GAAG,IAAI;IACvB,IAAI,CAACJ,cAAc,CAACsE,IAAI,CAAC;MACrBlE,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,IAAI,CAACD,QAAQ,EAAE;MACfoG,aAAa,CAAC,IAAI,CAACpG,QAAQ,CAAC;IAChC;IACA;IACA,IAAI,CAACA,QAAQ,GAAGqG,WAAW,CAAC,MAAM;MAC9B,IAAI,IAAI,CAACnG,UAAU,GAAG0D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;QACpC,IAAI,CAACrB,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAA,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxC,QAAQ,EACboG,aAAa,CAAC,IAAI,CAACpG,QAAQ,CAAC;IAChC,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,KAAK;MACxB,IAAI,CAACF,YAAY,CAACoE,IAAI,CAAC;QACnBlE,WAAW,EAAE;MACjB,CAAC,CAAC;IACN;EACJ;EACAvB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEkD,kBAAkB;MAAEpB,mBAAmB;MAAEF,aAAa;MAAEuB,OAAO;MAAEC,OAAO;MAAEhD;IAAG,CAAC,GAAG,IAAI;IAC7F,MAAMwH,GAAG,GAAGvK,KAAK,CAAC+C,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMH,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8I,eAAe,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;IACpD,MAAM0D,gBAAgB,GAAG5H,IAAI,KAAK,KAAK;IACvC,IAAI,CAAC0D,MAAM,CAAC,CAAC;IACb,OAAQpI,CAAC,CAACI,IAAI,EAAEmM,MAAM,CAACC,MAAM,CAAC;MAAE7H,GAAG,EAAE,0CAA0C;MAAE8H,IAAI,EAAEpG,aAAa,GAAG,MAAM,GAAGS,SAAS;MAAElC,KAAK,EAAE7C,kBAAkB,CAAC,IAAI,CAAC2K,KAAK,EAAE;QACzJ,CAAChI,IAAI,GAAG,IAAI;QACZ,gBAAgB,EAAE1C,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE,CAAC;QACrD8H,UAAU,EAAE9D,eAAe;QAC3B,CAAC,WAAWwD,GAAG,EAAE,GAAG;MACxB,CAAC,CAAC;MAAElH,KAAK,EAAE;QACP,cAAc,EAAE,GAAG,IAAI,CAACgB,IAAI,IAAI;QAChC,iBAAiB,EAAE,GAAG,IAAI,CAACC,OAAO;MACtC;IAAE,CAAC,EAAEG,mBAAmB,CAAC,EAAEvG,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEiI,GAAG,EAAG/H,EAAE,IAAM,IAAI,CAAC2F,mBAAmB,GAAG3F,EAAG;MAAEgI,EAAE,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAC,EAAEnF,kBAAkB,KAAK,QAAQ,GAAG3H,CAAC,CAAC,MAAM,EAAE;MAAE+M,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG,IAAI,EAAE/M,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAC5T,cAAc,EAAE,IAAI;QACpB,UAAU,EAAEgD,OAAO;QACnB,UAAU,EAAEC,OAAO;QACnB8E,UAAU,EAAE,CAAC/E,OAAO,IAAIC,OAAO,KAAKgB;MACxC,CAAC;MAAE+D,GAAG,EAAG3C,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA,QAAS;MAAET,QAAQ,EAAE,IAAI,CAAC1B,YAAY,GAAI2B,EAAE,IAAK,IAAI,CAACD,QAAQ,CAACC,EAAE,CAAC,GAAG3C,SAAS;MAAEgG,IAAI,EAAE;IAAS,CAAC,EAAE9M,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE2H,gBAAgB,GAAItM,CAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAoB,CAAC,EAAE5E,CAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAmB,CAAC,CAAC,EAAE5E,CAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAoB,CAAC,CAAC,CAAC,GAAI,IAAI,EAAE+C,kBAAkB,KAAK,OAAO,GAAG3H,CAAC,CAAC,MAAM,EAAE;MAAE+M,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;EAChb;EACA,IAAIlI,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAM0M,gBAAgB,GAAInI,EAAE,IAAK;EAC7B,IAAIoI,EAAE;EACN,IAAIpI,EAAE,CAACqI,aAAa,EAAE;IAClB;IACA,OAAOrI,EAAE,CAACqI,aAAa;EAC3B;EACA,IAAI,CAACD,EAAE,GAAGpI,EAAE,CAACsI,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,EAAE;IACnE;IACA,OAAOvI,EAAE,CAACsI,UAAU,CAACC,IAAI;EAC7B;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMpE,cAAc,GAAInE,EAAE,IAAK;EAC3B,MAAMwI,IAAI,GAAGxI,EAAE,CAACoD,OAAO,CAAC,UAAU,CAAC;EACnC,IAAIoF,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMtE,IAAI,GAAGlE,EAAE,CAACoD,OAAO,CAAC,4DAA4D,CAAC;EACrF,IAAIc,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA,OAAOiE,gBAAgB,CAACnI,EAAE,CAAC;AAC/B,CAAC;AACD;AACA,MAAMmF,kBAAkB,GAAGA,CAACvD,MAAM,EAAE5B,EAAE,EAAEyI,SAAS,EAAEzD,WAAW,KAAK;EAC/D,MAAM0D,KAAK,GAAG9G,MAAM,CAACS,QAAQ;EAC7B,MAAMsG,KAAK,GAAG/G,MAAM,CAACU,QAAQ;EAC7B,MAAMsG,KAAK,GAAGhH,MAAM,CAACe,WAAW;EAChC,MAAMN,QAAQ,GAAGrC,EAAE,CAAC8B,UAAU;EAC9B,MAAMQ,QAAQ,GAAGtC,EAAE,CAAC6B,SAAS;EAC7B,MAAMgH,SAAS,GAAGJ,SAAS,GAAGG,KAAK;EACnC,IAAI5D,WAAW,EAAE;IACb;IACApD,MAAM,CAACQ,SAAS,GAAGqG,SAAS;IAC5B7G,MAAM,CAACM,MAAM,GAAGG,QAAQ;IACxBT,MAAM,CAACO,MAAM,GAAGG,QAAQ;IACxBV,MAAM,CAACW,SAAS,GAAGX,MAAM,CAACY,SAAS,GAAG,CAAC;EAC3C;EACAZ,MAAM,CAACe,WAAW,GAAG8F,SAAS;EAC9B7G,MAAM,CAACS,QAAQ,GAAGT,MAAM,CAACE,UAAU,GAAGO,QAAQ;EAC9CT,MAAM,CAACU,QAAQ,GAAGV,MAAM,CAACC,SAAS,GAAGS,QAAQ;EAC7CV,MAAM,CAACa,MAAM,GAAGJ,QAAQ,GAAGT,MAAM,CAACM,MAAM;EACxCN,MAAM,CAACc,MAAM,GAAGJ,QAAQ,GAAGV,MAAM,CAACO,MAAM;EACxC,IAAI0G,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,GAAG,EAAE;IAClC,MAAMtG,SAAS,GAAG,CAACF,QAAQ,GAAGqG,KAAK,IAAIG,SAAS;IAChD,MAAMrG,SAAS,GAAG,CAACF,QAAQ,GAAGqG,KAAK,IAAIE,SAAS;IAChDjH,MAAM,CAACW,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGX,MAAM,CAACW,SAAS,GAAG,GAAG;IAC3DX,MAAM,CAACY,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGZ,MAAM,CAACY,SAAS,GAAG,GAAG;EAC/D;AACJ,CAAC;AACD1B,OAAO,CAACR,KAAK,GAAGO,UAAU;AAE1B,MAAMiI,gBAAgB,GAAGA,CAAC1D,QAAQ,EAAE2D,MAAM,KAAK;EAC3C9M,QAAQ,CAAC,MAAM;IACX,MAAM4F,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAMmH,SAAS,GAAG5D,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;IAC/D;AACR;AACA;AACA;IACQ,MAAM8C,YAAY,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,SAAS,GAAGF,SAAS,GAAGC,YAAY;IAC1C,MAAME,eAAe,GAAGtH,SAAS,GAAGqH,SAAS;IAC7C,MAAME,KAAK,GAAGtM,KAAK,CAAC,CAAC,EAAE,CAAC,GAAGqM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IAC7D5M,SAAS,CAAC,MAAM;MACZ0M,MAAM,CAACzI,KAAK,CAAC+I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,YAAY,GAAG,kqBAAkqB;AAEvrB,MAAMC,WAAW,GAAG,yfAAyf;AAE7gB,MAAMC,MAAM,GAAG,MAAM;EACjBvL,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAuL,MAAA;IACjB1O,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACwL,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,MAAMjK,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI2E,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEa;MAAS,CAAC,GAAG,IAAI;MACzB,MAAMqJ,OAAO,GAAGrJ,QAAQ,KAAK,MAAM;MACnC,IAAI,CAACsJ,wBAAwB,CAAC,CAAC;MAC/B,IAAID,OAAO,EAAE;QACT,MAAME,MAAM,GAAG,IAAI,CAACjK,EAAE,CAACoD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM8G,SAAS,GAAGD,MAAM,GAAG7M,cAAc,CAAC6M,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZ5M,uBAAuB,CAAC,IAAI,CAAC0C,EAAE,CAAC;UAChC;QACJ;QACA,IAAI,CAACmK,eAAe,CAACD,SAAS,CAAC;MACnC;IACJ,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAA7L,iBAAA,CAAG,WAAO2L,SAAS,EAAK;QACxC,MAAM9E,QAAQ,GAAIsE,MAAI,CAACtE,QAAQ,SAAS5H,gBAAgB,CAAC0M,SAAS,CAAE;QACpE;AACZ;AACA;QACYR,MAAI,CAACW,qBAAqB,GAAG,MAAM;UAC/BvB,gBAAgB,CAAC1D,QAAQ,EAAEsE,MAAI,CAAC1J,EAAE,CAAC;QACvC,CAAC;QACDoF,QAAQ,CAAC5B,gBAAgB,CAAC,QAAQ,EAAEkG,MAAI,CAACW,qBAAqB,CAAC;QAC/DvB,gBAAgB,CAAC1D,QAAQ,EAAEsE,MAAI,CAAC1J,EAAE,CAAC;MACvC,CAAC;MAAA,iBAAAsK,GAAA;QAAA,OAAAF,KAAA,CAAAhE,KAAA,OAAAC,SAAA;MAAA;IAAA;EACL;EACAjI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC0L,sBAAsB,CAAC,CAAC;EACjC;EACAS,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACT,sBAAsB,CAAC,CAAC;EACjC;EACM3G,iBAAiBA,CAAA,EAAG;IAAA,IAAAqH,MAAA;IAAA,OAAAjM,iBAAA;MACtBiM,MAAI,CAACb,YAAY,SAASlM,wBAAwB;QAAA,IAAAgN,KAAA,GAAAlM,iBAAA,CAAC,WAAOmM,YAAY,EAAEC,aAAa,EAAK;UACtF;AACZ;AACA;AACA;AACA;UACY,IAAID,YAAY,KAAK,KAAK,IAAIC,aAAa,KAAK1I,SAAS,EAAE;YACvD,MAAM0I,aAAa;UACvB;UACAH,MAAI,CAACZ,eAAe,GAAGc,YAAY,CAAC,CAAC;QACzC,CAAC;QAAA,iBAAAE,GAAA,EAAAC,GAAA;UAAA,OAAAJ,KAAA,CAAArE,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACA5C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACkG,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACmB,OAAO,CAAC,CAAC;IAC/B;EACJ;EACAd,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC5E,QAAQ,IAAI,IAAI,CAACiF,qBAAqB,EAAE;MAC7C,IAAI,CAACjF,QAAQ,CAACzB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC0G,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAGpI,SAAS;IAC1C;EACJ;EACArC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEiK,WAAW;MAAEnJ;IAAS,CAAC,GAAG,IAAI;IACtC,MAAMb,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMsN,IAAI,GAAG,IAAI,CAACxI,EAAE,CAACoD,OAAO,CAAC,UAAU,CAAC;IACxC,MAAM2H,MAAM,GAAGvC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACwC,aAAa,CAAC,sBAAsB,CAAC;IACrG,OAAQ7P,CAAC,CAACI,IAAI,EAAE;MAAEuE,GAAG,EAAE,0CAA0C;MAAE8H,IAAI,EAAE,aAAa;MAAE7H,KAAK,EAAE;QACvF,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QACxB,CAAC,oBAAoB,GAAGgK,WAAW;QACnC,CAAC,sBAAsBhK,IAAI,EAAE,GAAGgK,WAAW;QAC3C,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAACD,eAAe,KAAK,CAACmB,MAAM,IAAIA,MAAM,CAACE,IAAI,KAAK,QAAQ,CAAC;QAC1F,CAAC,mBAAmBvK,QAAQ,EAAE,GAAGA,QAAQ,KAAKuB;MAClD;IAAE,CAAC,EAAEpC,IAAI,KAAK,KAAK,IAAIgK,WAAW,IAAI1O,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE5E,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxM;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDgO,MAAM,CAACnJ,KAAK,GAAG;EACXK,GAAG,EAAE4I,YAAY;EACjB3I,EAAE,EAAE4I;AACR,CAAC;AAED,MAAM0B,UAAU,GAAG,sBAAsB;AACzC,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAC9B,MAAMC,WAAW,GAAGC,QAAQ,CAACN,aAAa,CAAC,GAAGI,OAAO,qBAAqB,CAAC;EAC3E,IAAIC,WAAW,KAAK,IAAI,EAAE;IACtB,OAAOA,WAAW;EACtB;EACA,MAAME,QAAQ,GAAGD,QAAQ,CAACE,aAAa,CAACJ,OAAO,CAAC;EAChDG,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;EAC5CH,QAAQ,CAACjL,KAAK,CAAC+I,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;EAC7CiC,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;EACnC,OAAOA,QAAQ;AACnB,CAAC;AACD,MAAMM,iBAAiB,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE;IACX;EACJ;EACA,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,gBAAgB,CAAC,aAAa,CAAC;EACzD,OAAO;IACHhM,EAAE,EAAE8L,QAAQ;IACZC,QAAQ,EAAEE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,GAAG,CAAEC,OAAO,IAAK;MAC5C,MAAMC,UAAU,GAAGD,OAAO,CAACpB,aAAa,CAAC,WAAW,CAAC;MACrD,OAAO;QACHhL,EAAE,EAAEoM,OAAO;QACXE,UAAU,EAAEF,OAAO,CAACG,UAAU,CAACvB,aAAa,CAAC,qBAAqB,CAAC;QACnEqB,UAAU;QACVG,YAAY,EAAEH,UAAU,GAAGA,UAAU,CAACE,UAAU,CAACvB,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACvFyB,YAAY,EAAER,KAAK,CAACC,IAAI,CAACE,OAAO,CAACJ,gBAAgB,CAAC,aAAa,CAAC;MACpE,CAAC;IACL,CAAC;EACL,CAAC;AACL,CAAC;AACD,MAAMU,mBAAmB,GAAGA,CAACtH,QAAQ,EAAEuH,iBAAiB,EAAEzC,SAAS,KAAK;EACpEjO,QAAQ,CAAC,MAAM;IACX,MAAM4F,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAMuH,KAAK,GAAGtM,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC+E,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC;IACjD;IACA,MAAM+K,eAAe,GAAG1C,SAAS,CAACc,aAAa,CAAC,gCAAgC,CAAC;IACjF,IAAI4B,eAAe,KAAK,IAAI,EAAE;MAC1BvQ,SAAS,CAAC,MAAM;QACZwQ,gBAAgB,CAACF,iBAAiB,CAACZ,QAAQ,EAAE3C,KAAK,CAAC;MACvD,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAM0D,2BAA2B,GAAGA,CAAChB,QAAQ,EAAEiB,OAAO,KAAK;EACvD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIjB,QAAQ,CAACpL,QAAQ,KAAK,MAAM,EAAE;IAC9B;EACJ;EACA,IAAIqM,OAAO,KAAK9K,SAAS,EAAE;IACvB6J,QAAQ,CAACxL,KAAK,CAAC0M,cAAc,CAAC,iBAAiB,CAAC;EACpD,CAAC,MACI;IACDlB,QAAQ,CAACxL,KAAK,CAAC+I,WAAW,CAAC,iBAAiB,EAAE0D,OAAO,CAACzD,QAAQ,CAAC,CAAC,CAAC;EACrE;AACJ,CAAC;AACD,MAAM2D,+BAA+B,GAAGA,CAACrI,EAAE,EAAEsI,eAAe,EAAErL,SAAS,KAAK;EACxE,IAAI,CAAC+C,EAAE,CAAC,CAAC,CAAC,CAACuI,cAAc,EAAE;IACvB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM/D,KAAK,GAAGxE,EAAE,CAAC,CAAC,CAAC,CAACwI,iBAAiB,GAAG,GAAG,IAAIvL,SAAS,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG+C,EAAE,CAAC,CAAC,CAAC,CAACwI,iBAAiB,IAAI,GAAG,GAAI,EAAE;EAC9GN,2BAA2B,CAACI,eAAe,CAAClN,EAAE,EAAEoJ,KAAK,KAAK,CAAC,GAAGnH,SAAS,GAAGmH,KAAK,CAAC;AACpF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMiE,yBAAyB,GAAGA,CAACzI,EAAE;AAAE;AACvCsI,eAAe,EAAEP,iBAAiB,EAAEvH,QAAQ,KAAK;EAC7C/I,SAAS,CAAC,MAAM;IACZ,MAAMwF,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpCoL,+BAA+B,CAACrI,EAAE,EAAEsI,eAAe,EAAErL,SAAS,CAAC;IAC/D,MAAMG,KAAK,GAAG4C,EAAE,CAAC,CAAC,CAAC;IACnB,MAAM0I,YAAY,GAAGtL,KAAK,CAACuL,gBAAgB;IAC3C,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,KAAK,GAAGH,YAAY,CAACI,MAAM;IACjE,MAAMC,QAAQ,GAAG3L,KAAK,CAAC4L,UAAU,CAACH,KAAK,GAAGzL,KAAK,CAAC4L,UAAU,CAACF,MAAM;IACjE,MAAMG,YAAY,GAAGL,gBAAgB,KAAK,CAAC,IAAIG,QAAQ,KAAK,CAAC;IAC7D,MAAMG,QAAQ,GAAGzJ,IAAI,CAAC0J,GAAG,CAACT,YAAY,CAACU,IAAI,GAAGhM,KAAK,CAACiM,kBAAkB,CAACD,IAAI,CAAC;IAC5E,MAAME,SAAS,GAAG7J,IAAI,CAAC0J,GAAG,CAACT,YAAY,CAACa,KAAK,GAAGnM,KAAK,CAACiM,kBAAkB,CAACE,KAAK,CAAC;IAC/E,MAAMC,mBAAmB,GAAGZ,gBAAgB,GAAG,CAAC,KAAKM,QAAQ,IAAI,CAAC,IAAII,SAAS,IAAI,CAAC,CAAC;IACrF,IAAIL,YAAY,IAAIO,mBAAmB,EAAE;MACrC;IACJ;IACA,IAAIpM,KAAK,CAACmL,cAAc,EAAE;MACtBkB,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCmB,eAAe,CAAC1B,iBAAiB,CAAC;IACtC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM2B,oBAAoB,GAAIhB,YAAY,CAAC/G,CAAC,KAAK,CAAC,IAAI+G,YAAY,CAACrH,CAAC,KAAK,CAAC,IAAMqH,YAAY,CAACG,KAAK,KAAK,CAAC,IAAIH,YAAY,CAACI,MAAM,KAAK,CAAE;MACtI,IAAIY,oBAAoB,IAAIzM,SAAS,GAAG,CAAC,EAAE;QACvCwM,eAAe,CAACnB,eAAe,CAAC;QAChCmB,eAAe,CAAC1B,iBAAiB,EAAE,KAAK,CAAC;QACzCG,2BAA2B,CAACI,eAAe,CAAClN,EAAE,CAAC;MACnD;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMqO,eAAe,GAAGA,CAACE,WAAW,EAAEC,MAAM,GAAG,IAAI,KAAK;EACpD,MAAM1C,QAAQ,GAAGyC,WAAW,CAACvO,EAAE;EAC/B,MAAM+L,QAAQ,GAAGwC,WAAW,CAACxC,QAAQ;EACrC,MAAM0C,SAAS,GAAG1C,QAAQ,CAACI,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAACC,UAAU,CAAC;EAC/D,IAAImC,MAAM,EAAE;IACR1C,QAAQ,CAACL,SAAS,CAACiD,MAAM,CAAC,mCAAmC,CAAC;IAC9DD,SAAS,CAACE,OAAO,CAAEC,QAAQ,IAAK;MAC5B,IAAIA,QAAQ,EAAE;QACVA,QAAQ,CAACC,eAAe,CAAC,aAAa,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN,CAAC,MACI;IACD/C,QAAQ,CAACL,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAC3D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ+C,SAAS,CAACE,OAAO,CAAEC,QAAQ,IAAK;MAC5B,IAAIA,QAAQ,EAAE;QACVA,QAAQ,CAACE,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAChD;IACJ,CAAC,CAAC;EACN;AACJ,CAAC;AACD,MAAMjC,gBAAgB,GAAGA,CAACd,QAAQ,GAAG,EAAE,EAAE3C,KAAK,GAAG,CAAC,EAAErL,UAAU,GAAG,KAAK,KAAK;EACvEgO,QAAQ,CAAC4C,OAAO,CAAEvC,OAAO,IAAK;IAC1B,MAAMwC,QAAQ,GAAGxC,OAAO,CAACC,UAAU;IACnC,MAAM0C,QAAQ,GAAG3C,OAAO,CAACI,YAAY;IACrC,IAAI,CAACoC,QAAQ,IAAIA,QAAQ,CAACI,IAAI,KAAK,OAAO,EAAE;MACxC;IACJ;IACAD,QAAQ,CAACzO,KAAK,CAACvC,UAAU,GAAGA,UAAU,GAAGmN,UAAU,GAAG,EAAE;IACxD6D,QAAQ,CAACzO,KAAK,CAAC2O,SAAS,GAAG,WAAW7F,KAAK,KAAKA,KAAK,MAAM;EAC/D,CAAC,CAAC;AACN,CAAC;AACD,MAAM8F,gBAAgB,GAAGA,CAAC9J,QAAQ,EAAE2D,MAAM,EAAEoG,cAAc,KAAK;EAC3DlT,QAAQ,CAAC,MAAM;IACX,MAAM4F,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAMuN,YAAY,GAAGrG,MAAM,CAAC5C,YAAY;IACxC,MAAM+C,SAAS,GAAGiG,cAAc,GAAGA,cAAc,CAAChJ,YAAY,GAAG,CAAC;IAClE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIgJ,cAAc,KAAK,IAAI,IAAItN,SAAS,GAAGqH,SAAS,EAAE;MAClDH,MAAM,CAACzI,KAAK,CAAC+I,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC;MAChDjE,QAAQ,CAAC9E,KAAK,CAAC+I,WAAW,CAAC,WAAW,EAAE,SAAS+F,YAAY,iBAAiB,CAAC;MAC/E;IACJ;IACA,MAAMjG,eAAe,GAAGtH,SAAS,GAAGqH,SAAS;IAC7C,MAAMD,YAAY,GAAG,EAAE;IACvB,MAAMG,KAAK,GAAGtM,KAAK,CAAC,CAAC,EAAEqM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IACzD5M,SAAS,CAAC,MAAM;MACZ+I,QAAQ,CAAC9E,KAAK,CAAC0M,cAAc,CAAC,WAAW,CAAC;MAC1CjE,MAAM,CAACzI,KAAK,CAAC+I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAM+F,YAAY,GAAG,+lEAA+lE;AAEpnE,MAAMC,WAAW,GAAG,sgBAAsgB;AAE1hB,MAAMC,MAAM,GAAG,MAAM;EACjBrR,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAqR,MAAA;IACjBxU,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACuD,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmI,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC4F,eAAe;MAAA,IAAAC,KAAA,GAAAnR,iBAAA,CAAG,WAAO2L,SAAS,EAAEiF,cAAc,EAAK;QACxD,MAAM/J,QAAQ,GAAIoK,MAAI,CAACpK,QAAQ,SAAS5H,gBAAgB,CAAC0M,SAAS,CAAE;QACpE;AACZ;AACA;QACYsF,MAAI,CAACnF,qBAAqB,GAAG,MAAM;UAC/B6E,gBAAgB,CAACM,MAAI,CAACpK,QAAQ,EAAEoK,MAAI,CAACxP,EAAE,EAAEmP,cAAc,CAAC;QAC5D,CAAC;QACD/J,QAAQ,CAAC5B,gBAAgB,CAAC,QAAQ,EAAEgM,MAAI,CAACnF,qBAAqB,CAAC;QAC/D6E,gBAAgB,CAACM,MAAI,CAACpK,QAAQ,EAAEoK,MAAI,CAACxP,EAAE,EAAEmP,cAAc,CAAC;MAC5D,CAAC;MAAA,iBAAAQ,GAAA,EAAAC,GAAA;QAAA,OAAAF,KAAA,CAAAtJ,KAAA,OAAAC,SAAA;MAAA;IAAA;EACL;EACAnD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxB,mBAAmB,GAAGhF,qBAAqB,CAAC,IAAI,CAACsD,EAAE,CAAC;EAC7D;EACA5B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACyR,sBAAsB,CAAC,CAAC;EACjC;EACAtF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACsF,sBAAsB,CAAC,CAAC;EACjC;EACApM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACqM,wBAAwB,CAAC,CAAC;EACnC;EACMD,sBAAsBA,CAAA,EAAG;IAAA,IAAAE,MAAA;IAAA,OAAAxR,iBAAA;MAC3B,MAAMsB,IAAI,GAAG3E,UAAU,CAAC6U,MAAI,CAAC;MAC7B,IAAIlQ,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEa;MAAS,CAAC,GAAGqP,MAAI;MACzB,MAAMC,WAAW,GAAGtP,QAAQ,KAAK,UAAU;MAC3C,MAAMqJ,OAAO,GAAGrJ,QAAQ,KAAK,MAAM;MACnCqP,MAAI,CAACD,wBAAwB,CAAC,CAAC;MAC/B,IAAIE,WAAW,EAAE;QACb,MAAM/F,MAAM,GAAG8F,MAAI,CAAC/P,EAAE,CAACoD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM8G,SAAS,GAAGD,MAAM,GAAG7M,cAAc,CAAC6M,MAAM,CAAC,GAAG,IAAI;QACxD;QACA5N,SAAS,CAAC,MAAM;UACZ,MAAM4T,KAAK,GAAG9E,YAAY,CAAC,WAAW,CAAC;UACvC8E,KAAK,CAACjB,IAAI,GAAG,OAAO;UACpB7D,YAAY,CAAC,iBAAiB,CAAC;QACnC,CAAC,CAAC;QACF,MAAM4E,MAAI,CAACG,mBAAmB,CAAChG,SAAS,EAAED,MAAM,CAAC;MACrD,CAAC,MACI,IAAIF,OAAO,EAAE;QACd,MAAME,MAAM,GAAG8F,MAAI,CAAC/P,EAAE,CAACoD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM8G,SAAS,GAAGD,MAAM,GAAG7M,cAAc,CAAC6M,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZ5M,uBAAuB,CAACyS,MAAI,CAAC/P,EAAE,CAAC;UAChC;QACJ;QACA,MAAMmP,cAAc,GAAGjF,SAAS,CAACc,aAAa,CAAC,iCAAiC,CAAC;QACjF,MAAM+E,MAAI,CAACN,eAAe,CAACvF,SAAS,EAAEiF,cAAc,CAAC;MACzD;IAAC;EACL;EACAW,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACK,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACC,UAAU,CAAC,CAAC;MACtC,IAAI,CAACD,oBAAoB,GAAGlO,SAAS;IACzC;IACA,IAAI,IAAI,CAACmD,QAAQ,IAAI,IAAI,CAACiF,qBAAqB,EAAE;MAC7C,IAAI,CAACjF,QAAQ,CAACzB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC0G,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAGpI,SAAS;IAC1C;IACA,IAAI,IAAI,CAACoO,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC5E,SAAS,CAACiD,MAAM,CAAC,sBAAsB,CAAC;MACnE,IAAI,CAAC2B,qBAAqB,GAAGpO,SAAS;IAC1C;EACJ;EACMiO,mBAAmBA,CAAChG,SAAS,EAAED,MAAM,EAAE;IAAA,IAAAqG,OAAA;IAAA,OAAA/R,iBAAA;MACzC,IAAI,CAAC2L,SAAS,IAAI,CAACD,MAAM,EAAE;QACvB3M,uBAAuB,CAACgT,OAAI,CAACtQ,EAAE,CAAC;QAChC;MACJ;MACA,IAAI,OAAOuQ,oBAAoB,KAAK,WAAW,EAAE;QAC7C;MACJ;MACAD,OAAI,CAAClL,QAAQ,SAAS5H,gBAAgB,CAAC0M,SAAS,CAAC;MACjD,MAAMsG,OAAO,GAAGvG,MAAM,CAAC+B,gBAAgB,CAAC,YAAY,CAAC;MACrDsE,OAAI,CAACD,qBAAqB,GAAGpE,KAAK,CAACC,IAAI,CAACsE,OAAO,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAAChQ,QAAQ,KAAK,UAAU,CAAC;MACjG,IAAI,CAAC4P,OAAI,CAACD,qBAAqB,EAAE;QAC7B;MACJ;MACA,MAAMnD,eAAe,GAAGrB,iBAAiB,CAACyE,OAAI,CAACD,qBAAqB,CAAC;MACrE,MAAM1D,iBAAiB,GAAGd,iBAAiB,CAACyE,OAAI,CAACtQ,EAAE,CAAC;MACpD,IAAI,CAACkN,eAAe,IAAI,CAACP,iBAAiB,EAAE;QACxC;MACJ;MACA0B,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCJ,2BAA2B,CAACI,eAAe,CAAClN,EAAE,EAAE,CAAC,CAAC;MAClD;AACR;AACA;AACA;AACA;AACA;MACQ,MAAM2Q,mBAAmB,GAAI/L,EAAE,IAAK;QAChCyI,yBAAyB,CAACzI,EAAE,EAAEsI,eAAe,EAAEP,iBAAiB,EAAE2D,OAAI,CAAClL,QAAQ,CAAC;MACpF,CAAC;MACDkL,OAAI,CAACH,oBAAoB,GAAG,IAAII,oBAAoB,CAACI,mBAAmB,EAAE;QACtEC,IAAI,EAAE1G,SAAS;QACf2G,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MAC1D,CAAC,CAAC;MACFP,OAAI,CAACH,oBAAoB,CAACW,OAAO,CAACnE,iBAAiB,CAACZ,QAAQ,CAACY,iBAAiB,CAACZ,QAAQ,CAACgF,MAAM,GAAG,CAAC,CAAC,CAAC/Q,EAAE,CAAC;MACvG;AACR;AACA;AACA;AACA;MACQsQ,OAAI,CAACjG,qBAAqB,GAAG,MAAM;QAC/BqC,mBAAmB,CAAC4D,OAAI,CAAClL,QAAQ,EAAEuH,iBAAiB,EAAEzC,SAAS,CAAC;MACpE,CAAC;MACDoG,OAAI,CAAClL,QAAQ,CAAC5B,gBAAgB,CAAC,QAAQ,EAAE8M,OAAI,CAACjG,qBAAqB,CAAC;MACpEhO,SAAS,CAAC,MAAM;QACZ,IAAIiU,OAAI,CAACD,qBAAqB,KAAKpO,SAAS,EAAE;UAC1CqO,OAAI,CAACD,qBAAqB,CAAC5E,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACpE;MACJ,CAAC,CAAC;IAAC;EACP;EACA9L,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEiK,WAAW;MAAEnI;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAM7B,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwF,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,MAAM;IACxC;IACA,MAAMsQ,QAAQ,GAAG7T,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC6C,EAAE,CAAC,GAAG,MAAM,GAAG,QAAQ;IACrE,OAAQ7E,CAAC,CAACI,IAAI,EAAEmM,MAAM,CAACC,MAAM,CAAC;MAAE7H,GAAG,EAAE,0CAA0C;MAAE8H,IAAI,EAAEoJ,QAAQ;MAAEjR,KAAK,EAAE;QAChG,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QACxB,CAAC,oBAAoB,GAAG,IAAI,CAACgK,WAAW;QACxC,CAAC,mBAAmBnJ,QAAQ,EAAE,GAAG,IAAI;QACrC,CAAC,sBAAsBb,IAAI,EAAE,GAAG,IAAI,CAACgK;MACzC;IAAE,CAAC,EAAEnI,mBAAmB,CAAC,EAAE7B,IAAI,KAAK,KAAK,IAAIgK,WAAW,IAAI1O,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE5E,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC9N;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD8T,MAAM,CAACjP,KAAK,GAAG;EACXK,GAAG,EAAE0O,YAAY;EACjBzO,EAAE,EAAE0O;AACR,CAAC;AAED,MAAM2B,eAAe,GAAG,4FAA4F;AAEpH,MAAMC,YAAY,GAAG,MAAM;EACvBhT,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACgT,cAAc,GAAGpV,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACqV,gBAAgB,GAAGrV,WAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACsV,eAAe,GAAGtV,WAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACuV,cAAc,GAAGzT,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAAC0T,4BAA4B,GAAG,KAAK;IACzC;AACR;AACA;IACQ,IAAI,CAAC1R,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC5B;AACR;AACA;IACQ,IAAI,CAACsW,QAAQ,GAAG,IAAI;EACxB;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,YAAY,KAAK3P,SAAS,CAAC;IACxD;EACJ;EACMkB,iBAAiBA,CAAA,EAAG;IAAA,IAAA0O,OAAA;IAAA,OAAAtT,iBAAA;MACtB,MAAMuT,OAAO,GAAGA,CAAA,KAAM;QAClBD,OAAI,CAACN,4BAA4B,GAAG,IAAI;QACxC,IAAIM,OAAI,CAACD,YAAY,EAAE;UACnBC,OAAI,CAACD,YAAY,CAACE,OAAO,CAAC,CAAC;QAC/B;MACJ,CAAC;MACDD,OAAI,CAACH,OAAO,GAAG,OAAO,MAAM,CAAC,0BAA0B,CAAC,EAAEK,sBAAsB,CAACF,OAAI,CAAC7R,EAAE,EAAE,MAAM,CAAC6R,OAAI,CAACN,4BAA4B,IAAI,CAAC,CAACM,OAAI,CAACD,YAAY,IAAIC,OAAI,CAACD,YAAY,CAACI,QAAQ,CAAC,CAAC,EAAE,MAAMF,OAAO,CAAC,CAAC,EAAG/K,IAAI,IAAK;QAAE,IAAIqB,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGyJ,OAAI,CAACI,GAAG,MAAM,IAAI,IAAI7J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8J,YAAY,CAACnL,IAAI,CAAC;MAAE,CAAC,EAAE,CAACoL,cAAc,EAAEpL,IAAI,EAAEqL,GAAG,KAAK;QACjV,IAAIP,OAAI,CAACI,GAAG,EAAE;UACVJ,OAAI,CAACI,GAAG,CAACI,QAAQ,CAAC,MAAM;YACpBR,OAAI,CAACN,4BAA4B,GAAG,KAAK;YACzC,IAAIM,OAAI,CAACD,YAAY,EAAE;cACnBC,OAAI,CAACD,YAAY,CAACU,KAAK,CAACH,cAAc,CAAC;YAC3C;UACJ,CAAC,EAAE;YAAEI,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;UACA,IAAIC,YAAY,GAAGL,cAAc,GAAG,CAAC,IAAI,GAAG,KAAK;UACjD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;UACgB,IAAI,CAACA,cAAc,EAAE;YACjBN,OAAI,CAACI,GAAG,CAACQ,MAAM,CAAC,gCAAgC,CAAC;YACjDD,YAAY,IAAI9U,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,MACI;YACDyL,YAAY,IAAI9U,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F;UACA8K,OAAI,CAACI,GAAG,CAACS,WAAW,CAACP,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEK,YAAY,EAAEJ,GAAG,CAAC;QACnE,CAAC,MACI;UACDP,OAAI,CAACN,4BAA4B,GAAG,KAAK;QAC7C;MACJ,CAAC,CAAC;MACFM,OAAI,CAACJ,mBAAmB,CAAC,CAAC;IAAC;EAC/B;EACAvO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACiO,cAAc,CAAC9L,IAAI,CAAC,CAAC;EAC9B;EACA5B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACiO,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC5G,OAAO,CAAC,CAAC;MACtB,IAAI,CAAC4G,OAAO,GAAGzP,SAAS;IAC5B;EACJ;EACA;EACM0Q,MAAMA,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAxU,iBAAA;MACtC,MAAMyU,MAAM,SAASD,OAAI,CAACzB,cAAc,CAAC2B,IAAI,CAAC,CAAC;MAC/C,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAI;QACAA,OAAO,SAASH,OAAI,CAAChV,UAAU,CAAC6U,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAChE,CAAC,CACD,OAAO7X,CAAC,EAAE;QACNsB,aAAa,CAAC,4CAA4C,EAAEtB,CAAC,CAAC;MAClE;MACA+X,MAAM,CAAC,CAAC;MACR,OAAOE,OAAO;IAAC;EACnB;EACA;EACMC,UAAUA,CAACnL,EAAE,EAAEoL,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAhV,iBAAA;MAC/C,MAAM2U,OAAO,SAASK,OAAI,CAACC,OAAO,CAACxL,EAAE,EAAEoL,MAAM,EAAE;QAC3CvN,QAAQ,EAAEwN,SAAS,KAAK,MAAM,GAAG,CAAC,GAAGpR,SAAS;QAC9CoR,SAAS,EAAEA,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QACpDI,gBAAgB,EAAEH;MACtB,CAAC,CAAC;MACF,OAAO;QACHJ,OAAO;QACPQ,OAAO,EAAEH,OAAI,CAACI;MAClB,CAAC;IAAC;EACN;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAtV,iBAAA;MACf,MAAMiQ,MAAM,GAAGqF,OAAI,CAACF,QAAQ;MAC5B,OAAOnF,MAAM,GACP;QACExG,EAAE,EAAEwG,MAAM,CAACpD,OAAO;QAClBsI,OAAO,EAAElF,MAAM;QACf4E,MAAM,EAAES,OAAI,CAACC;MACjB,CAAC,GACC7R,SAAS;IAAC;EACpB;EACMuR,OAAOA,CAACO,SAAS,EAAEX,MAAM,EAAEN,IAAI,EAAE;IAAA,IAAAkB,OAAA;IAAA,OAAAzV,iBAAA;MACnC,IAAIyV,OAAI,CAACC,eAAe,KAAKF,SAAS,IAAI/W,qBAAqB,CAACoW,MAAM,EAAEY,OAAI,CAACF,YAAY,CAAC,EAAE;QACxF,OAAO,KAAK;MAChB;MACA;MACA,MAAMjB,SAAS,GAAGmB,OAAI,CAACL,QAAQ;MAC/B,MAAMf,UAAU,SAASjV,eAAe,CAACqW,OAAI,CAACE,QAAQ,EAAEF,OAAI,CAAChU,EAAE,EAAE+T,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEX,MAAM,CAAC;MACvHY,OAAI,CAACC,eAAe,GAAGF,SAAS;MAChCC,OAAI,CAACL,QAAQ,GAAGf,UAAU;MAC1BoB,OAAI,CAACF,YAAY,GAAGV,MAAM;MAC1B;MACA,MAAMY,OAAI,CAACrB,MAAM,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAC9C,MAAMlV,eAAe,CAACoW,OAAI,CAACE,QAAQ,EAAErB,SAAS,CAAC;MAC/C,OAAO,IAAI;IAAC;EAChB;EACM9U,UAAUA,CAAAoW,GAAA,EAAAC,GAAA,EAAmC;IAAA,IAAAC,OAAA;IAAA,OAAA9V,iBAAA,YAAlCqU,UAAU,EAAEC,SAAS,EAAEC,IAAI,GAAG,CAAC,CAAC;MAC7C,IAAID,SAAS,KAAKD,UAAU,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA;MACAyB,OAAI,CAACjD,gBAAgB,CAAC/L,IAAI,CAAC,CAAC;MAC5B,MAAM;QAAErF,EAAE;QAAEH;MAAK,CAAC,GAAGwU,OAAI;MACzB,MAAM7C,QAAQ,GAAG6C,OAAI,CAAC7C,QAAQ,IAAInW,MAAM,CAACqD,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;MACrE,MAAM+U,gBAAgB,GAAGX,IAAI,CAACW,gBAAgB,IAAIY,OAAI,CAACf,SAAS,IAAIjY,MAAM,CAACiZ,GAAG,CAAC,cAAc,CAAC;MAC9F,MAAMvW,UAAU,CAAC2J,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE9H,IAAI;QAC/C2R,QAAQ;QACRoB,UAAU;QACVC,SAAS;QAAE9J,MAAM,EAAE/I,EAAE;QACrB;AACZ;AACA;AACA;AACA;QACYuU,QAAQ,EAAE5X,YAAY,CAACqD,EAAE,CAAC;QAAEwU,gBAAgB,EAAE1B,IAAI,CAAC2B,iBAAiB,GAC7DxC,GAAG,IAAK;UACP;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIA,GAAG,KAAKhQ,SAAS,IAAI,CAACoS,OAAI,CAAC9C,4BAA4B,EAAE;YACzD8C,OAAI,CAAC9C,4BAA4B,GAAG,IAAI;YACxCU,GAAG,CAACI,QAAQ,CAAC,MAAM;cACfgC,OAAI,CAAC9C,4BAA4B,GAAG,KAAK;cACzC,IAAI8C,OAAI,CAACzC,YAAY,EAAE;gBACnByC,OAAI,CAACzC,YAAY,CAACU,KAAK,CAAC,KAAK,CAAC;cAClC;YACJ,CAAC,EAAE;cAAEC,eAAe,EAAE;YAAK,CAAC,CAAC;YAC7B;AACxB;AACA;AACA;AACA;AACA;YACwBN,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B,CAAC,MACI;YACD2B,OAAI,CAACpC,GAAG,GAAGA,GAAG;UAClB;QACJ,CAAC,GACChQ;MAAU,CAAC,EAAE6Q,IAAI,CAAC,EAAE;QAAEW;MAAiB,CAAC,CAAC,CAAC;MACpD;MACAY,OAAI,CAAChD,eAAe,CAAChM,IAAI,CAAC,CAAC;MAC3B,OAAO,IAAI;IAAC,GAAAe,KAAA,OAAAC,SAAA;EAChB;EACAzG,MAAMA,CAAA,EAAG;IACL,OAAOzE,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC;EACzE;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiZ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACDxD,YAAY,CAAC5Q,KAAK,GAAG2Q,eAAe;AAEpC,MAAM0D,WAAW,GAAG,ujEAAujE;AAE3kE,MAAMC,UAAU,GAAG,grBAAgrB;AAEnsB,MAAMC,YAAY,GAAG,MAAM;EACvB3W,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAAC2W,QAAQ,GAAG/Y,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;EACpD;EACAgZ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACA7R,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC6R,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAMhG,IAAI,GAAG,IAAI,CAACiG,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACH,QAAQ,CAACzP,IAAI,CAAC;MACf,CAAC,SAAS2J,IAAI,EAAE,GAAG;IACvB,CAAC,CAAC;EACN;EACAiG,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjG,IAAI,KAAK/M,SAAS,GAAG,IAAI,CAAC+M,IAAI,GAAG,SAAS;EAC1D;EACApP,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8T,IAAI,GAAG,IAAI,CAACiG,OAAO,CAAC,CAAC;IAC3B,OAAQ9Z,CAAC,CAACI,IAAI,EAAE;MAAEuE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE7C,kBAAkB,CAAC,IAAI,CAAC2K,KAAK,EAAE;QACjG,CAAChI,IAAI,GAAG,IAAI;QACZ,CAAC,SAASmP,IAAI,EAAE,GAAG,IAAI;QACvB,WAAW,EAAE1D,QAAQ,CAAC4J,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,EAAE/Z,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAgB,CAAC,EAAE5E,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACpK;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiZ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACDG,YAAY,CAACvU,KAAK,GAAG;EACjBK,GAAG,EAAEgU,WAAW;EAChB/T,EAAE,EAAEgU;AACR,CAAC;AAED,MAAMO,aAAa,GAAG,0xFAA0xF;AAEhzF,MAAMC,YAAY,GAAG,u0EAAu0E;AAE51E,MAAMC,OAAO,GAAG,MAAM;EAClBnX,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACmX,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnC;EACArS,iBAAiBA,CAAA,EAAG;IAChB,MAAMsS,OAAO,GAAGvJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClM,EAAE,CAACgM,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMyJ,YAAY,GAAGD,OAAO,CAAC/E,IAAI,CAAEiF,MAAM,IAAK;MAC1C,OAAOA,MAAM,CAACzK,IAAI,KAAK,OAAO;IAClC,CAAC,CAAC;IACF,IAAIwK,YAAY,EAAE;MACdA,YAAY,CAAChK,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACpD;IACA,MAAMiK,eAAe,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC;IACzC,MAAMC,WAAW,GAAGF,eAAe,CAAClF,IAAI,CAAEiF,MAAM,IAAKA,MAAM,CAACzK,IAAI,KAAK,KAAK,CAAC,IACvE0K,eAAe,CAAClF,IAAI,CAAEiF,MAAM,IAAKA,MAAM,CAACzK,IAAI,KAAK,SAAS,CAAC,IAC3D0K,eAAe,CAAClF,IAAI,CAAEiF,MAAM,IAAKA,MAAM,CAACzK,IAAI,KAAK,WAAW,CAAC;IACjE,IAAI4K,WAAW,EAAE;MACbA,WAAW,CAACpK,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClD;EACJ;EACAoK,aAAaA,CAAClR,EAAE,EAAE;IACdA,EAAE,CAACmR,eAAe,CAAC,CAAC;IACpB,MAAM3K,OAAO,GAAGxG,EAAE,CAACoR,MAAM,CAAC5K,OAAO;IACjC,MAAM6K,aAAa,GAAGrR,EAAE,CAAChD,MAAM;IAC/B,MAAMsU,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAACb,cAAc,CAAChB,GAAG,CAAClJ,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAIgL,cAAc,GAAG,KAAK;IAC1B1O,MAAM,CAAC2O,IAAI,CAACJ,aAAa,CAAC,CAACtH,OAAO,CAAE7O,GAAG,IAAK;MACxC,MAAMwW,QAAQ,GAAG,WAAWxW,GAAG,EAAE;MACjC,MAAMyW,QAAQ,GAAGN,aAAa,CAACnW,GAAG,CAAC;MACnC,IAAIyW,QAAQ,KAAKJ,WAAW,CAACG,QAAQ,CAAC,EAAE;QACpCF,cAAc,GAAG,IAAI;MACzB;MACA,IAAIG,QAAQ,EAAE;QACVL,SAAS,CAACI,QAAQ,CAAC,GAAG,IAAI;MAC9B;IACJ,CAAC,CAAC;IACF,IAAIF,cAAc,EAAE;MAChB,IAAI,CAACd,cAAc,CAACkB,GAAG,CAACpL,OAAO,EAAE8K,SAAS,CAAC;MAC3C/Z,WAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACAyD,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG3E,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMib,WAAW,GAAG,CAAC,CAAC;IACtB,IAAI,CAACb,cAAc,CAAC3G,OAAO,CAAE8H,KAAK,IAAK;MACnC/O,MAAM,CAACC,MAAM,CAACwO,WAAW,EAAEM,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,OAAQtb,CAAC,CAACI,IAAI,EAAE;MAAEuE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE2H,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwO,WAAW,CAAC,EAAEjZ,kBAAkB,CAAC,IAAI,CAAC2K,KAAK,EAAE;QAC/I,CAAChI,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE1C,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE;MACpD,CAAC,CAAC;IAAE,CAAC,EAAE7E,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,oBAAoB;MAAEkI,IAAI,EAAE;IAAa,CAAC,CAAC,EAAE9M,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,mBAAmB;MAAEkI,IAAI,EAAE;IAAY,CAAC,EAAE9M,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEoI,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE/M,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEoI,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE/M,CAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,iBAAiB;MAAEkI,IAAI,EAAE;IAAU,CAAC,EAAE9M,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE3E,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEoI,IAAI,EAAE;IAAU,CAAC,CAAC,EAAE/M,CAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEoI,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EACttB;EACA,IAAIlI,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD4Z,OAAO,CAAC/U,KAAK,GAAG;EACZK,GAAG,EAAEwU,aAAa;EAClBvU,EAAE,EAAEwU;AACR,CAAC;AAED,SAASnX,GAAG,IAAIyY,OAAO,EAAEjW,OAAO,IAAIkW,WAAW,EAAE7V,OAAO,IAAI8V,WAAW,EAAEnN,MAAM,IAAIoN,UAAU,EAAEtH,MAAM,IAAIuH,UAAU,EAAE5F,YAAY,IAAI6F,iBAAiB,EAAElC,YAAY,IAAImC,SAAS,EAAE3B,OAAO,IAAI4B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}