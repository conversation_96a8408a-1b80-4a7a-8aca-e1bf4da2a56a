{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { n as focusVisibleElement, c as componentOnReady, f as addEventListener, m as removeEventListener, g as getElementRoot } from './helpers-1O4D2b7y.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, l as config, o as printIonError, m as printIonWarning, a as isPlatform } from './index-B_U9CtaY.js';\nimport { C as CoreDelegate } from './framework-delegate-DxcnWic_.js';\nimport { B as BACKDROP_NO_SCROLL } from './gesture-controller-BTEOs1at.js';\n\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. Element does not have a negative tabindex\n * 2. Element does not have `hidden`\n * 3. Element does not have `disabled` for non-Ionic components.\n * 4. Element does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\n/**\n * Focuses the first descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusFirstDescendant = (ref, fallbackElement) => {\n  const firstInput = ref.querySelector(focusableQueryString);\n  focusElementInContext(firstInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses the last descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusLastDescendant = (ref, fallbackElement) => {\n  const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n  const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n  focusElementInContext(lastInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses a particular element in a context. If the element\n * doesn't have anything focusable associated with it then\n * a fallback element will be focused.\n *\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInContext = (hostToFocus, fallbackElement) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    const radioGroup = elementToFocus.closest('ion-radio-group');\n    if (radioGroup) {\n      radioGroup.setFocus();\n    } else {\n      focusVisibleElement(elementToFocus);\n    }\n  } else {\n    // Focus fallback element instead of letting focus escape\n    fallbackElement.focus();\n  }\n};\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = tagName => {\n  return {\n    create(options) {\n      return createOverlay(tagName, options);\n    },\n    dismiss(data, role, id) {\n      return dismissOverlay(document, data, role, tagName, id);\n    },\n    getTop() {\n      return _asyncToGenerator(function* () {\n        return getPresentedOverlay(document, tagName);\n      })();\n    }\n  };\n};\nconst alertController = /*@__PURE__*/createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/createController('ion-loading');\nconst modalController = /*@__PURE__*/createController('ion-modal');\n/**\n * @deprecated Use the inline ion-picker component instead.\n */\nconst pickerController = /*@__PURE__*/createController('ion-picker-legacy');\nconst popoverController = /*@__PURE__*/createController('ion-popover');\nconst toastController = /*@__PURE__*/createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = el => {\n  if (typeof document !== 'undefined') {\n    /**\n     * Adds a single instance of event listeners for application behaviors:\n     *\n     * - Escape Key behavior to dismiss an overlay\n     * - Trapping focus within an overlay\n     * - Back button behavior to dismiss an overlay\n     *\n     * This only occurs when the first overlay is created.\n     */\n    connectListeners(document);\n  }\n  const overlayIndex = lastOverlayIndex++;\n  /**\n   * overlayIndex is used in the overlay components to set a zIndex.\n   * This ensures that the most recently presented overlay will be\n   * on top.\n   */\n  el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = el => {\n  if (!el.hasAttribute('id')) {\n    el.id = `ion-overlay-${++lastId}`;\n  }\n  return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n    return window.customElements.whenDefined(tagName).then(() => {\n      const element = document.createElement(tagName);\n      element.classList.add('overlay-hidden');\n      /**\n       * Convert the passed in overlay options into props\n       * that get passed down into the new overlay.\n       */\n      Object.assign(element, Object.assign(Object.assign({}, opts), {\n        hasController: true\n      }));\n      // append the overlay element to the document body\n      getAppRoot(document).appendChild(element);\n      return new Promise(resolve => componentOnReady(element, resolve));\n    });\n  }\n  return Promise.resolve();\n};\nconst isOverlayHidden = overlay => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    focusVisibleElement(elementToFocus);\n  } else {\n    // Focus overlay instead of letting focus escape\n    overlay.focus();\n  }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n  const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover');\n  const target = ev.target;\n  /**\n   * If no active overlay, ignore this event.\n   *\n   * If this component uses the shadow dom,\n   * this global listener is pointless\n   * since it will not catch the focus\n   * traps as they are inside the shadow root.\n   * We need to add a listener to the shadow root\n   * itself to ensure the focus trap works.\n   */\n  if (!lastOverlay || !target) {\n    return;\n  }\n  /**\n   * If the ion-disable-focus-trap class\n   * is present on an overlay, then this component\n   * instance has opted out of focus trapping.\n   * An example of this is when the sheet modal\n   * has a backdrop that is disabled. The content\n   * behind the sheet should be focusable until\n   * the backdrop is enabled.\n   */\n  if (lastOverlay.classList.contains(FOCUS_TRAP_DISABLE_CLASS)) {\n    return;\n  }\n  const trapScopedFocus = () => {\n    /**\n     * If we are focusing the overlay, clear\n     * the last focused element so that hitting\n     * tab activates the first focusable element\n     * in the overlay wrapper.\n     */\n    if (lastOverlay === target) {\n      lastOverlay.lastFocus = undefined;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n      /**\n       * Otherwise, we must be focusing an element\n       * inside of the overlay. The two possible options\n       * here are an input/button/etc or the ion-focus-trap\n       * element. The focus trap element is used to prevent\n       * the keyboard focus from leaving the overlay when\n       * using Tab or screen assistants.\n       */\n    } else {\n      /**\n       * We do not want to focus the traps, so get the overlay\n       * wrapper element as the traps live outside of the wrapper.\n       */\n      const overlayRoot = getElementRoot(lastOverlay);\n      if (!overlayRoot.contains(target)) {\n        return;\n      }\n      const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n      if (!overlayWrapper) {\n        return;\n      }\n      /**\n       * If the target is inside the wrapper, let the browser\n       * focus as normal and keep a log of the last focused element.\n       * Additionally, if the backdrop was tapped we should not\n       * move focus back inside the wrapper as that could cause\n       * an interactive elements focus state to activate.\n       */\n      if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n        lastOverlay.lastFocus = target;\n      } else {\n        /**\n         * Otherwise, we must have focused one of the focus traps.\n         * We need to wrap the focus to either the first element\n         * or the last element.\n         */\n        /**\n         * Once we call `focusFirstDescendant` and focus the first\n         * descendant, another focus event will fire which will\n         * cause `lastOverlay.lastFocus` to be updated before\n         * we can run the code after that. We will cache the value\n         * here to avoid that.\n         */\n        const lastFocus = lastOverlay.lastFocus;\n        // Focus the first element in the overlay wrapper\n        focusFirstDescendant(overlayWrapper, lastOverlay);\n        /**\n         * If the cached last focused element is the\n         * same as the active element, then we need\n         * to wrap focus to the last descendant. This happens\n         * when the first descendant is focused, and the user\n         * presses Shift + Tab. The previous line will focus\n         * the same descendant again (the first one), causing\n         * last focus to equal the active element.\n         */\n        if (lastFocus === doc.activeElement) {\n          focusLastDescendant(overlayWrapper, lastOverlay);\n        }\n        lastOverlay.lastFocus = doc.activeElement;\n      }\n    }\n  };\n  const trapShadowFocus = () => {\n    /**\n     * If the target is inside the wrapper, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (lastOverlay.contains(target)) {\n      lastOverlay.lastFocus = target;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n    } else {\n      /**\n       * Otherwise, we are about to have focus\n       * go out of the overlay. We need to wrap\n       * the focus to either the first element\n       * or the last element.\n       */\n      /**\n       * Once we call `focusFirstDescendant` and focus the first\n       * descendant, another focus event will fire which will\n       * cause `lastOverlay.lastFocus` to be updated before\n       * we can run the code after that. We will cache the value\n       * here to avoid that.\n       */\n      const lastFocus = lastOverlay.lastFocus;\n      // Focus the first element in the overlay wrapper\n      focusFirstDescendant(lastOverlay);\n      /**\n       * If the cached last focused element is the\n       * same as the active element, then we need\n       * to wrap focus to the last descendant. This happens\n       * when the first descendant is focused, and the user\n       * presses Shift + Tab. The previous line will focus\n       * the same descendant again (the first one), causing\n       * last focus to equal the active element.\n       */\n      if (lastFocus === doc.activeElement) {\n        focusLastDescendant(lastOverlay);\n      }\n      lastOverlay.lastFocus = doc.activeElement;\n    }\n  };\n  if (lastOverlay.shadowRoot) {\n    trapShadowFocus();\n  } else {\n    trapScopedFocus();\n  }\n};\nconst connectListeners = doc => {\n  if (lastOverlayIndex === 0) {\n    lastOverlayIndex = 1;\n    doc.addEventListener('focus', ev => {\n      trapKeyboardFocus(ev, doc);\n    }, true);\n    // handle back-button click\n    doc.addEventListener('ionBackButton', ev => {\n      const lastOverlay = getPresentedOverlay(doc);\n      if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n        ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n          /**\n           * Do not return this promise otherwise\n           * the hardware back button utility will\n           * be blocked until the overlay dismisses.\n           * This is important for a modal with canDismiss.\n           * If the application presents a confirmation alert\n           * in the \"canDismiss\" callback, then it will be impossible\n           * to use the hardware back button to dismiss the alert\n           * dialog because the hardware back button utility\n           * is blocked on waiting for the modal to dismiss.\n           */\n          lastOverlay.dismiss(undefined, BACKDROP);\n        });\n      }\n    });\n    /**\n     * Handle ESC to close overlay.\n     * CloseWatcher also handles pressing the Esc\n     * key, so if a browser supports CloseWatcher then\n     * this behavior will be handled via the ionBackButton\n     * event.\n     */\n    if (!shouldUseCloseWatcher()) {\n      doc.addEventListener('keydown', ev => {\n        if (ev.key === 'Escape') {\n          const lastOverlay = getPresentedOverlay(doc);\n          if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n            lastOverlay.dismiss(undefined, BACKDROP);\n          }\n        }\n      });\n    }\n  }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n  const overlay = getPresentedOverlay(doc, overlayTag, id);\n  if (!overlay) {\n    return Promise.reject('overlay does not exist');\n  }\n  return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n  if (selector === undefined) {\n    selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast';\n  }\n  return Array.from(doc.querySelectorAll(selector)).filter(c => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n  return getOverlays(doc, overlayTag).filter(o => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n  const overlays = getPresentedOverlays(doc, overlayTag);\n  return id === undefined ? overlays[overlays.length - 1] : overlays.find(o => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n  const root = getAppRoot(document);\n  const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n  if (!viewContainer) {\n    return;\n  }\n  if (hidden) {\n    viewContainer.setAttribute('aria-hidden', 'true');\n  } else {\n    viewContainer.removeAttribute('aria-hidden');\n  }\n};\nconst present = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) {\n    var _a, _b;\n    if (overlay.presented) {\n      return;\n    }\n    /**\n     * Due to accessibility guidelines, toasts do not have\n     * focus traps.\n     *\n     * All other overlays should have focus traps to prevent\n     * the keyboard focus from leaving the overlay.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n      setRootAriaHidden(true);\n      document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n    hideUnderlyingOverlaysFromScreenReaders(overlay.el);\n    hideAnimatingOverlayFromScreenReaders(overlay.el);\n    overlay.presented = true;\n    overlay.willPresent.emit();\n    (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n    const mode = getIonMode(overlay);\n    // get the user's animation fn if one was provided\n    const animationBuilder = overlay.enterAnimation ? overlay.enterAnimation : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n    const completed = yield overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    if (completed) {\n      overlay.didPresent.emit();\n      (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n    }\n    /**\n     * When an overlay that steals focus\n     * is dismissed, focus should be returned\n     * to the element that was focused\n     * prior to the overlay opening. Toast\n     * does not steal focus and is excluded\n     * from returning focus as a result.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n      restoreElementFocus(overlay.el);\n    }\n    /**\n     * If the focused element is already\n     * inside the overlay component then\n     * focus should not be moved from that\n     * to the overlay container.\n     */\n    if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n      overlay.el.focus();\n    }\n    /**\n     * If this overlay was previously dismissed without being\n     * the topmost one (such as by manually calling dismiss()),\n     * it would still have aria-hidden on being presented again.\n     * Removing it here ensures the overlay is visible to screen\n     * readers.\n     *\n     * If this overlay was being presented, then it was hidden\n     * from screen readers during the animation. Now that the\n     * animation is complete, we can reveal the overlay to\n     * screen readers.\n     */\n    overlay.el.removeAttribute('aria-hidden');\n  });\n  return function present(_x, _x2, _x3, _x4, _x5) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (overlayEl) {\n    let previousElement = document.activeElement;\n    if (!previousElement) {\n      return;\n    }\n    const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n    if (shadowRoot) {\n      // If there are no inner focusable elements, just focus the host element.\n      previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n    }\n    yield overlayEl.onDidDismiss();\n    /**\n     * After onDidDismiss, the overlay loses focus\n     * because it is removed from the document\n     *\n     * > An element will also lose focus [...]\n     * > if the element is removed from the document)\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n     *\n     * Additionally, `document.activeElement` returns:\n     *\n     * > The Element which currently has focus,\n     * > `<body>` or null if there is\n     * > no focused element.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n     *\n     * However, if the user has already focused\n     * an element sometime between onWillDismiss\n     * and onDidDismiss (for example, focusing a\n     * text box after tapping a button in an\n     * action sheet) then don't restore focus to\n     * previous element\n     */\n    if (document.activeElement === null || document.activeElement === document.body) {\n      previousElement.focus();\n    }\n  });\n  return function restoreElementFocus(_x6) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst dismiss = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) {\n    var _a, _b;\n    if (!overlay.presented) {\n      return false;\n    }\n    const presentedOverlays = doc !== undefined ? getPresentedOverlays(doc) : [];\n    /**\n     * For accessibility, toasts lack focus traps and don't receive\n     * `aria-hidden` on the root element when presented.\n     *\n     * All other overlays use focus traps to keep keyboard focus\n     * within the overlay, setting `aria-hidden` on the root element\n     * to enhance accessibility.\n     *\n     * Therefore, we must remove `aria-hidden` from the root element\n     * when the last non-toast overlay is dismissed.\n     */\n    const overlaysNotToast = presentedOverlays.filter(o => o.tagName !== 'ION-TOAST');\n    const lastOverlayNotToast = overlaysNotToast.length === 1 && overlaysNotToast[0].id === overlay.el.id;\n    /**\n     * If this is the last visible overlay that is not a toast\n     * then we want to re-add the root to the accessibility tree.\n     */\n    if (lastOverlayNotToast) {\n      setRootAriaHidden(false);\n      document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n    overlay.presented = false;\n    try {\n      /**\n       * There is no need to show the overlay to screen readers during\n       * the dismiss animation. This is because the overlay will be removed\n       * from the DOM after the animation is complete.\n       */\n      hideAnimatingOverlayFromScreenReaders(overlay.el);\n      // Overlay contents should not be clickable during dismiss\n      overlay.el.style.setProperty('pointer-events', 'none');\n      overlay.willDismiss.emit({\n        data,\n        role\n      });\n      (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({\n        data,\n        role\n      });\n      const mode = getIonMode(overlay);\n      const animationBuilder = overlay.leaveAnimation ? overlay.leaveAnimation : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n      // If dismissed via gesture, no need to play leaving animation again\n      if (role !== GESTURE) {\n        yield overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n      }\n      overlay.didDismiss.emit({\n        data,\n        role\n      });\n      (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({\n        data,\n        role\n      });\n      // Get a reference to all animations currently assigned to this overlay\n      // Then tear them down to return the overlay to its initial visual state\n      const animations = activeAnimations.get(overlay) || [];\n      animations.forEach(ani => ani.destroy());\n      activeAnimations.delete(overlay);\n      /**\n       * Make overlay hidden again in case it is being reused.\n       * We can safely remove pointer-events: none as\n       * overlay-hidden will set display: none.\n       */\n      overlay.el.classList.add('overlay-hidden');\n      overlay.el.style.removeProperty('pointer-events');\n      /**\n       * Clear any focus trapping references\n       * when the overlay is dismissed.\n       */\n      if (overlay.el.lastFocus !== undefined) {\n        overlay.el.lastFocus = undefined;\n      }\n    } catch (err) {\n      printIonError(`[${overlay.el.tagName.toLowerCase()}] - `, err);\n    }\n    overlay.el.remove();\n    revealOverlaysToScreenReaders();\n    return true;\n  });\n  return function dismiss(_x7, _x8, _x9, _x0, _x1, _x10, _x11) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst getAppRoot = doc => {\n  return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* (overlay, animationBuilder, baseEl, opts) {\n    // Make overlay visible in case it's hidden\n    baseEl.classList.remove('overlay-hidden');\n    const aniRoot = overlay.el;\n    const animation = animationBuilder(aniRoot, opts);\n    if (!overlay.animated || !config.getBoolean('animated', true)) {\n      animation.duration(0);\n    }\n    if (overlay.keyboardClose) {\n      animation.beforeAddWrite(() => {\n        const activeElement = baseEl.ownerDocument.activeElement;\n        if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n          activeElement.blur();\n        }\n      });\n    }\n    const activeAni = activeAnimations.get(overlay) || [];\n    activeAnimations.set(overlay, [...activeAni, animation]);\n    yield animation.play();\n    return true;\n  });\n  return function overlayAnimation(_x12, _x13, _x14, _x15) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nconst eventMethod = (element, eventName) => {\n  let resolve;\n  const promise = new Promise(r => resolve = r);\n  onceEvent(element, eventName, event => {\n    resolve(event.detail);\n  });\n  return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n  const handler = ev => {\n    removeEventListener(element, eventName, handler);\n    callback(ev);\n  };\n  addEventListener(element, eventName, handler);\n};\nconst isCancel = role => {\n  return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = h => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n  if (typeof handler === 'function') {\n    const jmp = config.get('_zoneGate', defaultGate);\n    return jmp(() => {\n      try {\n        return handler(arg);\n      } catch (e) {\n        throw e;\n      }\n    });\n  }\n  return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = ref => {\n  let inline = false;\n  let workingDelegate;\n  const coreDelegate = CoreDelegate();\n  /**\n   * Determines whether or not an overlay is being used\n   * inline or via a controller/JS and returns the correct delegate.\n   * By default, subsequent calls to getDelegate will use\n   * a cached version of the delegate.\n   * This is useful for calling dismiss after present,\n   * so that the correct delegate is given.\n   * @param force `true` to force the non-cached version of the delegate.\n   * @returns The delegate to use and whether or not the overlay is inline.\n   */\n  const getDelegate = (force = false) => {\n    if (workingDelegate && !force) {\n      return {\n        delegate: workingDelegate,\n        inline\n      };\n    }\n    const {\n      el,\n      hasController,\n      delegate\n    } = ref;\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = el.parentNode;\n    inline = parentEl !== null && !hasController;\n    workingDelegate = inline ? delegate || coreDelegate : delegate;\n    return {\n      inline,\n      delegate: workingDelegate\n    };\n  };\n  /**\n   * Attaches a component in the DOM. Teleports the component\n   * to the root of the app.\n   * @param component The component to optionally construct and append to the element.\n   */\n  const attachViewToDom = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(function* (component) {\n      const {\n        delegate\n      } = getDelegate(true);\n      if (delegate) {\n        return yield delegate.attachViewToDom(ref.el, component);\n      }\n      const {\n        hasController\n      } = ref;\n      if (hasController && component !== undefined) {\n        throw new Error('framework delegate is missing');\n      }\n      return null;\n    });\n    return function attachViewToDom(_x16) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  /**\n   * Moves a component back to its original location in the DOM.\n   */\n  const removeViewFromDom = () => {\n    const {\n      delegate\n    } = getDelegate();\n    if (delegate && ref.el !== undefined) {\n      delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n    }\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n  let destroyTriggerInteraction;\n  /**\n   * Removes the click listener from the trigger element.\n   */\n  const removeClickListener = () => {\n    if (destroyTriggerInteraction) {\n      destroyTriggerInteraction();\n      destroyTriggerInteraction = undefined;\n    }\n  };\n  /**\n   * Adds a click listener to the trigger element.\n   * Presents the overlay when the trigger is clicked.\n   * @param el The overlay element.\n   * @param trigger The ID of the element to add a click listener to.\n   */\n  const addClickListener = (el, trigger) => {\n    removeClickListener();\n    const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n    if (!triggerEl) {\n      printIonWarning(`[${el.tagName.toLowerCase()}] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n      return;\n    }\n    const configureTriggerInteraction = (targetEl, overlayEl) => {\n      const openOverlay = () => {\n        overlayEl.present();\n      };\n      targetEl.addEventListener('click', openOverlay);\n      return () => {\n        targetEl.removeEventListener('click', openOverlay);\n      };\n    };\n    destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n  };\n  return {\n    addClickListener,\n    removeClickListener\n  };\n};\n/**\n * The overlay that is being animated also needs to hide from screen\n * readers during its animation. This ensures that assistive technologies\n * like TalkBack do not announce or interact with the content until the\n * animation is complete, avoiding confusion for users.\n *\n * When the overlay is presented on an Android device, TalkBack's focus rings\n * may appear in the wrong position due to the transition (specifically\n * `transform` styles). This occurs because the focus rings are initially\n * displayed at the starting position of the elements before the transition\n * begins. This workaround ensures the focus rings do not appear in the\n * incorrect location.\n *\n * If this solution is applied to iOS devices, then it leads to a bug where\n * the overlays cannot be accessed by screen readers. This is due to\n * VoiceOver not being able to update the accessibility tree when the\n * `aria-hidden` is removed.\n *\n * @param overlay - The overlay that is being animated.\n */\nconst hideAnimatingOverlayFromScreenReaders = overlay => {\n  if (doc === undefined) return;\n  if (isPlatform('android')) {\n    /**\n     * Once the animation is complete, this attribute will be removed.\n     * This is done at the end of the `present` method.\n     */\n    overlay.setAttribute('aria-hidden', 'true');\n  }\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideUnderlyingOverlaysFromScreenReaders = newTopMostOverlay => {\n  var _a;\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const presentedOverlay = overlays[i];\n    const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n    /**\n     * If next overlay has aria-hidden then all remaining overlays will have it too.\n     * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n     * should not have aria-hidden either so focus can remain in the current overlay.\n     */\n    if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n      presentedOverlay.setAttribute('aria-hidden', 'true');\n    }\n  }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const currentOverlay = overlays[i];\n    /**\n     * If the current we are looking at is a Toast then we can remove aria-hidden.\n     * However, we potentially need to keep looking at the overlay stack because there\n     * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n     * overlay too so focus can move there since focus is never automatically moved to the Toast.\n     */\n    currentOverlay.removeAttribute('aria-hidden');\n    /**\n     * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n     * since this overlay should always receive focus. As a result, all underlying overlays should still\n     * be hidden from screen readers.\n     */\n    if (currentOverlay.tagName !== 'ION-TOAST') {\n      break;\n    }\n  }\n};\nconst FOCUS_TRAP_DISABLE_CLASS = 'ion-disable-focus-trap';\nexport { BACKDROP as B, FOCUS_TRAP_DISABLE_CLASS as F, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, focusLastDescendant as q, safeCall as s, toastController as t };", "map": {"version": 3, "names": ["d", "doc", "n", "focusVisibleElement", "c", "componentOnReady", "f", "addEventListener", "m", "removeEventListener", "g", "getElementRoot", "OVERLAY_BACK_BUTTON_PRIORITY", "shouldUseCloseWatcher", "e", "getIonMode", "l", "config", "o", "printIonError", "printIonWarning", "a", "isPlatform", "C", "CoreDelegate", "B", "BACKDROP_NO_SCROLL", "focusableQueryString", "focusFirstDescendant", "ref", "fallbackElement", "firstInput", "querySelector", "focusElementInContext", "focusLastDescendant", "inputs", "Array", "from", "querySelectorAll", "lastInput", "length", "hostToFocus", "elementToFocus", "shadowRoot", "radioGroup", "closest", "setFocus", "focus", "lastOverlayIndex", "lastId", "activeAnimations", "WeakMap", "createController", "tagName", "create", "options", "createOverlay", "dismiss", "data", "role", "id", "dismiss<PERSON><PERSON><PERSON>", "document", "getTop", "_asyncToGenerator", "getPresentedOverlay", "alertController", "actionSheetController", "loadingController", "modalController", "pickerController", "popoverController", "toastController", "prepareOverlay", "el", "connectListeners", "overlayIndex", "setOverlayId", "hasAttribute", "opts", "window", "customElements", "whenDefined", "then", "element", "createElement", "classList", "add", "Object", "assign", "hasController", "getAppRoot", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "isOverlayHidden", "overlay", "contains", "focusElementInOverlay", "trapKeyboardFocus", "ev", "lastOverlay", "target", "FOCUS_TRAP_DISABLE_CLASS", "trapScopedFocus", "lastFocus", "undefined", "overlayRoot", "overlayWrapper", "activeElement", "trapShadowFocus", "<PERSON><PERSON><PERSON><PERSON>", "detail", "register", "BACKDROP", "key", "overlayTag", "reject", "getOverlays", "selector", "filter", "getPresentedOverlays", "overlays", "find", "setRootAriaHidden", "hidden", "root", "viewContainer", "setAttribute", "removeAttribute", "present", "_ref", "name", "iosEnterAnimation", "mdEnterAnimation", "_a", "_b", "presented", "body", "hideUnderlyingOverlaysFromScreenReaders", "hideAnimatingOverlayFromScreenReaders", "willPresent", "emit", "willPresentShorthand", "mode", "animationBuilder", "enterAnimation", "get", "completed", "overlayAnimation", "didPresent", "didPresentShorthand", "restoreElementFocus", "keyboardClose", "_x", "_x2", "_x3", "_x4", "_x5", "apply", "arguments", "_ref2", "overlayEl", "previousElement", "onDid<PERSON><PERSON><PERSON>", "_x6", "_ref3", "iosLeaveAnimation", "mdLeaveAnimation", "presentedOverlays", "overlaysNotToast", "lastOverlayNotToast", "remove", "style", "setProperty", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leaveAnimation", "GESTURE", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animations", "for<PERSON>ach", "ani", "destroy", "delete", "removeProperty", "err", "toLowerCase", "revealOverlaysToScreenReaders", "_x7", "_x8", "_x9", "_x0", "_x1", "_x10", "_x11", "_ref4", "baseEl", "aniRoot", "animation", "animated", "getBoolean", "duration", "beforeAddWrite", "ownerDocument", "matches", "blur", "activeAni", "set", "play", "_x12", "_x13", "_x14", "_x15", "eventMethod", "eventName", "promise", "r", "onceEvent", "event", "callback", "handler", "isCancel", "defaultGate", "h", "safeCall", "arg", "jmp", "OVERLAY_GESTURE_PRIORITY", "createDelegateController", "inline", "workingDelegate", "coreDelegate", "getDelegate", "force", "delegate", "parentEl", "parentNode", "attachViewToDom", "_ref5", "component", "Error", "_x16", "removeViewFromDom", "parentElement", "createTriggerController", "destroyTriggerInteraction", "removeClickListener", "addClickListener", "trigger", "triggerEl", "getElementById", "configureTriggerInteraction", "targetEl", "openOverlay", "newTopMostOverlay", "i", "presentedOverlay", "nextPresentedOverlay", "currentOverlay", "F", "G", "O", "b", "j", "k", "p", "q", "s", "t"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FRONTEND DEVELOPER/pouch_db/node_modules/@ionic/core/dist/esm/overlays-8Y2rA-ps.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { n as focusVisibleElement, c as componentOnReady, f as addEventListener, m as removeEventListener, g as getElementRoot } from './helpers-1O4D2b7y.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, l as config, o as printIonError, m as printIonWarning, a as isPlatform } from './index-B_U9CtaY.js';\nimport { C as CoreDelegate } from './framework-delegate-DxcnWic_.js';\nimport { B as BACKDROP_NO_SCROLL } from './gesture-controller-BTEOs1at.js';\n\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. <PERSON><PERSON> does not have a negative tabindex\n * 2. <PERSON><PERSON> does not have `hidden`\n * 3. <PERSON><PERSON> does not have `disabled` for non-Ionic components.\n * 4. <PERSON><PERSON> does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\n/**\n * Focuses the first descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusFirstDescendant = (ref, fallbackElement) => {\n    const firstInput = ref.querySelector(focusableQueryString);\n    focusElementInContext(firstInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses the last descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusLastDescendant = (ref, fallbackElement) => {\n    const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n    const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n    focusElementInContext(lastInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses a particular element in a context. If the element\n * doesn't have anything focusable associated with it then\n * a fallback element will be focused.\n *\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInContext = (hostToFocus, fallbackElement) => {\n    let elementToFocus = hostToFocus;\n    const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n    }\n    if (elementToFocus) {\n        const radioGroup = elementToFocus.closest('ion-radio-group');\n        if (radioGroup) {\n            radioGroup.setFocus();\n        }\n        else {\n            focusVisibleElement(elementToFocus);\n        }\n    }\n    else {\n        // Focus fallback element instead of letting focus escape\n        fallbackElement.focus();\n    }\n};\n\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = (tagName) => {\n    return {\n        create(options) {\n            return createOverlay(tagName, options);\n        },\n        dismiss(data, role, id) {\n            return dismissOverlay(document, data, role, tagName, id);\n        },\n        async getTop() {\n            return getPresentedOverlay(document, tagName);\n        },\n    };\n};\nconst alertController = /*@__PURE__*/ createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/ createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/ createController('ion-loading');\nconst modalController = /*@__PURE__*/ createController('ion-modal');\n/**\n * @deprecated Use the inline ion-picker component instead.\n */\nconst pickerController = /*@__PURE__*/ createController('ion-picker-legacy');\nconst popoverController = /*@__PURE__*/ createController('ion-popover');\nconst toastController = /*@__PURE__*/ createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = (el) => {\n    if (typeof document !== 'undefined') {\n        /**\n         * Adds a single instance of event listeners for application behaviors:\n         *\n         * - Escape Key behavior to dismiss an overlay\n         * - Trapping focus within an overlay\n         * - Back button behavior to dismiss an overlay\n         *\n         * This only occurs when the first overlay is created.\n         */\n        connectListeners(document);\n    }\n    const overlayIndex = lastOverlayIndex++;\n    /**\n     * overlayIndex is used in the overlay components to set a zIndex.\n     * This ensures that the most recently presented overlay will be\n     * on top.\n     */\n    el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = (el) => {\n    if (!el.hasAttribute('id')) {\n        el.id = `ion-overlay-${++lastId}`;\n    }\n    return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n        return window.customElements.whenDefined(tagName).then(() => {\n            const element = document.createElement(tagName);\n            element.classList.add('overlay-hidden');\n            /**\n             * Convert the passed in overlay options into props\n             * that get passed down into the new overlay.\n             */\n            Object.assign(element, Object.assign(Object.assign({}, opts), { hasController: true }));\n            // append the overlay element to the document body\n            getAppRoot(document).appendChild(element);\n            return new Promise((resolve) => componentOnReady(element, resolve));\n        });\n    }\n    return Promise.resolve();\n};\nconst isOverlayHidden = (overlay) => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n    let elementToFocus = hostToFocus;\n    const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n    }\n    if (elementToFocus) {\n        focusVisibleElement(elementToFocus);\n    }\n    else {\n        // Focus overlay instead of letting focus escape\n        overlay.focus();\n    }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n    const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover');\n    const target = ev.target;\n    /**\n     * If no active overlay, ignore this event.\n     *\n     * If this component uses the shadow dom,\n     * this global listener is pointless\n     * since it will not catch the focus\n     * traps as they are inside the shadow root.\n     * We need to add a listener to the shadow root\n     * itself to ensure the focus trap works.\n     */\n    if (!lastOverlay || !target) {\n        return;\n    }\n    /**\n     * If the ion-disable-focus-trap class\n     * is present on an overlay, then this component\n     * instance has opted out of focus trapping.\n     * An example of this is when the sheet modal\n     * has a backdrop that is disabled. The content\n     * behind the sheet should be focusable until\n     * the backdrop is enabled.\n     */\n    if (lastOverlay.classList.contains(FOCUS_TRAP_DISABLE_CLASS)) {\n        return;\n    }\n    const trapScopedFocus = () => {\n        /**\n         * If we are focusing the overlay, clear\n         * the last focused element so that hitting\n         * tab activates the first focusable element\n         * in the overlay wrapper.\n         */\n        if (lastOverlay === target) {\n            lastOverlay.lastFocus = undefined;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n            /**\n             * Otherwise, we must be focusing an element\n             * inside of the overlay. The two possible options\n             * here are an input/button/etc or the ion-focus-trap\n             * element. The focus trap element is used to prevent\n             * the keyboard focus from leaving the overlay when\n             * using Tab or screen assistants.\n             */\n        }\n        else {\n            /**\n             * We do not want to focus the traps, so get the overlay\n             * wrapper element as the traps live outside of the wrapper.\n             */\n            const overlayRoot = getElementRoot(lastOverlay);\n            if (!overlayRoot.contains(target)) {\n                return;\n            }\n            const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n            if (!overlayWrapper) {\n                return;\n            }\n            /**\n             * If the target is inside the wrapper, let the browser\n             * focus as normal and keep a log of the last focused element.\n             * Additionally, if the backdrop was tapped we should not\n             * move focus back inside the wrapper as that could cause\n             * an interactive elements focus state to activate.\n             */\n            if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n                lastOverlay.lastFocus = target;\n            }\n            else {\n                /**\n                 * Otherwise, we must have focused one of the focus traps.\n                 * We need to wrap the focus to either the first element\n                 * or the last element.\n                 */\n                /**\n                 * Once we call `focusFirstDescendant` and focus the first\n                 * descendant, another focus event will fire which will\n                 * cause `lastOverlay.lastFocus` to be updated before\n                 * we can run the code after that. We will cache the value\n                 * here to avoid that.\n                 */\n                const lastFocus = lastOverlay.lastFocus;\n                // Focus the first element in the overlay wrapper\n                focusFirstDescendant(overlayWrapper, lastOverlay);\n                /**\n                 * If the cached last focused element is the\n                 * same as the active element, then we need\n                 * to wrap focus to the last descendant. This happens\n                 * when the first descendant is focused, and the user\n                 * presses Shift + Tab. The previous line will focus\n                 * the same descendant again (the first one), causing\n                 * last focus to equal the active element.\n                 */\n                if (lastFocus === doc.activeElement) {\n                    focusLastDescendant(overlayWrapper, lastOverlay);\n                }\n                lastOverlay.lastFocus = doc.activeElement;\n            }\n        }\n    };\n    const trapShadowFocus = () => {\n        /**\n         * If the target is inside the wrapper, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (lastOverlay.contains(target)) {\n            lastOverlay.lastFocus = target;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus\n             * go out of the overlay. We need to wrap\n             * the focus to either the first element\n             * or the last element.\n             */\n            /**\n             * Once we call `focusFirstDescendant` and focus the first\n             * descendant, another focus event will fire which will\n             * cause `lastOverlay.lastFocus` to be updated before\n             * we can run the code after that. We will cache the value\n             * here to avoid that.\n             */\n            const lastFocus = lastOverlay.lastFocus;\n            // Focus the first element in the overlay wrapper\n            focusFirstDescendant(lastOverlay);\n            /**\n             * If the cached last focused element is the\n             * same as the active element, then we need\n             * to wrap focus to the last descendant. This happens\n             * when the first descendant is focused, and the user\n             * presses Shift + Tab. The previous line will focus\n             * the same descendant again (the first one), causing\n             * last focus to equal the active element.\n             */\n            if (lastFocus === doc.activeElement) {\n                focusLastDescendant(lastOverlay);\n            }\n            lastOverlay.lastFocus = doc.activeElement;\n        }\n    };\n    if (lastOverlay.shadowRoot) {\n        trapShadowFocus();\n    }\n    else {\n        trapScopedFocus();\n    }\n};\nconst connectListeners = (doc) => {\n    if (lastOverlayIndex === 0) {\n        lastOverlayIndex = 1;\n        doc.addEventListener('focus', (ev) => {\n            trapKeyboardFocus(ev, doc);\n        }, true);\n        // handle back-button click\n        doc.addEventListener('ionBackButton', (ev) => {\n            const lastOverlay = getPresentedOverlay(doc);\n            if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n                    /**\n                     * Do not return this promise otherwise\n                     * the hardware back button utility will\n                     * be blocked until the overlay dismisses.\n                     * This is important for a modal with canDismiss.\n                     * If the application presents a confirmation alert\n                     * in the \"canDismiss\" callback, then it will be impossible\n                     * to use the hardware back button to dismiss the alert\n                     * dialog because the hardware back button utility\n                     * is blocked on waiting for the modal to dismiss.\n                     */\n                    lastOverlay.dismiss(undefined, BACKDROP);\n                });\n            }\n        });\n        /**\n         * Handle ESC to close overlay.\n         * CloseWatcher also handles pressing the Esc\n         * key, so if a browser supports CloseWatcher then\n         * this behavior will be handled via the ionBackButton\n         * event.\n         */\n        if (!shouldUseCloseWatcher()) {\n            doc.addEventListener('keydown', (ev) => {\n                if (ev.key === 'Escape') {\n                    const lastOverlay = getPresentedOverlay(doc);\n                    if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                        lastOverlay.dismiss(undefined, BACKDROP);\n                    }\n                }\n            });\n        }\n    }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n    const overlay = getPresentedOverlay(doc, overlayTag, id);\n    if (!overlay) {\n        return Promise.reject('overlay does not exist');\n    }\n    return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n    if (selector === undefined) {\n        selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast';\n    }\n    return Array.from(doc.querySelectorAll(selector)).filter((c) => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n    return getOverlays(doc, overlayTag).filter((o) => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n    const overlays = getPresentedOverlays(doc, overlayTag);\n    return id === undefined ? overlays[overlays.length - 1] : overlays.find((o) => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n    const root = getAppRoot(document);\n    const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n    if (!viewContainer) {\n        return;\n    }\n    if (hidden) {\n        viewContainer.setAttribute('aria-hidden', 'true');\n    }\n    else {\n        viewContainer.removeAttribute('aria-hidden');\n    }\n};\nconst present = async (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) => {\n    var _a, _b;\n    if (overlay.presented) {\n        return;\n    }\n    /**\n     * Due to accessibility guidelines, toasts do not have\n     * focus traps.\n     *\n     * All other overlays should have focus traps to prevent\n     * the keyboard focus from leaving the overlay.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n        setRootAriaHidden(true);\n        document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n    hideUnderlyingOverlaysFromScreenReaders(overlay.el);\n    hideAnimatingOverlayFromScreenReaders(overlay.el);\n    overlay.presented = true;\n    overlay.willPresent.emit();\n    (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n    const mode = getIonMode(overlay);\n    // get the user's animation fn if one was provided\n    const animationBuilder = overlay.enterAnimation\n        ? overlay.enterAnimation\n        : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n    const completed = await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    if (completed) {\n        overlay.didPresent.emit();\n        (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n    }\n    /**\n     * When an overlay that steals focus\n     * is dismissed, focus should be returned\n     * to the element that was focused\n     * prior to the overlay opening. Toast\n     * does not steal focus and is excluded\n     * from returning focus as a result.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n        restoreElementFocus(overlay.el);\n    }\n    /**\n     * If the focused element is already\n     * inside the overlay component then\n     * focus should not be moved from that\n     * to the overlay container.\n     */\n    if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n        overlay.el.focus();\n    }\n    /**\n     * If this overlay was previously dismissed without being\n     * the topmost one (such as by manually calling dismiss()),\n     * it would still have aria-hidden on being presented again.\n     * Removing it here ensures the overlay is visible to screen\n     * readers.\n     *\n     * If this overlay was being presented, then it was hidden\n     * from screen readers during the animation. Now that the\n     * animation is complete, we can reveal the overlay to\n     * screen readers.\n     */\n    overlay.el.removeAttribute('aria-hidden');\n};\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = async (overlayEl) => {\n    let previousElement = document.activeElement;\n    if (!previousElement) {\n        return;\n    }\n    const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n    }\n    await overlayEl.onDidDismiss();\n    /**\n     * After onDidDismiss, the overlay loses focus\n     * because it is removed from the document\n     *\n     * > An element will also lose focus [...]\n     * > if the element is removed from the document)\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n     *\n     * Additionally, `document.activeElement` returns:\n     *\n     * > The Element which currently has focus,\n     * > `<body>` or null if there is\n     * > no focused element.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n     *\n     * However, if the user has already focused\n     * an element sometime between onWillDismiss\n     * and onDidDismiss (for example, focusing a\n     * text box after tapping a button in an\n     * action sheet) then don't restore focus to\n     * previous element\n     */\n    if (document.activeElement === null || document.activeElement === document.body) {\n        previousElement.focus();\n    }\n};\nconst dismiss = async (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) => {\n    var _a, _b;\n    if (!overlay.presented) {\n        return false;\n    }\n    const presentedOverlays = doc !== undefined ? getPresentedOverlays(doc) : [];\n    /**\n     * For accessibility, toasts lack focus traps and don't receive\n     * `aria-hidden` on the root element when presented.\n     *\n     * All other overlays use focus traps to keep keyboard focus\n     * within the overlay, setting `aria-hidden` on the root element\n     * to enhance accessibility.\n     *\n     * Therefore, we must remove `aria-hidden` from the root element\n     * when the last non-toast overlay is dismissed.\n     */\n    const overlaysNotToast = presentedOverlays.filter((o) => o.tagName !== 'ION-TOAST');\n    const lastOverlayNotToast = overlaysNotToast.length === 1 && overlaysNotToast[0].id === overlay.el.id;\n    /**\n     * If this is the last visible overlay that is not a toast\n     * then we want to re-add the root to the accessibility tree.\n     */\n    if (lastOverlayNotToast) {\n        setRootAriaHidden(false);\n        document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n    overlay.presented = false;\n    try {\n        /**\n         * There is no need to show the overlay to screen readers during\n         * the dismiss animation. This is because the overlay will be removed\n         * from the DOM after the animation is complete.\n         */\n        hideAnimatingOverlayFromScreenReaders(overlay.el);\n        // Overlay contents should not be clickable during dismiss\n        overlay.el.style.setProperty('pointer-events', 'none');\n        overlay.willDismiss.emit({ data, role });\n        (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({ data, role });\n        const mode = getIonMode(overlay);\n        const animationBuilder = overlay.leaveAnimation\n            ? overlay.leaveAnimation\n            : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n        // If dismissed via gesture, no need to play leaving animation again\n        if (role !== GESTURE) {\n            await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n        }\n        overlay.didDismiss.emit({ data, role });\n        (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({ data, role });\n        // Get a reference to all animations currently assigned to this overlay\n        // Then tear them down to return the overlay to its initial visual state\n        const animations = activeAnimations.get(overlay) || [];\n        animations.forEach((ani) => ani.destroy());\n        activeAnimations.delete(overlay);\n        /**\n         * Make overlay hidden again in case it is being reused.\n         * We can safely remove pointer-events: none as\n         * overlay-hidden will set display: none.\n         */\n        overlay.el.classList.add('overlay-hidden');\n        overlay.el.style.removeProperty('pointer-events');\n        /**\n         * Clear any focus trapping references\n         * when the overlay is dismissed.\n         */\n        if (overlay.el.lastFocus !== undefined) {\n            overlay.el.lastFocus = undefined;\n        }\n    }\n    catch (err) {\n        printIonError(`[${overlay.el.tagName.toLowerCase()}] - `, err);\n    }\n    overlay.el.remove();\n    revealOverlaysToScreenReaders();\n    return true;\n};\nconst getAppRoot = (doc) => {\n    return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = async (overlay, animationBuilder, baseEl, opts) => {\n    // Make overlay visible in case it's hidden\n    baseEl.classList.remove('overlay-hidden');\n    const aniRoot = overlay.el;\n    const animation = animationBuilder(aniRoot, opts);\n    if (!overlay.animated || !config.getBoolean('animated', true)) {\n        animation.duration(0);\n    }\n    if (overlay.keyboardClose) {\n        animation.beforeAddWrite(() => {\n            const activeElement = baseEl.ownerDocument.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n                activeElement.blur();\n            }\n        });\n    }\n    const activeAni = activeAnimations.get(overlay) || [];\n    activeAnimations.set(overlay, [...activeAni, animation]);\n    await animation.play();\n    return true;\n};\nconst eventMethod = (element, eventName) => {\n    let resolve;\n    const promise = new Promise((r) => (resolve = r));\n    onceEvent(element, eventName, (event) => {\n        resolve(event.detail);\n    });\n    return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n    const handler = (ev) => {\n        removeEventListener(element, eventName, handler);\n        callback(ev);\n    };\n    addEventListener(element, eventName, handler);\n};\nconst isCancel = (role) => {\n    return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = (h) => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n    if (typeof handler === 'function') {\n        const jmp = config.get('_zoneGate', defaultGate);\n        return jmp(() => {\n            try {\n                return handler(arg);\n            }\n            catch (e) {\n                throw e;\n            }\n        });\n    }\n    return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = (ref) => {\n    let inline = false;\n    let workingDelegate;\n    const coreDelegate = CoreDelegate();\n    /**\n     * Determines whether or not an overlay is being used\n     * inline or via a controller/JS and returns the correct delegate.\n     * By default, subsequent calls to getDelegate will use\n     * a cached version of the delegate.\n     * This is useful for calling dismiss after present,\n     * so that the correct delegate is given.\n     * @param force `true` to force the non-cached version of the delegate.\n     * @returns The delegate to use and whether or not the overlay is inline.\n     */\n    const getDelegate = (force = false) => {\n        if (workingDelegate && !force) {\n            return {\n                delegate: workingDelegate,\n                inline,\n            };\n        }\n        const { el, hasController, delegate } = ref;\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = el.parentNode;\n        inline = parentEl !== null && !hasController;\n        workingDelegate = inline ? delegate || coreDelegate : delegate;\n        return { inline, delegate: workingDelegate };\n    };\n    /**\n     * Attaches a component in the DOM. Teleports the component\n     * to the root of the app.\n     * @param component The component to optionally construct and append to the element.\n     */\n    const attachViewToDom = async (component) => {\n        const { delegate } = getDelegate(true);\n        if (delegate) {\n            return await delegate.attachViewToDom(ref.el, component);\n        }\n        const { hasController } = ref;\n        if (hasController && component !== undefined) {\n            throw new Error('framework delegate is missing');\n        }\n        return null;\n    };\n    /**\n     * Moves a component back to its original location in the DOM.\n     */\n    const removeViewFromDom = () => {\n        const { delegate } = getDelegate();\n        if (delegate && ref.el !== undefined) {\n            delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n        }\n    };\n    return {\n        attachViewToDom,\n        removeViewFromDom,\n    };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n    let destroyTriggerInteraction;\n    /**\n     * Removes the click listener from the trigger element.\n     */\n    const removeClickListener = () => {\n        if (destroyTriggerInteraction) {\n            destroyTriggerInteraction();\n            destroyTriggerInteraction = undefined;\n        }\n    };\n    /**\n     * Adds a click listener to the trigger element.\n     * Presents the overlay when the trigger is clicked.\n     * @param el The overlay element.\n     * @param trigger The ID of the element to add a click listener to.\n     */\n    const addClickListener = (el, trigger) => {\n        removeClickListener();\n        const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n        if (!triggerEl) {\n            printIonWarning(`[${el.tagName.toLowerCase()}] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n            return;\n        }\n        const configureTriggerInteraction = (targetEl, overlayEl) => {\n            const openOverlay = () => {\n                overlayEl.present();\n            };\n            targetEl.addEventListener('click', openOverlay);\n            return () => {\n                targetEl.removeEventListener('click', openOverlay);\n            };\n        };\n        destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n    };\n    return {\n        addClickListener,\n        removeClickListener,\n    };\n};\n/**\n * The overlay that is being animated also needs to hide from screen\n * readers during its animation. This ensures that assistive technologies\n * like TalkBack do not announce or interact with the content until the\n * animation is complete, avoiding confusion for users.\n *\n * When the overlay is presented on an Android device, TalkBack's focus rings\n * may appear in the wrong position due to the transition (specifically\n * `transform` styles). This occurs because the focus rings are initially\n * displayed at the starting position of the elements before the transition\n * begins. This workaround ensures the focus rings do not appear in the\n * incorrect location.\n *\n * If this solution is applied to iOS devices, then it leads to a bug where\n * the overlays cannot be accessed by screen readers. This is due to\n * VoiceOver not being able to update the accessibility tree when the\n * `aria-hidden` is removed.\n *\n * @param overlay - The overlay that is being animated.\n */\nconst hideAnimatingOverlayFromScreenReaders = (overlay) => {\n    if (doc === undefined)\n        return;\n    if (isPlatform('android')) {\n        /**\n         * Once the animation is complete, this attribute will be removed.\n         * This is done at the end of the `present` method.\n         */\n        overlay.setAttribute('aria-hidden', 'true');\n    }\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideUnderlyingOverlaysFromScreenReaders = (newTopMostOverlay) => {\n    var _a;\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const presentedOverlay = overlays[i];\n        const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n        /**\n         * If next overlay has aria-hidden then all remaining overlays will have it too.\n         * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n         * should not have aria-hidden either so focus can remain in the current overlay.\n         */\n        if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n            presentedOverlay.setAttribute('aria-hidden', 'true');\n        }\n    }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const currentOverlay = overlays[i];\n        /**\n         * If the current we are looking at is a Toast then we can remove aria-hidden.\n         * However, we potentially need to keep looking at the overlay stack because there\n         * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n         * overlay too so focus can move there since focus is never automatically moved to the Toast.\n         */\n        currentOverlay.removeAttribute('aria-hidden');\n        /**\n         * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n         * since this overlay should always receive focus. As a result, all underlying overlays should still\n         * be hidden from screen readers.\n         */\n        if (currentOverlay.tagName !== 'ION-TOAST') {\n            break;\n        }\n    }\n};\nconst FOCUS_TRAP_DISABLE_CLASS = 'ion-disable-focus-trap';\n\nexport { BACKDROP as B, FOCUS_TRAP_DISABLE_CLASS as F, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, focusLastDescendant as q, safeCall as s, toastController as t };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,uBAAuB;AAC7J,SAASC,4BAA4B,EAAEC,qBAAqB,QAAQ,oCAAoC;AACxG,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,aAAa,EAAEX,CAAC,IAAIY,eAAe,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC7H,SAASC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AACpE,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,kCAAkC;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,skBAAskB;AACnmB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGA,CAACC,GAAG,EAAEC,eAAe,KAAK;EACnD,MAAMC,UAAU,GAAGF,GAAG,CAACG,aAAa,CAACL,oBAAoB,CAAC;EAC1DM,qBAAqB,CAACF,UAAU,EAAED,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGD,GAAG,CAAC;AACrH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,mBAAmB,GAAGA,CAACL,GAAG,EAAEC,eAAe,KAAK;EAClD,MAAMK,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACR,GAAG,CAACS,gBAAgB,CAACX,oBAAoB,CAAC,CAAC;EACrE,MAAMY,SAAS,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EACtEP,qBAAqB,CAACM,SAAS,EAAET,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGD,GAAG,CAAC;AACpH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,GAAGA,CAACQ,WAAW,EAAEX,eAAe,KAAK;EAC5D,IAAIY,cAAc,GAAGD,WAAW;EAChC,MAAME,UAAU,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,UAAU;EACnG,IAAIA,UAAU,EAAE;IACZ;IACAD,cAAc,GAAGC,UAAU,CAACX,aAAa,CAACL,oBAAoB,CAAC,IAAIc,WAAW;EAClF;EACA,IAAIC,cAAc,EAAE;IAChB,MAAME,UAAU,GAAGF,cAAc,CAACG,OAAO,CAAC,iBAAiB,CAAC;IAC5D,IAAID,UAAU,EAAE;MACZA,UAAU,CAACE,QAAQ,CAAC,CAAC;IACzB,CAAC,MACI;MACD3C,mBAAmB,CAACuC,cAAc,CAAC;IACvC;EACJ,CAAC,MACI;IACD;IACAZ,eAAe,CAACiB,KAAK,CAAC,CAAC;EAC3B;AACJ,CAAC;AAED,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,MAAM,GAAG,CAAC;AACd,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACtC,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;EAClC,OAAO;IACHC,MAAMA,CAACC,OAAO,EAAE;MACZ,OAAOC,aAAa,CAACH,OAAO,EAAEE,OAAO,CAAC;IAC1C,CAAC;IACDE,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAE;MACpB,OAAOC,cAAc,CAACC,QAAQ,EAAEJ,IAAI,EAAEC,IAAI,EAAEN,OAAO,EAAEO,EAAE,CAAC;IAC5D,CAAC;IACKG,MAAMA,CAAA,EAAG;MAAA,OAAAC,iBAAA;QACX,OAAOC,mBAAmB,CAACH,QAAQ,EAAET,OAAO,CAAC;MAAC;IAClD;EACJ,CAAC;AACL,CAAC;AACD,MAAMa,eAAe,GAAG,aAAcd,gBAAgB,CAAC,WAAW,CAAC;AACnE,MAAMe,qBAAqB,GAAG,aAAcf,gBAAgB,CAAC,kBAAkB,CAAC;AAChF,MAAMgB,iBAAiB,GAAG,aAAchB,gBAAgB,CAAC,aAAa,CAAC;AACvE,MAAMiB,eAAe,GAAG,aAAcjB,gBAAgB,CAAC,WAAW,CAAC;AACnE;AACA;AACA;AACA,MAAMkB,gBAAgB,GAAG,aAAclB,gBAAgB,CAAC,mBAAmB,CAAC;AAC5E,MAAMmB,iBAAiB,GAAG,aAAcnB,gBAAgB,CAAC,aAAa,CAAC;AACvE,MAAMoB,eAAe,GAAG,aAAcpB,gBAAgB,CAAC,WAAW,CAAC;AACnE;AACA;AACA;AACA,MAAMqB,cAAc,GAAIC,EAAE,IAAK;EAC3B,IAAI,OAAOZ,QAAQ,KAAK,WAAW,EAAE;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQa,gBAAgB,CAACb,QAAQ,CAAC;EAC9B;EACA,MAAMc,YAAY,GAAG5B,gBAAgB,EAAE;EACvC;AACJ;AACA;AACA;AACA;EACI0B,EAAE,CAACE,YAAY,GAAGA,YAAY;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAIH,EAAE,IAAK;EACzB,IAAI,CAACA,EAAE,CAACI,YAAY,CAAC,IAAI,CAAC,EAAE;IACxBJ,EAAE,CAACd,EAAE,GAAG,eAAe,EAAEX,MAAM,EAAE;EACrC;EACA,OAAOyB,EAAE,CAACd,EAAE;AAChB,CAAC;AACD,MAAMJ,aAAa,GAAGA,CAACH,OAAO,EAAE0B,IAAI,KAAK;EACrC;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,cAAc,KAAK,WAAW,EAAE;IAC/E,OAAOD,MAAM,CAACC,cAAc,CAACC,WAAW,CAAC7B,OAAO,CAAC,CAAC8B,IAAI,CAAC,MAAM;MACzD,MAAMC,OAAO,GAAGtB,QAAQ,CAACuB,aAAa,CAAChC,OAAO,CAAC;MAC/C+B,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MACvC;AACZ;AACA;AACA;MACYC,MAAM,CAACC,MAAM,CAACL,OAAO,EAAEI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,IAAI,CAAC,EAAE;QAAEW,aAAa,EAAE;MAAK,CAAC,CAAC,CAAC;MACvF;MACAC,UAAU,CAAC7B,QAAQ,CAAC,CAAC8B,WAAW,CAACR,OAAO,CAAC;MACzC,OAAO,IAAIS,OAAO,CAAEC,OAAO,IAAKzF,gBAAgB,CAAC+E,OAAO,EAAEU,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC;EACN;EACA,OAAOD,OAAO,CAACC,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD,MAAMC,eAAe,GAAIC,OAAO,IAAKA,OAAO,CAACV,SAAS,CAACW,QAAQ,CAAC,gBAAgB,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACzD,WAAW,EAAEuD,OAAO,KAAK;EACpD,IAAItD,cAAc,GAAGD,WAAW;EAChC,MAAME,UAAU,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,UAAU;EACnG,IAAIA,UAAU,EAAE;IACZ;IACAD,cAAc,GAAGC,UAAU,CAACX,aAAa,CAACL,oBAAoB,CAAC,IAAIc,WAAW;EAClF;EACA,IAAIC,cAAc,EAAE;IAChBvC,mBAAmB,CAACuC,cAAc,CAAC;EACvC,CAAC,MACI;IACD;IACAsD,OAAO,CAACjD,KAAK,CAAC,CAAC;EACnB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,iBAAiB,GAAGA,CAACC,EAAE,EAAEnG,GAAG,KAAK;EACnC,MAAMoG,WAAW,GAAGpC,mBAAmB,CAAChE,GAAG,EAAE,gFAAgF,CAAC;EAC9H,MAAMqG,MAAM,GAAGF,EAAE,CAACE,MAAM;EACxB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACD,WAAW,IAAI,CAACC,MAAM,EAAE;IACzB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAID,WAAW,CAACf,SAAS,CAACW,QAAQ,CAACM,wBAAwB,CAAC,EAAE;IAC1D;EACJ;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIH,WAAW,KAAKC,MAAM,EAAE;MACxBD,WAAW,CAACI,SAAS,GAAGC,SAAS;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIJ,MAAM,CAACjD,OAAO,KAAK,WAAW,EAAE;MACrC6C,qBAAqB,CAACG,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAAC;MACzD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAMM,WAAW,GAAGhG,cAAc,CAAC0F,WAAW,CAAC;MAC/C,IAAI,CAACM,WAAW,CAACV,QAAQ,CAACK,MAAM,CAAC,EAAE;QAC/B;MACJ;MACA,MAAMM,cAAc,GAAGD,WAAW,CAAC3E,aAAa,CAAC,sBAAsB,CAAC;MACxE,IAAI,CAAC4E,cAAc,EAAE;QACjB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIA,cAAc,CAACX,QAAQ,CAACK,MAAM,CAAC,IAAIA,MAAM,KAAKK,WAAW,CAAC3E,aAAa,CAAC,cAAc,CAAC,EAAE;QACzFqE,WAAW,CAACI,SAAS,GAAGH,MAAM;MAClC,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;QACgB;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMG,SAAS,GAAGJ,WAAW,CAACI,SAAS;QACvC;QACA7E,oBAAoB,CAACgF,cAAc,EAAEP,WAAW,CAAC;QACjD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAII,SAAS,KAAKxG,GAAG,CAAC4G,aAAa,EAAE;UACjC3E,mBAAmB,CAAC0E,cAAc,EAAEP,WAAW,CAAC;QACpD;QACAA,WAAW,CAACI,SAAS,GAAGxG,GAAG,CAAC4G,aAAa;MAC7C;IACJ;EACJ,CAAC;EACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B;AACR;AACA;AACA;IACQ,IAAIT,WAAW,CAACJ,QAAQ,CAACK,MAAM,CAAC,EAAE;MAC9BD,WAAW,CAACI,SAAS,GAAGH,MAAM;MAC9B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIA,MAAM,CAACjD,OAAO,KAAK,WAAW,EAAE;MACrC6C,qBAAqB,CAACG,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAAC;IAC7D,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;MACY;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMI,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACvC;MACA7E,oBAAoB,CAACyE,WAAW,CAAC;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAII,SAAS,KAAKxG,GAAG,CAAC4G,aAAa,EAAE;QACjC3E,mBAAmB,CAACmE,WAAW,CAAC;MACpC;MACAA,WAAW,CAACI,SAAS,GAAGxG,GAAG,CAAC4G,aAAa;IAC7C;EACJ,CAAC;EACD,IAAIR,WAAW,CAAC1D,UAAU,EAAE;IACxBmE,eAAe,CAAC,CAAC;EACrB,CAAC,MACI;IACDN,eAAe,CAAC,CAAC;EACrB;AACJ,CAAC;AACD,MAAM7B,gBAAgB,GAAI1E,GAAG,IAAK;EAC9B,IAAI+C,gBAAgB,KAAK,CAAC,EAAE;IACxBA,gBAAgB,GAAG,CAAC;IACpB/C,GAAG,CAACM,gBAAgB,CAAC,OAAO,EAAG6F,EAAE,IAAK;MAClCD,iBAAiB,CAACC,EAAE,EAAEnG,GAAG,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC;IACR;IACAA,GAAG,CAACM,gBAAgB,CAAC,eAAe,EAAG6F,EAAE,IAAK;MAC1C,MAAMC,WAAW,GAAGpC,mBAAmB,CAAChE,GAAG,CAAC;MAC5C,IAAIoG,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACU,eAAe,EAAE;QACvFX,EAAE,CAACY,MAAM,CAACC,QAAQ,CAACrG,4BAA4B,EAAE,MAAM;UACnD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoByF,WAAW,CAAC5C,OAAO,CAACiD,SAAS,EAAEQ,QAAQ,CAAC;QAC5C,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACrG,qBAAqB,CAAC,CAAC,EAAE;MAC1BZ,GAAG,CAACM,gBAAgB,CAAC,SAAS,EAAG6F,EAAE,IAAK;QACpC,IAAIA,EAAE,CAACe,GAAG,KAAK,QAAQ,EAAE;UACrB,MAAMd,WAAW,GAAGpC,mBAAmB,CAAChE,GAAG,CAAC;UAC5C,IAAIoG,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACU,eAAe,EAAE;YACvFV,WAAW,CAAC5C,OAAO,CAACiD,SAAS,EAAEQ,QAAQ,CAAC;UAC5C;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;AACJ,CAAC;AACD,MAAMrD,cAAc,GAAGA,CAAC5D,GAAG,EAAEyD,IAAI,EAAEC,IAAI,EAAEyD,UAAU,EAAExD,EAAE,KAAK;EACxD,MAAMoC,OAAO,GAAG/B,mBAAmB,CAAChE,GAAG,EAAEmH,UAAU,EAAExD,EAAE,CAAC;EACxD,IAAI,CAACoC,OAAO,EAAE;IACV,OAAOH,OAAO,CAACwB,MAAM,CAAC,wBAAwB,CAAC;EACnD;EACA,OAAOrB,OAAO,CAACvC,OAAO,CAACC,IAAI,EAAEC,IAAI,CAAC;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM2D,WAAW,GAAGA,CAACrH,GAAG,EAAEsH,QAAQ,KAAK;EACnC,IAAIA,QAAQ,KAAKb,SAAS,EAAE;IACxBa,QAAQ,GAAG,0FAA0F;EACzG;EACA,OAAOnF,KAAK,CAACC,IAAI,CAACpC,GAAG,CAACqC,gBAAgB,CAACiF,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAEpH,CAAC,IAAKA,CAAC,CAACwE,YAAY,GAAG,CAAC,CAAC;AACvF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6C,oBAAoB,GAAGA,CAACxH,GAAG,EAAEmH,UAAU,KAAK;EAC9C,OAAOE,WAAW,CAACrH,GAAG,EAAEmH,UAAU,CAAC,CAACI,MAAM,CAAEtG,CAAC,IAAK,CAAC6E,eAAe,CAAC7E,CAAC,CAAC,CAAC;AAC1E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,mBAAmB,GAAGA,CAAChE,GAAG,EAAEmH,UAAU,EAAExD,EAAE,KAAK;EACjD,MAAM8D,QAAQ,GAAGD,oBAAoB,CAACxH,GAAG,EAAEmH,UAAU,CAAC;EACtD,OAAOxD,EAAE,KAAK8C,SAAS,GAAGgB,QAAQ,CAACA,QAAQ,CAAClF,MAAM,GAAG,CAAC,CAAC,GAAGkF,QAAQ,CAACC,IAAI,CAAEzG,CAAC,IAAKA,CAAC,CAAC0C,EAAE,KAAKA,EAAE,CAAC;AAC/F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgE,iBAAiB,GAAGA,CAACC,MAAM,GAAG,KAAK,KAAK;EAC1C,MAAMC,IAAI,GAAGnC,UAAU,CAAC7B,QAAQ,CAAC;EACjC,MAAMiE,aAAa,GAAGD,IAAI,CAAC9F,aAAa,CAAC,sDAAsD,CAAC;EAChG,IAAI,CAAC+F,aAAa,EAAE;IAChB;EACJ;EACA,IAAIF,MAAM,EAAE;IACRE,aAAa,CAACC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EACrD,CAAC,MACI;IACDD,aAAa,CAACE,eAAe,CAAC,aAAa,CAAC;EAChD;AACJ,CAAC;AACD,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAnE,iBAAA,CAAG,WAAOgC,OAAO,EAAEoC,IAAI,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEvD,IAAI,EAAK;IAChF,IAAIwD,EAAE,EAAEC,EAAE;IACV,IAAIxC,OAAO,CAACyC,SAAS,EAAE;MACnB;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIzC,OAAO,CAACtB,EAAE,CAACrB,OAAO,KAAK,WAAW,EAAE;MACpCuE,iBAAiB,CAAC,IAAI,CAAC;MACvB9D,QAAQ,CAAC4E,IAAI,CAACpD,SAAS,CAACC,GAAG,CAAC7D,kBAAkB,CAAC;IACnD;IACAiH,uCAAuC,CAAC3C,OAAO,CAACtB,EAAE,CAAC;IACnDkE,qCAAqC,CAAC5C,OAAO,CAACtB,EAAE,CAAC;IACjDsB,OAAO,CAACyC,SAAS,GAAG,IAAI;IACxBzC,OAAO,CAAC6C,WAAW,CAACC,IAAI,CAAC,CAAC;IAC1B,CAACP,EAAE,GAAGvC,OAAO,CAAC+C,oBAAoB,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAAC,CAAC;IAClF,MAAME,IAAI,GAAGjI,UAAU,CAACiF,OAAO,CAAC;IAChC;IACA,MAAMiD,gBAAgB,GAAGjD,OAAO,CAACkD,cAAc,GACzClD,OAAO,CAACkD,cAAc,GACtBjI,MAAM,CAACkI,GAAG,CAACf,IAAI,EAAEY,IAAI,KAAK,KAAK,GAAGX,iBAAiB,GAAGC,gBAAgB,CAAC;IAC7E,MAAMc,SAAS,SAASC,gBAAgB,CAACrD,OAAO,EAAEiD,gBAAgB,EAAEjD,OAAO,CAACtB,EAAE,EAAEK,IAAI,CAAC;IACrF,IAAIqE,SAAS,EAAE;MACXpD,OAAO,CAACsD,UAAU,CAACR,IAAI,CAAC,CAAC;MACzB,CAACN,EAAE,GAAGxC,OAAO,CAACuD,mBAAmB,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAAC,CAAC;IACrF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI9C,OAAO,CAACtB,EAAE,CAACrB,OAAO,KAAK,WAAW,EAAE;MACpCmG,mBAAmB,CAACxD,OAAO,CAACtB,EAAE,CAAC;IACnC;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIsB,OAAO,CAACyD,aAAa,KAAK3F,QAAQ,CAAC+C,aAAa,KAAK,IAAI,IAAI,CAACb,OAAO,CAACtB,EAAE,CAACuB,QAAQ,CAACnC,QAAQ,CAAC+C,aAAa,CAAC,CAAC,EAAE;MAC5Gb,OAAO,CAACtB,EAAE,CAAC3B,KAAK,CAAC,CAAC;IACtB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIiD,OAAO,CAACtB,EAAE,CAACuD,eAAe,CAAC,aAAa,CAAC;EAC7C,CAAC;EAAA,gBAhEKC,OAAOA,CAAAwB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAA3B,IAAA,CAAA4B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgEZ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMR,mBAAmB;EAAA,IAAAS,KAAA,GAAAjG,iBAAA,CAAG,WAAOkG,SAAS,EAAK;IAC7C,IAAIC,eAAe,GAAGrG,QAAQ,CAAC+C,aAAa;IAC5C,IAAI,CAACsD,eAAe,EAAE;MAClB;IACJ;IACA,MAAMxH,UAAU,GAAGwH,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACxH,UAAU;IAC/G,IAAIA,UAAU,EAAE;MACZ;MACAwH,eAAe,GAAGxH,UAAU,CAACX,aAAa,CAACL,oBAAoB,CAAC,IAAIwI,eAAe;IACvF;IACA,MAAMD,SAAS,CAACE,YAAY,CAAC,CAAC;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAItG,QAAQ,CAAC+C,aAAa,KAAK,IAAI,IAAI/C,QAAQ,CAAC+C,aAAa,KAAK/C,QAAQ,CAAC4E,IAAI,EAAE;MAC7EyB,eAAe,CAACpH,KAAK,CAAC,CAAC;IAC3B;EACJ,CAAC;EAAA,gBAtCKyG,mBAAmBA,CAAAa,GAAA;IAAA,OAAAJ,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsCxB;AACD,MAAMvG,OAAO;EAAA,IAAA6G,KAAA,GAAAtG,iBAAA,CAAG,WAAOgC,OAAO,EAAEtC,IAAI,EAAEC,IAAI,EAAEyE,IAAI,EAAEmC,iBAAiB,EAAEC,gBAAgB,EAAEzF,IAAI,EAAK;IAC5F,IAAIwD,EAAE,EAAEC,EAAE;IACV,IAAI,CAACxC,OAAO,CAACyC,SAAS,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,MAAMgC,iBAAiB,GAAGxK,GAAG,KAAKyG,SAAS,GAAGe,oBAAoB,CAACxH,GAAG,CAAC,GAAG,EAAE;IAC5E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,MAAMyK,gBAAgB,GAAGD,iBAAiB,CAACjD,MAAM,CAAEtG,CAAC,IAAKA,CAAC,CAACmC,OAAO,KAAK,WAAW,CAAC;IACnF,MAAMsH,mBAAmB,GAAGD,gBAAgB,CAAClI,MAAM,KAAK,CAAC,IAAIkI,gBAAgB,CAAC,CAAC,CAAC,CAAC9G,EAAE,KAAKoC,OAAO,CAACtB,EAAE,CAACd,EAAE;IACrG;AACJ;AACA;AACA;IACI,IAAI+G,mBAAmB,EAAE;MACrB/C,iBAAiB,CAAC,KAAK,CAAC;MACxB9D,QAAQ,CAAC4E,IAAI,CAACpD,SAAS,CAACsF,MAAM,CAAClJ,kBAAkB,CAAC;IACtD;IACAsE,OAAO,CAACyC,SAAS,GAAG,KAAK;IACzB,IAAI;MACA;AACR;AACA;AACA;AACA;MACQG,qCAAqC,CAAC5C,OAAO,CAACtB,EAAE,CAAC;MACjD;MACAsB,OAAO,CAACtB,EAAE,CAACmG,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;MACtD9E,OAAO,CAAC+E,WAAW,CAACjC,IAAI,CAAC;QAAEpF,IAAI;QAAEC;MAAK,CAAC,CAAC;MACxC,CAAC4E,EAAE,GAAGvC,OAAO,CAACgF,oBAAoB,MAAM,IAAI,IAAIzC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAAC;QAAEpF,IAAI;QAAEC;MAAK,CAAC,CAAC;MAChG,MAAMqF,IAAI,GAAGjI,UAAU,CAACiF,OAAO,CAAC;MAChC,MAAMiD,gBAAgB,GAAGjD,OAAO,CAACiF,cAAc,GACzCjF,OAAO,CAACiF,cAAc,GACtBhK,MAAM,CAACkI,GAAG,CAACf,IAAI,EAAEY,IAAI,KAAK,KAAK,GAAGuB,iBAAiB,GAAGC,gBAAgB,CAAC;MAC7E;MACA,IAAI7G,IAAI,KAAKuH,OAAO,EAAE;QAClB,MAAM7B,gBAAgB,CAACrD,OAAO,EAAEiD,gBAAgB,EAAEjD,OAAO,CAACtB,EAAE,EAAEK,IAAI,CAAC;MACvE;MACAiB,OAAO,CAACmF,UAAU,CAACrC,IAAI,CAAC;QAAEpF,IAAI;QAAEC;MAAK,CAAC,CAAC;MACvC,CAAC6E,EAAE,GAAGxC,OAAO,CAACoF,mBAAmB,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAAC;QAAEpF,IAAI;QAAEC;MAAK,CAAC,CAAC;MAC/F;MACA;MACA,MAAM0H,UAAU,GAAGnI,gBAAgB,CAACiG,GAAG,CAACnD,OAAO,CAAC,IAAI,EAAE;MACtDqF,UAAU,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;MAC1CtI,gBAAgB,CAACuI,MAAM,CAACzF,OAAO,CAAC;MAChC;AACR;AACA;AACA;AACA;MACQA,OAAO,CAACtB,EAAE,CAACY,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC1CS,OAAO,CAACtB,EAAE,CAACmG,KAAK,CAACa,cAAc,CAAC,gBAAgB,CAAC;MACjD;AACR;AACA;AACA;MACQ,IAAI1F,OAAO,CAACtB,EAAE,CAAC+B,SAAS,KAAKC,SAAS,EAAE;QACpCV,OAAO,CAACtB,EAAE,CAAC+B,SAAS,GAAGC,SAAS;MACpC;IACJ,CAAC,CACD,OAAOiF,GAAG,EAAE;MACRxK,aAAa,CAAC,IAAI6E,OAAO,CAACtB,EAAE,CAACrB,OAAO,CAACuI,WAAW,CAAC,CAAC,MAAM,EAAED,GAAG,CAAC;IAClE;IACA3F,OAAO,CAACtB,EAAE,CAACkG,MAAM,CAAC,CAAC;IACnBiB,6BAA6B,CAAC,CAAC;IAC/B,OAAO,IAAI;EACf,CAAC;EAAA,gBA3EKpI,OAAOA,CAAAqI,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAA9B,KAAA,CAAAP,KAAA,OAAAC,SAAA;EAAA;AAAA,GA2EZ;AACD,MAAMrE,UAAU,GAAI1F,GAAG,IAAK;EACxB,OAAOA,GAAG,CAAC+B,aAAa,CAAC,SAAS,CAAC,IAAI/B,GAAG,CAACyI,IAAI;AACnD,CAAC;AACD,MAAMW,gBAAgB;EAAA,IAAAgD,KAAA,GAAArI,iBAAA,CAAG,WAAOgC,OAAO,EAAEiD,gBAAgB,EAAEqD,MAAM,EAAEvH,IAAI,EAAK;IACxE;IACAuH,MAAM,CAAChH,SAAS,CAACsF,MAAM,CAAC,gBAAgB,CAAC;IACzC,MAAM2B,OAAO,GAAGvG,OAAO,CAACtB,EAAE;IAC1B,MAAM8H,SAAS,GAAGvD,gBAAgB,CAACsD,OAAO,EAAExH,IAAI,CAAC;IACjD,IAAI,CAACiB,OAAO,CAACyG,QAAQ,IAAI,CAACxL,MAAM,CAACyL,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;MAC3DF,SAAS,CAACG,QAAQ,CAAC,CAAC,CAAC;IACzB;IACA,IAAI3G,OAAO,CAACyD,aAAa,EAAE;MACvB+C,SAAS,CAACI,cAAc,CAAC,MAAM;QAC3B,MAAM/F,aAAa,GAAGyF,MAAM,CAACO,aAAa,CAAChG,aAAa;QACxD,IAAIA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACiG,OAAO,CAAC,+BAA+B,CAAC,EAAE;UACtHjG,aAAa,CAACkG,IAAI,CAAC,CAAC;QACxB;MACJ,CAAC,CAAC;IACN;IACA,MAAMC,SAAS,GAAG9J,gBAAgB,CAACiG,GAAG,CAACnD,OAAO,CAAC,IAAI,EAAE;IACrD9C,gBAAgB,CAAC+J,GAAG,CAACjH,OAAO,EAAE,CAAC,GAAGgH,SAAS,EAAER,SAAS,CAAC,CAAC;IACxD,MAAMA,SAAS,CAACU,IAAI,CAAC,CAAC;IACtB,OAAO,IAAI;EACf,CAAC;EAAA,gBApBK7D,gBAAgBA,CAAA8D,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAAjB,KAAA,CAAAtC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoBrB;AACD,MAAMuD,WAAW,GAAGA,CAACnI,OAAO,EAAEoI,SAAS,KAAK;EACxC,IAAI1H,OAAO;EACX,MAAM2H,OAAO,GAAG,IAAI5H,OAAO,CAAE6H,CAAC,IAAM5H,OAAO,GAAG4H,CAAE,CAAC;EACjDC,SAAS,CAACvI,OAAO,EAAEoI,SAAS,EAAGI,KAAK,IAAK;IACrC9H,OAAO,CAAC8H,KAAK,CAAC5G,MAAM,CAAC;EACzB,CAAC,CAAC;EACF,OAAOyG,OAAO;AAClB,CAAC;AACD,MAAME,SAAS,GAAGA,CAACvI,OAAO,EAAEoI,SAAS,EAAEK,QAAQ,KAAK;EAChD,MAAMC,OAAO,GAAI1H,EAAE,IAAK;IACpB3F,mBAAmB,CAAC2E,OAAO,EAAEoI,SAAS,EAAEM,OAAO,CAAC;IAChDD,QAAQ,CAACzH,EAAE,CAAC;EAChB,CAAC;EACD7F,gBAAgB,CAAC6E,OAAO,EAAEoI,SAAS,EAAEM,OAAO,CAAC;AACjD,CAAC;AACD,MAAMC,QAAQ,GAAIpK,IAAI,IAAK;EACvB,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAKuD,QAAQ;AACjD,CAAC;AACD,MAAM8G,WAAW,GAAIC,CAAC,IAAKA,CAAC,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAGA,CAACJ,OAAO,EAAEK,GAAG,KAAK;EAC/B,IAAI,OAAOL,OAAO,KAAK,UAAU,EAAE;IAC/B,MAAMM,GAAG,GAAGnN,MAAM,CAACkI,GAAG,CAAC,WAAW,EAAE6E,WAAW,CAAC;IAChD,OAAOI,GAAG,CAAC,MAAM;MACb,IAAI;QACA,OAAON,OAAO,CAACK,GAAG,CAAC;MACvB,CAAC,CACD,OAAOrN,CAAC,EAAE;QACN,MAAMA,CAAC;MACX;IACJ,CAAC,CAAC;EACN;EACA,OAAO4F,SAAS;AACpB,CAAC;AACD,MAAMQ,QAAQ,GAAG,UAAU;AAC3B,MAAMgE,OAAO,GAAG,SAAS;AACzB,MAAMmD,wBAAwB,GAAG,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAIzM,GAAG,IAAK;EACtC,IAAI0M,MAAM,GAAG,KAAK;EAClB,IAAIC,eAAe;EACnB,MAAMC,YAAY,GAAGjN,YAAY,CAAC,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMkN,WAAW,GAAGA,CAACC,KAAK,GAAG,KAAK,KAAK;IACnC,IAAIH,eAAe,IAAI,CAACG,KAAK,EAAE;MAC3B,OAAO;QACHC,QAAQ,EAAEJ,eAAe;QACzBD;MACJ,CAAC;IACL;IACA,MAAM;MAAE7J,EAAE;MAAEgB,aAAa;MAAEkJ;IAAS,CAAC,GAAG/M,GAAG;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMgN,QAAQ,GAAGnK,EAAE,CAACoK,UAAU;IAC9BP,MAAM,GAAGM,QAAQ,KAAK,IAAI,IAAI,CAACnJ,aAAa;IAC5C8I,eAAe,GAAGD,MAAM,GAAGK,QAAQ,IAAIH,YAAY,GAAGG,QAAQ;IAC9D,OAAO;MAAEL,MAAM;MAAEK,QAAQ,EAAEJ;IAAgB,CAAC;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAMO,eAAe;IAAA,IAAAC,KAAA,GAAAhL,iBAAA,CAAG,WAAOiL,SAAS,EAAK;MACzC,MAAM;QAAEL;MAAS,CAAC,GAAGF,WAAW,CAAC,IAAI,CAAC;MACtC,IAAIE,QAAQ,EAAE;QACV,aAAaA,QAAQ,CAACG,eAAe,CAAClN,GAAG,CAAC6C,EAAE,EAAEuK,SAAS,CAAC;MAC5D;MACA,MAAM;QAAEvJ;MAAc,CAAC,GAAG7D,GAAG;MAC7B,IAAI6D,aAAa,IAAIuJ,SAAS,KAAKvI,SAAS,EAAE;QAC1C,MAAM,IAAIwI,KAAK,CAAC,+BAA+B,CAAC;MACpD;MACA,OAAO,IAAI;IACf,CAAC;IAAA,gBAVKH,eAAeA,CAAAI,IAAA;MAAA,OAAAH,KAAA,CAAAjF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAUpB;EACD;AACJ;AACA;EACI,MAAMoF,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,MAAM;MAAER;IAAS,CAAC,GAAGF,WAAW,CAAC,CAAC;IAClC,IAAIE,QAAQ,IAAI/M,GAAG,CAAC6C,EAAE,KAAKgC,SAAS,EAAE;MAClCkI,QAAQ,CAACQ,iBAAiB,CAACvN,GAAG,CAAC6C,EAAE,CAAC2K,aAAa,EAAExN,GAAG,CAAC6C,EAAE,CAAC;IAC5D;EACJ,CAAC;EACD,OAAO;IACHqK,eAAe;IACfK;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;EAClC,IAAIC,yBAAyB;EAC7B;AACJ;AACA;EACI,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAID,yBAAyB,EAAE;MAC3BA,yBAAyB,CAAC,CAAC;MAC3BA,yBAAyB,GAAG7I,SAAS;IACzC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM+I,gBAAgB,GAAGA,CAAC/K,EAAE,EAAEgL,OAAO,KAAK;IACtCF,mBAAmB,CAAC,CAAC;IACrB,MAAMG,SAAS,GAAGD,OAAO,KAAKhJ,SAAS,GAAG5C,QAAQ,CAAC8L,cAAc,CAACF,OAAO,CAAC,GAAG,IAAI;IACjF,IAAI,CAACC,SAAS,EAAE;MACZvO,eAAe,CAAC,IAAIsD,EAAE,CAACrB,OAAO,CAACuI,WAAW,CAAC,CAAC,sCAAsC8D,OAAO,gIAAgI,EAAEhL,EAAE,CAAC;MAC9N;IACJ;IACA,MAAMmL,2BAA2B,GAAGA,CAACC,QAAQ,EAAE5F,SAAS,KAAK;MACzD,MAAM6F,WAAW,GAAGA,CAAA,KAAM;QACtB7F,SAAS,CAAChC,OAAO,CAAC,CAAC;MACvB,CAAC;MACD4H,QAAQ,CAACvP,gBAAgB,CAAC,OAAO,EAAEwP,WAAW,CAAC;MAC/C,OAAO,MAAM;QACTD,QAAQ,CAACrP,mBAAmB,CAAC,OAAO,EAAEsP,WAAW,CAAC;MACtD,CAAC;IACL,CAAC;IACDR,yBAAyB,GAAGM,2BAA2B,CAACF,SAAS,EAAEjL,EAAE,CAAC;EAC1E,CAAC;EACD,OAAO;IACH+K,gBAAgB;IAChBD;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM5G,qCAAqC,GAAI5C,OAAO,IAAK;EACvD,IAAI/F,GAAG,KAAKyG,SAAS,EACjB;EACJ,IAAIpF,UAAU,CAAC,SAAS,CAAC,EAAE;IACvB;AACR;AACA;AACA;IACQ0E,OAAO,CAACgC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAC/C;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,uCAAuC,GAAIqH,iBAAiB,IAAK;EACnE,IAAIzH,EAAE;EACN,IAAItI,GAAG,KAAKyG,SAAS,EACjB;EACJ,MAAMgB,QAAQ,GAAGD,oBAAoB,CAACxH,GAAG,CAAC;EAC1C,KAAK,IAAIgQ,CAAC,GAAGvI,QAAQ,CAAClF,MAAM,GAAG,CAAC,EAAEyN,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,MAAMC,gBAAgB,GAAGxI,QAAQ,CAACuI,CAAC,CAAC;IACpC,MAAME,oBAAoB,GAAG,CAAC5H,EAAE,GAAGb,QAAQ,CAACuI,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI1H,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGyH,iBAAiB;IACtG;AACR;AACA;AACA;AACA;IACQ,IAAIG,oBAAoB,CAACrL,YAAY,CAAC,aAAa,CAAC,IAAIqL,oBAAoB,CAAC9M,OAAO,KAAK,WAAW,EAAE;MAClG6M,gBAAgB,CAAClI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACxD;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM6D,6BAA6B,GAAGA,CAAA,KAAM;EACxC,IAAI5L,GAAG,KAAKyG,SAAS,EACjB;EACJ,MAAMgB,QAAQ,GAAGD,oBAAoB,CAACxH,GAAG,CAAC;EAC1C,KAAK,IAAIgQ,CAAC,GAAGvI,QAAQ,CAAClF,MAAM,GAAG,CAAC,EAAEyN,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,MAAMG,cAAc,GAAG1I,QAAQ,CAACuI,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;AACA;IACQG,cAAc,CAACnI,eAAe,CAAC,aAAa,CAAC;IAC7C;AACR;AACA;AACA;AACA;IACQ,IAAImI,cAAc,CAAC/M,OAAO,KAAK,WAAW,EAAE;MACxC;IACJ;EACJ;AACJ,CAAC;AACD,MAAMkD,wBAAwB,GAAG,wBAAwB;AAEzD,SAASW,QAAQ,IAAIzF,CAAC,EAAE8E,wBAAwB,IAAI8J,CAAC,EAAEnF,OAAO,IAAIoF,CAAC,EAAEjC,wBAAwB,IAAIkC,CAAC,EAAErM,eAAe,IAAI7C,CAAC,EAAE8C,qBAAqB,IAAIqM,CAAC,EAAEjM,iBAAiB,IAAInE,CAAC,EAAEkO,wBAAwB,IAAItO,CAAC,EAAEsP,uBAAuB,IAAIxO,CAAC,EAAEoH,OAAO,IAAI5H,CAAC,EAAEmD,OAAO,IAAI/C,CAAC,EAAE6M,WAAW,IAAIU,CAAC,EAAEF,QAAQ,IAAIkC,CAAC,EAAExL,cAAc,IAAIgM,CAAC,EAAE5L,YAAY,IAAI6L,CAAC,EAAEtM,iBAAiB,IAAIpD,CAAC,EAAEqD,eAAe,IAAI7D,CAAC,EAAEoB,oBAAoB,IAAI1B,CAAC,EAAE+D,mBAAmB,IAAI/C,CAAC,EAAEoD,gBAAgB,IAAIqM,CAAC,EAAEzO,mBAAmB,IAAI0O,CAAC,EAAE1C,QAAQ,IAAI2C,CAAC,EAAErM,eAAe,IAAIsM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}