{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppComponent {\n  constructor() {}\n  static #_ = this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    standalone: false,\n    decls: 2,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ion-app\");\n        i0.ɵɵelement(1, \"ion-router-outlet\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [i1.IonApp, i1.IonRouterOutlet],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "_", "_2", "selectors", "standalone", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\pouch_db\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: 'app.component.html',\n  styleUrls: ['app.component.scss'],\n  standalone: false,\n})\nexport class AppComponent {\n  constructor() {}\n}\n", "<ion-app>\n  <ion-router-outlet></ion-router-outlet>\n</ion-app>\n"], "mappings": ";;AAQA,OAAM,MAAOA,YAAY;EACvBC,YAAA,GAAe;EAAC,QAAAC,CAAA,G;qCADLF,YAAY;EAAA;EAAA,QAAAG,EAAA,G;UAAZH,YAAY;IAAAI,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBE,EAAA,CAAAC,cAAA,cAAS;QACPD,EAAA,CAAAE,SAAA,wBAAuC;QACzCF,EAAA,CAAAG,YAAA,EAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}